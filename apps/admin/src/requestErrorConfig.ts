﻿import type { RequestOptions } from '@@/plugin-request/request';
import type { RequestConfig } from '@umijs/max';
import { history } from '@umijs/max';
import { message } from 'antd';
import { goLogin } from './pages/user/login';
import { getToken } from './services/fetcher';
// 错误处理方案： 错误类型
// enum ErrorShowType {
//   SILENT = 0,
//   WARN_MESSAGE = 1,
//   ERROR_MESSAGE = 2,
//   NOTIFICATION = 3,
//   REDIRECT = 9,
// }
// 与后端约定的响应数据格式
interface ResponseStructure {
  status: number;
  code: number;
  message: string;
  response_time: number;
  data: any;
}

/**
 * @name 错误处理
 * pro 自带的错误处理， 可以在这里做自己的改动
 * @doc https://umijs.org/docs/max/request#配置
 */
export const errorConfig: RequestConfig = {
  // 错误处理： umi@3 的错误处理方案。
  errorConfig: {
    // 错误抛出
    errorThrower: (res) => {
      const { status, code, message, data } = res as unknown as ResponseStructure;
      if (status !== 200) {
        const error: any = new Error(message);
        error.name = 'BizError';
        error.info = { code, message, data };
        throw error; // 抛出自制的错误
      }
    },
    // 错误接收及处理
    errorHandler: (error: any, opts: any) => {
      if (opts?.skipErrorHandler) throw error;
      // 我们的 errorThrower 抛出的错误。
      if (error.name === 'BizError') {
        // const errorInfo: ResponseStructure | undefined = error.info;
        // if (errorInfo) {
        //   const { message, code } = errorInfo;
        //   switch (errorInfo.showType) {
        //     case ErrorShowType.SILENT:
        //       // do nothing
        //       break;
        //     case ErrorShowType.WARN_MESSAGE:
        //       message.warning(message);
        //       break;
        //     case ErrorShowType.ERROR_MESSAGE:
        //       message.error(message);
        //       break;
        //     case ErrorShowType.NOTIFICATION:
        //       notification.open({
        //         description: message,
        //         message: code,
        //       });
        //       break;
        //     case ErrorShowType.REDIRECT:
        //       // TODO: redirect
        //       break;
        //     default:
        //       message.error(message);
        //   }
        // }
      } else if (error.response) {
        console.log(` error`, error, error.response.status);
        // Axios 的错误
        // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
        message.error(`服务器错误，请稍后再试！`);
        // throw new Error(error.response.data.message);
      } else if (error.request) {
        // 请求已经成功发起，但没有收到响应
        // \`error.request\` 在浏览器中是 XMLHttpRequest 的实例，
        // 而在node.js中是 http.ClientRequest 的实例
        message.error('无响应！请重试。');
      } else {
        // 发送请求时出了点问题
        message.error('请求失败，请稍后再试！');
      }
    },
  },

  // 请求拦截器
  requestInterceptors: [
    (config: RequestOptions) => {
      // 拦截请求配置，进行个性化处理。
      const url = config?.url;
      return {
        ...config,
        url,
        headers: {
          ...config.headers,
          Authorization: getToken(),
        },
      };
    },
  ],

  // 响应拦截器
  responseInterceptors: [
    (response) => {
      // 拦截响应数据，进行个性化处理
      const { data, status } = response as unknown as ResponseStructure;
      // console.log(`responseInterceptors`, data, status, response);

      switch (data.status) {
        // case 200:
        //   return response;
        // case 400:
        //   message.error(data?.message + ':' + response.config.url);
        //   break;
        case 401: {
          goLogin();
          // 如果在登录页面则不提示
          if (!window.location.pathname.includes('/user/login')) {
            message.error(data?.message);
          }
          // throw new Error(data?.message);
        }
        case 403:
          message.error(data?.message);
          break;
        case 404:
          message.error(data?.message);
          break;
        case 500:
          history.push('/exception/500');
          message.error(data?.message);
          throw new Error(data?.message);
        default:
          // message.error('请求失败！');
          break;
      }
      return response;
    },
  ],
};
