// 合作类型枚举
export enum CooperationType {
  // 试用相关
  TRIAL = '1', // 开通试用
  TRIAL_EXTENSION = '2', // 延长试用

  // 付费相关
  PAYMENT = '3', // 开通付费

  // 状态变更
  DISABLE = '4', // 停用
}

// 试用政策枚举
export enum TrialPolicy {
  POLICY_1 = 'policy1',
  POLICY_2 = 'policy2',
  POLICY_3 = 'policy3',
}

// 付费政策枚举
export enum PaymentPolicy {
  POLICY_1 = 'policy1',
  POLICY_2 = 'policy2',
  POLICY_3 = 'policy3',
}

// 学生选择相关接口
export interface Grade {
  id: string;
  name: string;
}

export interface Class {
  id: string;
  name: string;
  gradeId: string;
}

export interface Student {
  id: string;
  name: string;
  classId: string;
}

// 合作申请表单数据接口
export interface CooperationFormData {
  cooperationType: CooperationType;
  startTime: string;
  endTime: string;
  dateRange: string[];
  duration: number;
  studentType: string;
  studentSelection: number[];
  remark?: string;
  selectedStudents?: string[];
  studentCount?: number;
}
