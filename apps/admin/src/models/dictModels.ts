// src/models/useDict.ts
import {
  DictOption,
  DictTypeEnum,
  DictTypeValue,
  getDictByType,
  transformToDictOption,
} from '@/services/dict';
import { ProSchemaValueEnumObj } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import { useCallback, useEffect, useState } from 'react';

type DictCache = Record<DictTypeValue, DictOption[]>;

interface LoadingFlags {
  [key: string]: boolean;
}
const initialDictCache: DictCache = {
  [DictTypeEnum.PHASE]: [],
  [DictTypeEnum.EDU_SYSTEM]: [],
  [DictTypeEnum.SCHOOL_FEATURE]: [],
  [DictTypeEnum.SCHOOL_PROPERTY]: [],
  [DictTypeEnum.SCHOOL_CP]: [],
  [DictTypeEnum.SUBJECT]: [],
};

let initializationPromise: Promise<void> | null = null;

export default () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [dictCache, setDictCache] = useState<DictCache>(initialDictCache);
  const [loadingFlags] = useState<LoadingFlags>({});

  // Load multiple dictionaries at once
  const loadDictionaries = async (dictTypes?: DictTypeValue[]) => {
    // Filter out already cached types
    const uncachedTypes = dictTypes?.filter((type) => !dictCache[type]);

    try {
      const response = await getDictByType(uncachedTypes);
      // console.log('获取字典数据', response);

      if (response.status === 200) {
        const dictData = response.data;
        setDictCache((prevCache) => {
          const newCache = { ...prevCache };
          Object.entries(dictData).forEach(([type, items]) => {
            if (type in newCache) {
              newCache[type as DictTypeValue] = items?.children?.map(transformToDictOption) || [];
            }
          });
          return newCache;
        });
      }
    } catch (error) {
      message.error('获取字典数据失败');
      console.error('Failed to load dictionaries:', error);
    }
  };

  // Initialize all dictionaries on mount
  useEffect(() => {
    // console.log('初始化字典数据', { dictCache, initializationPromise, currentUser });

    if (!initializationPromise && currentUser) {
      initializationPromise = loadDictionaries();
    }
  }, [currentUser]);

  // Get dictionary by type
  const getDict = useCallback(
    async (dictType: DictTypeValue): Promise<DictOption[]> => {
      if (initializationPromise) {
        await initializationPromise;
      }

      if (dictCache[dictType]) {
        return dictCache[dictType];
      }

      if (loadingFlags[dictType]) {
        return new Promise((resolve) => {
          const timer = setInterval(() => {
            if (dictCache[dictType]) {
              clearInterval(timer);
              resolve(dictCache[dictType]);
            }
          }, 100);
        });
      }

      loadingFlags[dictType] = true;
      try {
        await loadDictionaries([dictType]);
        return dictCache[dictType] || [];
      } finally {
        loadingFlags[dictType] = false;
      }
    },
    [dictCache],
  );

  const getDictValueEnum = (dictType: DictTypeValue) => {
    const options = dictCache[dictType];
    let result: ProSchemaValueEnumObj = {};
    options.forEach((item) => {
      result[item.value] = {
        text: item.label,
      };
    });
    // console.log('transformValueEnumresult', options, result);

    return result;
  };

  // Clear cache
  const clearCache = useCallback((dictType?: DictTypeValue) => {
    if (dictType) {
      setDictCache((prevCache) => {
        const newCache = { ...prevCache };
        delete newCache[dictType];
        return newCache;
      });
    } else {
      setDictCache(initialDictCache);
      initializationPromise = null;
    }
  }, []);

  return {
    dictCache,
    getDict,
    clearCache,
    getDictValueEnum,
  };
};
