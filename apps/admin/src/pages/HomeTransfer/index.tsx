import { history, useModel } from '@umijs/max';
import { Spin } from 'antd';
import { useEffect } from 'react';

export default function HomeTransfer() {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const menus = currentUser?.menus || [];
  useEffect(() => {
    const token = localStorage.getItem('token');
    // console.log(`HomeTransfer useEffect`, { menus, token });

    if (!token) {
      // 这里必须使用 window.location.href 而不是 history.push，会有 bug
      window.location.href = '/user/login';
      return;
    }
    if (!menus) return;
    const hasPartnerSchoolList = menus.some((menu) => menu.menuPath === '/partner-school/list');

    // menus 中是否有/partner-school/list
    if (hasPartnerSchoolList) {
      history.push('/partner-school/list');
    } else {
      history.push('/my-partner-school/list');
    }
  }, [menus]);
  return (
    <div className="flex justify-center items-center h-screen">
      <Spin />
    </div>
  );
}
