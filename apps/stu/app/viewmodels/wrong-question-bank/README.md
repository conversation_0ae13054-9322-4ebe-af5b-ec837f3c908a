# 错题本 ViewModel 层

错题本 ViewModel 层负责处理错题本相关的业务逻辑和状态管理，为 View 层提供简洁易用的接口。

## 🎯 核心功能

- **错题切换** - 统一处理题目加入/移出错题本的逻辑
- **状态管理** - 管理加载状态和错误状态
- **错误处理** - 统一的错误处理机制

## 📋 可用 Hooks

| Hook 名称 | 描述 | 主要接口 | 依赖 Model |
|-----------|------|----------|------------|
| `useToggleWrongQuestion` | 切换错题本状态 | `toggle, isLoading, error` | `useManualAddWrongQuestion`, `useDeleteWrongQuestions` |

## 🚀 使用示例

### 基础用法

```typescript
import { useToggleWrongQuestion } from '@/viewmodels/wrong-question-bank';

function QuestionCard({ questionId, isInWrongBank, wrongQuestionId }: {
  questionId: number;
  isInWrongBank: boolean;
  wrongQuestionId?: number;
}) {
  const { toggle, isLoading, error } = useToggleWrongQuestion();
  const studentId = 1001; // 从用户上下文获取

  const handleToggle = async () => {
    try {
      if (isInWrongBank && wrongQuestionId) {
        // 从错题本移除
        await toggle({
          action: 'remove',
          studentId,
          wrongQuestionId,
        });
      } else {
        // 添加到错题本
        await toggle({
          action: 'add',
          studentId,
          questionId,
          errorReasonTags: ['需要复习'], // 默认错因标签
          notes: '手动添加到错题本',
        });
      }
      // 成功后可以刷新题目状态或显示成功提示
    } catch (error) {
      console.error('操作失败:', error);
      // 显示错误提示
    }
  };

  return (
    <div className="question-card">
      <button 
        className="toggle-wrong-question-btn"
        onClick={handleToggle} 
        disabled={isLoading}
      >
        {isLoading ? '处理中...' : (isInWrongBank ? '移出错题本' : '加入错题本')}
      </button>
      {error && (
        <div className="error-message">
          操作失败: {error.message}
        </div>
      )}
    </div>
  );
}
```

### 带错因标签选择的用法

```typescript
import { useToggleWrongQuestion } from '@/viewmodels/wrong-question-bank';
import { useState } from 'react';

function QuestionWithErrorTags({ questionId }: { questionId: number }) {
  const { toggle, isLoading, error } = useToggleWrongQuestion();
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [notes, setNotes] = useState('');
  const studentId = 1001;

  const errorTagOptions = [
    '计算错误',
    '概念理解不清',
    '审题不仔细',
    '方法选择错误',
    '需要复习'
  ];

  const handleAddToWrongBank = async () => {
    if (selectedTags.length === 0) {
      alert('请至少选择一个错因标签');
      return;
    }

    try {
      await toggle({
        action: 'add',
        studentId,
        questionId,
        errorReasonTags: selectedTags,
        notes,
      });
      // 重置表单
      setSelectedTags([]);
      setNotes('');
    } catch (error) {
      console.error('添加失败:', error);
    }
  };

  return (
    <div className="question-error-form">
      <div className="error-tags-section">
        <h4>选择错因标签：</h4>
        {errorTagOptions.map(tag => (
          <label key={tag} className="error-tag-option">
            <input
              type="checkbox"
              checked={selectedTags.includes(tag)}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedTags([...selectedTags, tag]);
                } else {
                  setSelectedTags(selectedTags.filter(t => t !== tag));
                }
              }}
            />
            {tag}
          </label>
        ))}
      </div>
      
      <div className="notes-section">
        <label>
          备注：
          <textarea
            className="notes-input"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="可选：添加学习备注"
          />
        </label>
      </div>

      <button 
        className="add-to-wrong-bank-btn"
        onClick={handleAddToWrongBank}
        disabled={isLoading || selectedTags.length === 0}
      >
        {isLoading ? '添加中...' : '加入错题本'}
      </button>

      {error && (
        <div className="error-message">
          {error.message}
        </div>
      )}
    </div>
  );
}
```

## 📊 类型定义

### ToggleActionParams

```typescript
export type ToggleActionParams =
  | {
      action: 'add';
      studentId: number;
      questionId: number;
      errorReasonTags: string[];
      notes?: string;
    }
  | {
      action: 'remove';
      studentId: number;
      wrongQuestionId: number;
    };
```

### UseToggleWrongQuestionReturn

```typescript
interface UseToggleWrongQuestionReturn {
  toggle: (params: ToggleActionParams) => Promise<void>;
  isLoading: boolean;
  error: Error | null;
}
```

## 🔧 设计原则

1. **统一接口** - 通过 `toggle` 函数统一处理添加和删除操作
2. **类型安全** - 使用 TypeScript 联合类型确保参数正确性
3. **错误处理** - 统一的错误状态管理和错误重抛机制
4. **状态管理** - 清晰的加载状态，避免重复操作
5. **依赖注入** - 依赖 Model 层的具体实现，保持层次分离

## ⚠️ 注意事项

1. **学生ID必需** - 所有操作都需要提供有效的学生ID
2. **错因标签必需** - 添加错题时必须提供至少一个错因标签
3. **错误重抛** - Hook 会重新抛出错误，调用方需要处理异常
4. **状态重置** - 每次调用 `toggle` 时会重置错误状态
5. **异步操作** - `toggle` 函数是异步的，需要使用 await 或 .then() 处理

## 🧪 测试建议

- 测试添加和删除操作的成功场景
- 测试参数验证和错误处理
- 测试加载状态的正确更新
- 测试错误状态的管理和重置
- 模拟 Model 层的各种响应情况 