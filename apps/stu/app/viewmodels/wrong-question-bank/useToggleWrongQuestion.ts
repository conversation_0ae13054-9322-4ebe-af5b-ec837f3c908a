import { useState, useCallback } from 'react';
import {
  useManualAddWrongQuestion,
  useDeleteWrongQuestions,
} from '../../models/wrong-question-bank';

interface UseToggleWrongQuestionReturn {
  toggle: (params: ToggleActionParams) => Promise<void>;
  isLoading: boolean;
  error: Error | null;
}

export type ToggleActionParams =
  | {
      action: 'add';
      studentId: number;
      questionId: number;
      errorReasonTags: string[];
      notes?: string;
    }
  | {
      action: 'remove';
      studentId: number;
      wrongQuestionId: number;
    };

/**
 * 错题本切换 Hook
 * 提供统一的接口来添加或删除错题
 */
export function useToggleWrongQuestion(): UseToggleWrongQuestionReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const { manualAddWrongQuestion } = useManualAddWrongQuestion();
  const { deleteWrongQuestions } = useDeleteWrongQuestions();

  const toggle = useCallback(
    async (params: ToggleActionParams) => {
      setIsLoading(true);
      setError(null);
      
      try {
        if (params.action === 'add') {
          await manualAddWrongQuestion({
            studentId: params.studentId,
            questionId: params.questionId,
            errorReasonTags: params.errorReasonTags,
            notes: params.notes,
          });
        } else {
          // params.action === 'remove'
          await deleteWrongQuestions({
            studentId: params.studentId,
            wrongQuestionIds: String(params.wrongQuestionId),
          });
        }
      } catch (e) {
        const errorObj = e as Error;
        setError(errorObj);
        throw errorObj; // 重新抛出错误，让调用方也能处理
      } finally {
        setIsLoading(false);
      }
    },
    [manualAddWrongQuestion, deleteWrongQuestions]
  );

  return {
    toggle,
    isLoading,
    error,
  };
} 