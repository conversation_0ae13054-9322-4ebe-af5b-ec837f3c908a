# Exercise ViewModels 层

## 📋 概述

Exercise ViewModels 层是 MVVM 架构中的视图模型层，负责封装业务逻辑、状态管理和数据转换。该层作为View层和Model层之间的桥梁，确保View层保持纯净，只负责UI渲染。

## 🏗️ 架构设计

### MVVM分层架构

```
View Layer (views/exercise/) 
    ↓ 调用Context
Context Layer (contexts/question-context.tsx)
    ↓ 集成ViewModel
ViewModel Layer (viewmodels/exercise/) ← 当前层
    ↓ 调用Model
Model Layer (models/exercise/)
```

### 核心原则

- **业务逻辑封装**：所有复杂的业务逻辑都封装在ViewModel中
- **状态管理**：管理组件状态，避免View层直接操作状态
- **数据转换**：处理Model层数据与View层展示需求之间的转换
- **错误处理**：统一的错误处理和用户反馈机制

## 📁 文件结构

```
viewmodels/exercise/
├── README.md                    # 当前文档
├── question-viewmodel.ts        # 题目业务逻辑ViewModel
├── transition-viewmodel.ts      # 转场动画ViewModel
├── exit-viewmodel.ts           # 退出功能ViewModel (新增)
└── choice-question-viewmodel.ts # 选择题逻辑ViewModel
```

## 🔧 核心ViewModels

### 1. ExitViewModel (新增)
**职责**：退出功能的业务逻辑封装

**核心功能**：
- 管理退出确认弹窗状态
- 处理不同学习类型的退出逻辑差异
- 调用Model层的退出会话API
- 提供统一的退出接口给View层

**架构优势**：
```typescript
// ❌ 之前：View层直接调用Model
function ExerciseView() {
  const { exitSession, isExiting } = useExitStudySession(); // 直接调用Model
  
  const handleBack = async () => {
    if (studyType !== StudyType.AI_COURSE) {
      await exitSession({ studySessionId }); // View层处理业务逻辑
    }
    onBack();
  };
}

// ✅ 现在：View层通过Context调用ViewModel
function ExerciseView() {
  const { exitState, handleExitRequest } = useQuestionContext(); // 通过Context获取
  
  const handleBack = () => {
    handleExitRequest(studyType, studySessionId); // 委托给ViewModel处理
  };
}
```

**状态管理**：
```typescript
export interface ExitViewModelState {
  showExitConfirm: boolean;  // 弹窗显示状态
  isExiting: boolean;        // 退出处理状态
  exitError: Error | null;   // 错误状态
}

export interface ExitViewModelActions {
  handleExitRequest: (studyType: StudyType, onExitCallback?: () => void) => void;
  handleConfirmExit: (studySessionId: number, onExitCallback?: () => void) => Promise<void>;
  handleCancelExit: () => void;
}
```

### 2. QuestionViewModel
**职责**：题目相关的核心业务逻辑

**功能**：
- 题目数据获取和转换
- 答题状态管理
- 提交答案处理
- 进度计算

### 3. TransitionViewModel
**职责**：转场动画和反馈管理

**功能**：
- 答题反馈动画
- 转场序列控制
- 特殊反馈处理

## 🔄 集成到Context

### Context层集成

```typescript
export function QuestionContextProvider({ children, config }: QuestionContextProviderProps) {
  // 集成各个ViewModel
  const viewModelData = useQuestionViewModel(config);
  const transitionViewModel = useTransitionViewModel();
  const exitViewModel = useExitViewModel(); // 新增

  // 封装退出方法，加入业务逻辑
  const handleExitRequest = useCallback((studyType: StudyType, studySessionId: number) => {
    exitViewModel.handleExitRequest(studyType, undefined);
  }, [exitViewModel]);

  // 合并所有ViewModel数据
  const contextValue: QuestionContextValue = {
    ...viewModelData,
    // 转场管理
    transitionState: transitionViewModel.transitionState,
    // 退出管理 (新增)
    exitState: {
      showExitConfirm: exitViewModel.showExitConfirm,
      isExiting: exitViewModel.isExiting,
      exitError: exitViewModel.exitError,
    },
    handleExitRequest,
    handleConfirmExit,
    handleCancelExit,
  };
}
```

## 🎯 架构优势

### 1. 职责分离
```
View层：     只负责UI渲染和用户交互事件
ViewModel层：封装业务逻辑、状态管理、错误处理
Model层：    纯数据获取和API调用
```

### 2. 代码可维护性
- **集中管理**：退出逻辑集中在ExitViewModel中
- **易于测试**：ViewModel可以独立测试业务逻辑
- **复用性强**：其他组件可以复用退出ViewModel

### 3. 类型安全
```typescript
// 完整的类型定义
export type ExitViewModel = ExitViewModelState & ExitViewModelActions;

// Context提供完整类型推导
const { exitState, handleExitRequest } = useQuestionContext();
//      ↑ 完整的类型提示
```

### 4. 错误处理统一化
```typescript
// ViewModel层统一错误处理
const handleConfirmExit = useCallback(async (studySessionId: number, onExitCallback?: () => void) => {
  try {
    if (studySessionId > 0) {
      await exitSession({ studySessionId });
    }
    // 成功处理
  } catch (error) {
    setExitError(error instanceof Error ? error : new Error('退出会话失败'));
    // 即使失败也确保用户能退出
    if (onExitCallback) onExitCallback();
  }
}, [exitSession]);
```

## 🚀 架构优化历程

### ExitViewModel的调用方式优化 (2025-01)

**阶段一：通过Context包装**
```
View → Context → ExitViewModel → Model
```
Context充当中间层，包装ExitViewModel的方法。

**阶段二：直接调用（当前推荐）**
```
View → ExitViewModel → Model
```

**优化原因**：
- ✅ **无状态耦合**：ExitViewModel与Context中其他状态完全独立
- ✅ **代码简化**：去掉Context层的冗余包装代码
- ✅ **性能提升**：减少不必要的Context传递和重渲染
- ✅ **架构清晰**：直接调用更符合单一职责原则

**设计原则**：
- **无耦合即可直调**：独立的ViewModel可以被View直接调用
- **有耦合走Context**：需要与其他状态交互的才通过Context统一管理

## 📊 性能优化

### 1. 状态优化
- 使用`useCallback`优化方法引用稳定性
- 避免不必要的重渲染

### 2. 内存管理
- 及时清理错误状态
- 避免内存泄漏

## 🔗 相关文档
- [Context层说明](../../contexts/README.md)
- [View层说明](../../views/exercise/README.md)
- [Model层说明](../../models/exercise/README.md)