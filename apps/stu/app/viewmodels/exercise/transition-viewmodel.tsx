"use client";

import { useSignal } from "@preact-signals/safe-react";
import type {
  NextQuestionGroupInfo,
  SpecialFeedback,
} from "@repo/core/exercise/model/types";
import { useCallback, useRef, useState } from "react";

/**
 * 转场播放项接口
 */
export interface TransitionItem {
  id: string;
  type: "group" | "special";
  data: NextQuestionGroupInfo | SpecialFeedback;
  duration: number;
}

/**
 * 转场播放队列接口
 */
interface TransitionQueue {
  items: TransitionItem[];
  currentIndex: number;
  totalDuration: number;
}

/**
 * 转场动画状态接口
 */
interface TransitionState {
  // 播放队列
  queue: TransitionQueue;

  // 当前播放的转场项
  currentTransition: TransitionItem | null;

  // 全局转场状态
  isPlayingTransitions: boolean;
}

type ExecuteTransitionSequenceParams = {
  groupInfo?: NextQuestionGroupInfo;
  specialFeedbacks?: SpecialFeedback[];
};

/**
 * 转场ViewModel - 统一管理所有转场逻辑
 */
export const useTransitionViewModel = () => {
  // 🔥 统一的转场状态管理
  const [transitionState, setTransitionState] = useState<TransitionState>({
    queue: {
      items: [],
      currentIndex: -1,
      totalDuration: 0,
    },
    currentTransition: null,
    isPlayingTransitions: false,
  });

  // 🔥 跟踪当前题组索引，用于判断是否需要播放题组转场
  const currentGroupIndex = useSignal(-1);

  // 🔥 构建转场播放队列
  const buildTransitionQueue = useCallback(
    (
      groupInfo?: NextQuestionGroupInfo,
      specialFeedbacks?: SpecialFeedback[]
    ): TransitionItem[] => {
      const items: TransitionItem[] = [];
      // 用户是否退出之前播放过转场了，恢复的时候应该不要播放，比如本地记录的是 0，后端返回的大于 1
      const shouldSkipPlay =
        currentGroupIndex.value === 0 && (groupInfo?.groupIndex || 0) > 1;

      // 🔥 题组转场优先播放 - 只有当groupIndex发生变化时才播放
      if (
        groupInfo?.groupName &&
        !isNaN(groupInfo?.groupIndex) &&
        groupInfo.groupIndex !== currentGroupIndex.value &&
        !shouldSkipPlay
      ) {
        console.log("题组转场检查:", {
          groupInfo,
          newGroupIndex: groupInfo.groupIndex,
          currentGroupIndex,
          shouldPlay: groupInfo.groupIndex !== currentGroupIndex.value,
        });

        items.push({
          id: `group-${groupInfo.groupIndex}-${groupInfo.groupName}`,
          type: "group",
          data: groupInfo,
          duration: 2200, // 题组转场固定2.5秒
        });
      }

      // 特殊反馈按顺序添加
      if (specialFeedbacks && specialFeedbacks.length > 0) {
        specialFeedbacks.forEach((feedback, index) => {
          items.push({
            id: `special-${feedback.type}-${index}`,
            type: "special",
            data: feedback,
            duration: 2000, // 特殊反馈固定3秒
          });
        });
      }

      return items;
    },
    [currentGroupIndex]
  );

  // 🔥 推进到下一个转场
  const nextTransition = useCallback(() => {
    setTransitionState((prev) => {
      const { queue } = prev;
      const nextIndex = queue.currentIndex + 1;

      if (nextIndex >= queue.items.length) {
        // 播放完成，清空队列
        return {
          ...prev,
          queue: { items: [], currentIndex: -1, totalDuration: 0 },
          currentTransition: null,
          isPlayingTransitions: false,
        };
      }

      // 播放下一个转场
      const nextItem = queue.items[nextIndex] || null;
      return {
        ...prev,
        queue: { ...queue, currentIndex: nextIndex },
        currentTransition: nextItem,
        isPlayingTransitions: true,
      };
    });
  }, []);

  // 已处理转场记录
  const processedTransitionsRef = useRef<Set<string>>(new Set());

  // 🔥 执行完整转场序列
  const executeTransitionSequence = useCallback(
    async ({
      groupInfo,
      specialFeedbacks,
    }: ExecuteTransitionSequenceParams): Promise<void> => {
      return new Promise<void>(async (resolve, reject) => {
        // 构建转场队列
        const transitionItems = buildTransitionQueue(
          groupInfo,
          specialFeedbacks
        );

        // 如果没有转场需要播放
        if (transitionItems.length === 0) {
          resolve();
          return; // 表示无转场，应直接切换题目
        }
        // 🔥 更新当前题组索引和上一个题组名称
        currentGroupIndex.value =
          groupInfo?.groupIndex ?? currentGroupIndex.value;
        console.log("题组转场检查1:", {
          groupInfo,
          currentGroupIndex,
          groupIndex: groupInfo?.groupIndex,
        });

        // 生成唯一的转场标识符
        const transitionKey = transitionItems.map((item) => item.id).join("-");

        // 检查是否已处理过相同的转场
        if (processedTransitionsRef.current.has(transitionKey)) {
          resolve();
          return; // 表示已处理，应直接切换题目
        }

        // 计算总播放时长
        const totalDuration = transitionItems.reduce(
          (sum, item) => sum + item.duration,
          0
        );

        // 设置转场队列并开始播放
        setTransitionState((prev) => ({
          ...prev,
          queue: {
            items: transitionItems,
            currentIndex: -1, // 从-1开始，nextTransition会推进到0
            totalDuration,
          },
          isPlayingTransitions: true,
        }));

        // 标记为已处理
        processedTransitionsRef.current.add(transitionKey);

        // 开始播放第一个转场
        nextTransition();

        resolve();
        return;
      });
    },
    [buildTransitionQueue, nextTransition]
  );

  // 🔥 清理转场记录 (题目切换时调用)
  const clearTransitionRecords = useCallback(() => {
    processedTransitionsRef.current.clear();
  }, []);

  // 🔥 重置转场系统 (完全重新开始时调用)
  const resetTransitionSystem = useCallback(() => {
    processedTransitionsRef.current.clear();
    currentGroupIndex.value = -1; // 重置题组索引
    setTransitionState({
      queue: { items: [], currentIndex: -1, totalDuration: 0 },
      currentTransition: null,
      isPlayingTransitions: false,
    });
  }, []);

  return {
    // 状态
    transitionState,

    // 转场序列操作
    executeTransitionSequence,
    clearTransitionRecords,
    resetTransitionSystem,

    // 播放控制
    nextTransition,
  };
};
