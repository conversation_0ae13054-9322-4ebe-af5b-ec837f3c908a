"use client";

import { useState, useCallback } from "react";
import { QUESTION_TYPE, StudyType } from "@repo/core/enums";
import type { UserAnswerData, QuestionState } from "../../../types/app/exercise";
import type { NextQuestionInfo } from "@repo/core/exercise/model";
import { isSelfEvaluationQuestionType } from "@repo/core/exercise/utils/question/question-viewmodel-utils";
import { useSearchParams } from "next/navigation";
import { ConfirmDialogConfig } from "@/app/views/exercise/components/exercise-dialog";

/**
 * 确认对话框类型
 */
export type ConfirmationType =
  | "multipleChoiceConfirm"           // 多选题只选一个确认
  | "subjectiveEmpty"                 // 主观题空白确认
  | "subjectivePartEmpty"             // 主观题部分空白确认
  | "subjectivePhotoEmpty";           // 主观题图片空白确认


/**
 * 提交前校验结果
 */
interface BeforeSubmitValidationResult {
  canDirectSubmit: boolean;
  needsConfirmation: boolean;
  confirmationType?: ConfirmationType;
  title?: string;
  message?: string;
  needsSpecialHandling: boolean;
  specialHandlingType?: "fillBlankDialog";
}

/**
 * 题目提交ViewModel
 * 
 * 统一管理所有题型的提交逻辑：
 * 1. 多选题确认逻辑
 * 2. 主观题空白确认逻辑（合并填空题、问答题）
 * 3. 特殊处理（如填空题组件内对话框）
 */
export function useQuestionSubmissionViewModel() {
  // 确认对话框状态
  const [confirmationDialog, setConfirmationDialog] = useState<ConfirmDialogConfig>({
    isVisible: false,
  });
  const urlParams = useSearchParams();
  const studyType = (urlParams.get("studyType") as unknown as StudyType) || StudyType.AI_COURSE;
  /**
   * 提交前校验
   */
  const beforeSubmitValidation = useCallback((
    question: NextQuestionInfo,
    userAnswerData: UserAnswerData,
    _questionState: QuestionState, // 预留参数，用于未来可能的状态相关校验
    additionalContext?: {
      subjectiveAnswer?: string[];
      imageFiles?: File[];
      inputMode?: "keyboard" | "camera";
    }
  ): BeforeSubmitValidationResult => {
    const questionType = question.questionType;

    // 基础结果
    const result: BeforeSubmitValidationResult = {
      canDirectSubmit: true,
      needsConfirmation: false,
      needsSpecialHandling: false,
    };

    switch (questionType) {
      case QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE:
        return beforeMultipleChoiceSubmit(userAnswerData, result);

      case QUESTION_TYPE.QUESTION_TYPE_QA:
      case QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK:

        // 问答题作为主观题处理
        return beforeSubjectiveSubmit(userAnswerData, additionalContext, result);

      case QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE:
      case QUESTION_TYPE.QUESTION_TYPE_JUDGMENT:
        // 客观题，直接提交
        return result;

      default:
        return result;
    }
  }, []);

  /**
   * 多选题提交前校验
   */
  const beforeMultipleChoiceSubmit = (
    userAnswerData: UserAnswerData,
    result: BeforeSubmitValidationResult
  ): BeforeSubmitValidationResult => {
    const selectedCount = userAnswerData.choiceAnswers?.length || 0;

    if (selectedCount === 1) {
      return {
        ...result,
        canDirectSubmit: false,
        needsConfirmation: true,
        confirmationType: "multipleChoiceConfirm",
        title: "多选题答案有多个",
        message: undefined, // 多选题不需要额外消息
      };
    }

    return result;
  };

  /**
   * 主观题提交前校验
   */
  const beforeSubjectiveSubmit = (
    userAnswerData: UserAnswerData,
    additionalContext: {
      subjectiveAnswer?: string[];
      imageFiles?: File[];
      inputMode?: "keyboard" | "camera";
    } | undefined,
    result: BeforeSubmitValidationResult
  ): BeforeSubmitValidationResult => {
    const inputMode = additionalContext?.inputMode || userAnswerData.inputMode || "keyboard";

    if (inputMode === "camera") {
      // 拍照模式
      const imageFiles = additionalContext?.imageFiles || (userAnswerData.imgFiles as string[]) || [];
      if (imageFiles.length === 0) {
        // 对应 fillBlankViewModel.submitDialogType === "photoEmpty"
        return {
          ...result,
          canDirectSubmit: false,
          needsConfirmation: true,
          confirmationType: "subjectivePhotoEmpty",
          title: "本题未作答，确认提交吗？",
          message: undefined, // 保持原有简洁文案
        };
      }
    } else {
      // 键盘模式 - 统一按答案数量处理，不区分问答题和填空题
      const answers = additionalContext?.subjectiveAnswer || userAnswerData.subjectiveAnswer || [];
      const emptyCount = answers.filter((answer: string) => !answer?.trim()).length;
      const totalCount = answers.length;

      if (emptyCount === totalCount) {
        // 一个都没有答（全部为空）
        return {
          ...result,
          canDirectSubmit: false,
          needsConfirmation: true,
          confirmationType: "subjectiveEmpty",
          title: "本题未作答，确认提交吗？",
          message: undefined,
        };
      } else if (emptyCount > 0) {
        // 答案数量不够（还有填空未作答）
        return {
          ...result,
          canDirectSubmit: false,
          needsConfirmation: true,
          confirmationType: "subjectivePartEmpty",
          title: "还有填空未作答，确认提交吗？",
          message: undefined,
        };
      }
    }

    return result;
  };

  /**
   * 处理提交按钮点击
   */
  const beforeSubmitCheck = useCallback((
    params: {
      question: NextQuestionInfo,
      userAnswerData: UserAnswerData,
      questionState: QuestionState,
      actualSubmitHandler: (data: UserAnswerData) => Promise<void>,
      additionalContext?: {
        subjectiveAnswer?: string[];
        imageFiles?: File[];
        inputMode?: "keyboard" | "camera";
      }
    }) => {
    const { question, userAnswerData, questionState, actualSubmitHandler, additionalContext } = params;
    // 提交前校验
    const strategy = beforeSubmitValidation(
      question,
      userAnswerData,
      questionState,
      additionalContext
    );

    if (strategy.canDirectSubmit) {
      // 可以直接提交
      actualSubmitHandler(userAnswerData);
    } else if (strategy.needsConfirmation) {
      // 需要用户确认
      const isSpecialType = strategy.confirmationType && [
        "multipleChoiceConfirm",
        "subjectiveEmpty",
        "subjectivePartEmpty",
        "subjectivePhotoEmpty"
      ].includes(strategy.confirmationType);

      if (isSpecialType) {
        // 特殊类型：按钮逻辑相反
        // "我再想想" (confirmText) → 关闭弹窗 (onConfirm)
        // "继续提交" (cancelText) → 提交当前作答 (onCancel)
        setConfirmationDialog({
          isVisible: true,
          type: strategy.confirmationType,
          title: strategy.title,
          message: strategy.message,
          studyType: studyType,
          onClose: () => {
            setConfirmationDialog({ isVisible: false });
          },
          onConfirm: () => {
            // "我再想想" - 只关闭弹窗
            setConfirmationDialog({ isVisible: false });
          },
          onCancel: () => {
            // "继续提交" - 提交当前作答
            setConfirmationDialog({ isVisible: false });
            actualSubmitHandler(userAnswerData);
          }
        });
      } else {
        // 普通类型：正常逻辑
        setConfirmationDialog({
          isVisible: true,
          type: strategy.confirmationType,
          title: strategy.title,
          message: strategy.message,
          studyType: studyType,
          onClose: () => {
            setConfirmationDialog({ isVisible: false });
          },
          onConfirm: () => {
            setConfirmationDialog({ isVisible: false });
            actualSubmitHandler(userAnswerData);
          },
          onCancel: () => {
            setConfirmationDialog({ isVisible: false });
          }
        });
      }
    } else if (strategy.needsSpecialHandling) {
      // 需要特殊处理
      // 这种情况下返回特殊处理标识，让调用方处理
      return { needsSpecialHandling: true, specialHandlingType: strategy.specialHandlingType };
    }

    return { needsSpecialHandling: false };
  }, [beforeSubmitValidation]);

  /**
   * 关闭确认对话框
   */
  const closeConfirmationDialog = useCallback(() => {
    setConfirmationDialog({ isVisible: false });
  }, []);

  /**
   * 确认提交
   */
  const confirmSubmit = useCallback(() => {
    if (confirmationDialog.onConfirm) {
      confirmationDialog.onConfirm();
    }
  }, [confirmationDialog]);

  /**
   * 取消提交
   */
  const cancelSubmit = useCallback(() => {
    if (confirmationDialog.onCancel) {
      confirmationDialog.onCancel();
    }
  }, [confirmationDialog]);

  /**
   * 检查是否可以提交（用于按钮禁用状态）
   */
  const canSubmit = useCallback((
    question: NextQuestionInfo | null,
    userAnswerData: UserAnswerData,
    questionState?: QuestionState
  ): boolean => {
    if (!question) {
      return false;
    }

    // 🆕 在awaiting_self_evaluation状态下，需要在QuestionView中单独处理
    if (questionState === "awaiting_self_evaluation") {
      // 这个状态的检查逻辑在QuestionView中处理（检查isAllSelfEvaluated）
      return true; // 默认返回true，实际检查在调用处
    }

    const questionType = question.questionType;

    switch (questionType) {
      case QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE:
      case QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE:
        return (userAnswerData.choiceAnswers?.length || 0) > 0;

      case QUESTION_TYPE.QUESTION_TYPE_JUDGMENT:
        return (userAnswerData.choiceAnswers?.length || 0) > 0;

      case QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK:
      case QUESTION_TYPE.QUESTION_TYPE_QA:
        // 主观题：允许提交（即使为空，会有确认对话框）
        return true;

      default:
        return false;
    }
  }, []);

  /**
   * 检查是否为主观题
   */
  const isSubjectiveQuestion = useCallback((questionType: QUESTION_TYPE): boolean => {
    return isSelfEvaluationQuestionType(questionType);
  }, []);

  return {
    // 状态
    confirmationDialog,

    // 方法
    beforeSubmitCheck,
    closeConfirmationDialog,
    confirmSubmit,
    cancelSubmit,
    canSubmit,
    isSubjectiveQuestion,
    beforeSubmitValidation,
  };
}