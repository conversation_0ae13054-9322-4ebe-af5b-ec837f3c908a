import { useQuestionContext } from '@/app/contexts/question-context';
import { QUESTION_TYPE } from '@repo/core/enums';
import { NextQuestionInfo } from '@repo/core/exercise/model';
import { useCallback, useMemo } from 'react';

/**
 * 选择题 ViewModel
 * 
 * ✅ 已适配新的统一架构：
 * - 移除对已删除的 useExerciseSessionContext 的依赖
 * - 使用新的 useQuestionContext 获取题目数据
 * - 简化答案管理，只管理本地状态
 * 
 * 职责：
 * - 管理选择题的选项选择状态
 * - 处理单选/多选逻辑
 * - 提供选项点击交互
 */
export function useChoiceQuestionViewModel(question?: NextQuestionInfo) {
  // 🎯 从统一Context获取题目数据和答案管理方法
  const { currentQuestion, userAnswerData, updateUserAnswer, questionState, setQuestionState } = useQuestionContext();

  // 使用传入的题目或Context中的当前题目
  const displayQuestion = question || currentQuestion;

  // 🔧 修复：使用Context中的选择状态，而不是本地状态
  const choiceAnswers = useMemo(() => {
    const options = userAnswerData.choiceAnswers || [];
    return options;
  }, [userAnswerData.choiceAnswers, displayQuestion?.questionId]);

  // 判断是否为多选题
  const isMultipleChoice = useMemo(() => {
    return displayQuestion?.questionType === QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE;
  }, [displayQuestion?.questionType]);

  // 处理选项点击
  const handleOptionClick = useCallback((optionKey: string) => {
    if (!displayQuestion) return;



    // 🔧 处理状态切换 - 支持 uncertain 和 first_attempt_incorrect 状态
    if (questionState === 'uncertain' || questionState === 'first_attempt_incorrect') {
      setQuestionState('answering');
    }

    // 🔧 关键修复：在 first_attempt_incorrect 状态下，从空选择开始
    let currentChoices = choiceAnswers;
    if (questionState === 'first_attempt_incorrect') {
      currentChoices = [];
    }

    let newSelectedOptions: string[];

    if (isMultipleChoice) {
      // 多选题逻辑
      if (currentChoices.includes(optionKey)) {
        newSelectedOptions = currentChoices.filter(key => key !== optionKey);
      } else {
        newSelectedOptions = [...currentChoices, optionKey];
      }
    } else {
      // 单选题逻辑
      newSelectedOptions = [optionKey];
    }

    // 🔧 修复：更新Context中的答案状态
    updateUserAnswer({ choiceAnswers: newSelectedOptions });
  }, [choiceAnswers, isMultipleChoice, displayQuestion, updateUserAnswer, questionState, setQuestionState]);
  // 重置选择
  const resetSelection = useCallback(() => {
    updateUserAnswer({ choiceAnswers: [] });
  }, [updateUserAnswer]);

  // 检查是否有选择
  const hasSelection = useMemo(() => {
    return choiceAnswers.length > 0;
  }, [choiceAnswers]);

  return {
    choiceAnswers,
    isMultipleChoice,
    hasSelection,
    handleOptionClick,
    resetSelection,
  };
}