"use client";

import { toast } from "@/app/components/common/toast";
import { useQuestionContext } from "@/app/contexts/question-context";
import {
  FeedbackType,
  createFeedbackSubmitData,
  useFeedbackConfig,
  useSubmitFeedback,
  type FeedbackSubmitData,
  type FeedbackTag,
} from "@/app/models/feedback";
import { useClientContext } from "@/app/providers/client-provider";
import { trackEvent } from "@/app/utils/device";
import { captureFeedback, getCurrentScope } from "@sentry/nextjs";
import { useCallback, useEffect, useState } from "react";

export const MAX_CONTENT_LENGTH = 300;

export function useFeedbackViewModel(props?: {
  questionId?: string;
  widgetIndex?: number;
  feedbackType?: FeedbackType;
  feedbackKnowledgeId?: number;
  feedbackPhaseId?: number;
  feedbackLessonId?: number;
  feedbackWidgetIndex?: number;
  feedbackStudySessionId?: string;
  feedbackSubjectId?: number;
}) {
  const questionId = props?.questionId || "";
  const widgetIndex = props?.widgetIndex || 0;
  const feedbackType = props?.feedbackType || FeedbackType.AI;
  const feedbackKnowledgeId = props?.feedbackKnowledgeId || 0;
  const feedbackPhaseId = props?.feedbackPhaseId;
  const feedbackLessonId = props?.feedbackLessonId || 0;
  const feedbackWidgetIndex = props?.feedbackWidgetIndex || 0;
  const feedbackStudySessionId = props?.feedbackStudySessionId || "";
  const feedbackSubjectId = props?.feedbackSubjectId || 0;
  // 状态管理
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTags, setSelectedTags] = useState<number[]>([]);
  const [feedbackContent, setFeedbackContent] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasLoadedConfig, setHasLoadedConfig] = useState(false);
  const { trigger: submitFeedback } = useSubmitFeedback();
  const { currentQuestion } = useQuestionContext();

  const {
    deviceInfo,
    appInfo,
    networkHeaders: networkType,
    studentUserInfo,
  } = useClientContext();

  const {
    config: tags,
    refreshConfig: refreshConfig,
    isMutating,
  } = useFeedbackConfig({
    feedbackType: feedbackType,
  });

  // 组件初始化时就预加载反馈配置
  useEffect(() => {
    if (!hasLoadedConfig && !isMutating) {
      refreshConfig();
      setHasLoadedConfig(true);
    }
  }, [hasLoadedConfig, isMutating, refreshConfig]);

  // 计算属性
  const canSubmit = feedbackContent.trim().length >= 5;

  // 方法
  const handleOpen = useCallback(() => {
    setIsOpen(true);
    trackEvent("feedback_entry_click", {
      question_id: questionId,
      widget_index: widgetIndex,
    });
  }, [questionId, widgetIndex]);

  const handleClose = useCallback(() => {
    setIsOpen(false);
    // 重置状态
    setSelectedTags([]);
    setFeedbackContent("");
    setIsSubmitting(false);
  }, []);

  const handleTagToggle = useCallback((tagId: number) => {
    setSelectedTags((prev) => {
      if (prev.includes(tagId)) {
        return prev.filter((id) => id !== tagId);
      } else {
        return [...prev, tagId];
      }
    });
  }, []);

  const handleContentChange = useCallback((content: string) => {
    // 限制100字
    if (content.length <= MAX_CONTENT_LENGTH) {
      setFeedbackContent(content);
    }
  }, []);

  const handleSubmit = useCallback(async () => {
    // if (!canSubmit || !studentInfo) return;
    setIsSubmitting(true);
    try {
      // 创建反馈提交数据
      const feedbackData: FeedbackSubmitData = createFeedbackSubmitData({
        feedbackType: feedbackType,
        feedbackLabels: selectedTags,
        feedbackContent: feedbackContent.trim(),
        feedbackKnowledgeId: feedbackKnowledgeId,
        feedbackPhaseId: feedbackPhaseId,
        feedbackLessonId: feedbackLessonId,
        feedbackWidgetIndex: feedbackWidgetIndex,
        feedbackStudySessionId: feedbackStudySessionId,
        feedbackSubjectId: feedbackSubjectId,
        feedbackSourceClientId: 3,
        feedbackStudentId: studentUserInfo?.classId || 0,
        feedbackGradeId: studentUserInfo?.classGrade || 0,
        feedbackQuestionId: currentQuestion?.questionId as string,
        feedbackTime: Date.now(),
        feedbackDeviceInfo: {
          os: "android",
          deviceModel: deviceInfo?.deviceName || "",
          appVersion: appInfo?.versionName || "",
          networkType: networkType?.["X-Network-Type"] || "",
        },
      });

      captureFeedback({
        message: `${selectedTags.reduce((acc, tag) => {
          const name = ((tags || []) as FeedbackTag[]).find(
            (t) => t.id === tag
          )?.name;
          if (name) return `${acc}[${name}]`;
          return acc;
        }, "")}\n${feedbackData.feedbackContent}`,
        url: window.location.href,
        name: getCurrentScope().getUser()?.username,
      });

      // 跟踪提交事件
      trackEvent("feedback_submit", {
        question_id: questionId,
        widget_index: widgetIndex,
        tag_ids: selectedTags,
        content_length: feedbackContent.length,
      });

      await submitFeedback(feedbackData);

      // 提交成功后关闭弹窗
      handleClose();
      toast.show("反馈提交成功");
    } catch (error) {
      console.error("Failed to submit feedback:", error);
      toast.show("提交失败，请重试", {
        type: "error",
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [
    questionId,
    widgetIndex,
    selectedTags,
    feedbackContent,
    studentUserInfo,
    deviceInfo,
    appInfo,
    networkType,
    currentQuestion,
    feedbackType,
    feedbackKnowledgeId,
    feedbackPhaseId,
    feedbackLessonId,
    feedbackWidgetIndex,
    feedbackStudySessionId,
    feedbackSubjectId,
    submitFeedback,
    handleClose,
  ]);

  return {
    isOpen,
    tags: (tags || []) as FeedbackTag[],
    selectedTags,
    feedbackContent,
    isSubmitting,
    canSubmit,

    handleOpen,
    handleClose,
    handleTagToggle,
    handleContentChange,
    handleSubmit,
  };
}
