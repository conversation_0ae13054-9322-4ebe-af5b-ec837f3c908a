"use client";

import { QUESTION_TYPE, StudyType } from "@repo/core/enums";
import { ProgressBarProps } from "@repo/core/exercise/components";
import {
  ApiGetNextQuestionData,
  NextQuestionInfo,
  SubmitAnswer,
  SubmitAnswerResponse,
} from "@repo/core/exercise/model";
import {
  QuestionState,
  StudentAnswer,
  UserAnswerData,
} from "@repo/core/exercise/model/types";
import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { trackEvent } from "../utils/device";
import { useQuestionViewModel } from "../viewmodels/exercise/question-viewmodel";
import { useTransitionViewModel } from "../viewmodels/exercise/transition-viewmodel";

// 集成了ViewModel的完整Context接口
interface QuestionContextValue {
  widgetIndex?: number;
  fetchNextQuestion: () => void;
  studyType: StudyType;
  studySessionId: number;
  // ===== 原始配置 =====
  // mode: 'direct' | 'url';
  questionData?: ApiGetNextQuestionData;
  onComplete?: (totalTimeSpent?: number) => void;
  onBack?: () => void;

  // ===== 题目数据 =====
  currentQuestion: NextQuestionInfo | null;

  // ===== 数据状态 =====
  isValidQuestionData?: boolean | null;
  isLoading: boolean;
  hasNextQuestion: boolean; // 是否有下一题

  // ===== 题目状态 =====
  questionState: QuestionState;
  isSubmitting: boolean; // 提交状态
  submitCount: number;
  streakCount: number;
  lastSubmitResult: SubmitAnswerResponse | null;

  // ===== 计时器 =====
  getTimeSpent: () => number; // 业务逻辑用（获取准确时间，毫秒）
  getTotalTimeSpent: () => number; // 获取总时间（毫秒）
  addToTotalTime: (timeMs: number) => void; // 累加时间到总时间
  timerControl: {
    isActive: boolean | null;
    onTimeUpdate: (timeMs: number) => void; // 改为毫秒
    shouldReset: string; // 题目ID变化时重置
  };

  // ===== 计算属性 =====
  canRetryAnswer: boolean;

  // 🔥 进度条相关状态和控制器（来自 useProgressBar hook）
  progressBarProps: ProgressBarProps;
  isProgressBarAnimating: boolean;

  // ===== 答案状态管理 =====
  userAnswerData: {
    choiceAnswers?: string[];
    subjectiveAnswer?: string[];
    englishFillBlankAnswers?: string[];
    inputMode?: "keyboard" | "camera";
    imgFiles?: string[];
  };
  updateUserAnswer: (
    answerData: Partial<QuestionContextValue["userAnswerData"]>
  ) => void;
  clearUserAnswer: () => void;
  clearChoiceAnswer: () => void;
  isAnswerComplete: boolean;

  // ===== 自评状态管理 =====
  isSelfEvaluationSubmitted: boolean;
  setSelfEvaluationSubmitted: (submitted: boolean) => void;

  // ===== 核心方法 =====
  handleSubmitAnswer: (
    preparedAnswer: SubmitAnswer
  ) => Promise<SubmitAnswerResponse | null>;
  handleContinue: () => void;
  handleUncertainClick: () => void;
  handleGiveUpClick: () => void;
  handleSubmitClick: (
    userAnswerData?: UserAnswerData,
    selfEvaluations?: (0 | 1 | 2 | 3)[],
    skipSelfEvaluation?: boolean
  ) => Promise<void>;
  setQuestionState: (newState: QuestionState) => void;

  // ===== 🔥 转场管理 =====
  transitionState: ReturnType<typeof useTransitionViewModel>["transitionState"];
  executeTransitionSequence: ReturnType<
    typeof useTransitionViewModel
  >["executeTransitionSequence"];
  clearTransitionRecords: ReturnType<
    typeof useTransitionViewModel
  >["clearTransitionRecords"];
  nextTransition: ReturnType<typeof useTransitionViewModel>["nextTransition"];

  // ===== 埋点方法 =====
  trackEventWithExercise: (
    eventName: string,
    additionalParams?: Record<string, unknown>
  ) => void;

  // ===== 学生作答数据 =====
  studentAnswer?: StudentAnswer;

  // ===== 学科信息 =====
  isEnglish: boolean;
  subjectId: number | null;
  subjectName: string | undefined;
}

interface QuestionContextProviderProps {
  widgetIndex?: number;
  children: ReactNode;
  firstQuestionData?: ApiGetNextQuestionData;
  onBack?: () => void;
  onComplete?: (totalTimeSpent?: number) => void;
  studyType: StudyType;
  studySessionId: number;
  activeInCourse?: boolean; // AI课程页面激活状态
}

// 创建上下文 - 使用唯一名称避免与预览模式的 Context 冲突
const ExerciseQuestionContext = createContext<QuestionContextValue>(
  {} as QuestionContextValue
);

/**
 * 统一的题目上下文提供者
 *
 * ✅ 集成了 ViewModel 的完整功能：
 * - 自动调用 useQuestionViewModel 获取所有状态和方法
 * - 集成 useTransitionViewModel 管理转场逻辑
 * - 将配置和 ViewModel 数据合并提供给下层组件
 * - 下层组件只需要调用 useQuestionContext 即可获得所有功能
 *
 * 优势：
 * - 简化了组件调用，不需要同时使用多个 hooks
 * - 统一的数据源，避免状态不一致
 * - 更好的类型推导和代码提示
 */
export function QuestionContextProvider({
  widgetIndex,
  onBack,
  children,
  firstQuestionData,
  onComplete,
  studyType,
  studySessionId,
  activeInCourse,
}: QuestionContextProviderProps) {
  // 🔥 集成转场ViewModel
  const transitionViewModel = useTransitionViewModel();
  // 调用原有的 ViewModel hook 获取所有状态和方法
  const viewModelData = useQuestionViewModel({
    firstQuestionData: firstQuestionData,
    onComplete: onComplete,
    studyType: studyType,
    studySessionId: studySessionId,
    widgetIndex: widgetIndex,
    activeInCourse: activeInCourse,
    transitionViewModel: transitionViewModel,
  });

  // 🔧 新增：统一的答案状态管理
  const [userAnswerData, setUserAnswerData] = useState({
    choiceAnswers: [] as string[],
    subjectiveAnswer: [] as string[],
    englishFillBlankAnswers: [] as string[],
    inputMode: "keyboard" as "keyboard" | "camera",
    imgFiles: [] as string[],
  });

  // 🆕 自评提交状态
  const [isSelfEvaluationSubmitted, setIsSelfEvaluationSubmitted] =
    useState(false);

  // 🆕 总时间累加器（毫秒）
  const [totalTimeSpent, setTotalTimeSpent] = useState(0);

  // 更新用户答案数据
  const updateUserAnswer = useCallback(
    (answerData: Partial<typeof userAnswerData>) => {
      console.log("answerData", answerData);
      setUserAnswerData((prev) => ({ ...prev, ...answerData }));
    },
    []
  );

  // 清空用户答案数据
  const clearUserAnswer = useCallback(() => {
    setUserAnswerData({
      choiceAnswers: [],
      subjectiveAnswer: [],
      englishFillBlankAnswers: [],
      inputMode: "keyboard",
      imgFiles: [],
    });
  }, []);

  // 🔧 新增：清空选择题答案（用于首次错误后重新选择）
  const clearChoiceAnswer = useCallback(() => {
    // console.log("[QuestionContext] 🔄 清空选择题答案");
    updateUserAnswer({ choiceAnswers: [] });
  }, [updateUserAnswer]);

  // 🆕 自评提交状态设置
  const setSelfEvaluationSubmitted = useCallback((submitted: boolean) => {
    setIsSelfEvaluationSubmitted(submitted);
  }, []);

  // 🆕 总时间管理函数
  const getTotalTimeSpent = useCallback(() => {
    return totalTimeSpent;
  }, [totalTimeSpent]);

  const addToTotalTime = useCallback((timeMs: number) => {
    setTotalTimeSpent((prev) => prev + timeMs);
  }, []);

  // 🆕 埋点方法：自动处理练习相关参数
  const trackEventWithExercise = useCallback(
    (eventName: string, additionalParams: Record<string, unknown> = {}) => {
      const currentQuestionId = viewModelData.baseTrackParams?.question_id;
      if (!currentQuestionId) {
        console.warn(
          "[QuestionContext] trackEventWithExercise: 当前没有题目ID，跳过埋点"
        );
        return;
      }

      const finalParams = {
        ...viewModelData.baseTrackParams,
        ...additionalParams,
      };

      trackEvent(eventName, finalParams);
      // console.log(`[QuestionContext] 📊 ${eventName} 埋点:`, finalParams);
    },
    [viewModelData.baseTrackParams]
  );

  // 🔧 监听题目ID变化，自动清空用户答案状态并埋点
  const previousQuestionIdRef = useRef<string | null>(null);
  useEffect(() => {
    const currentQuestionId = viewModelData.currentQuestion?.questionId;
    if (
      currentQuestionId &&
      String(currentQuestionId) !== previousQuestionIdRef.current
    ) {
      // 题目ID发生变化，清空用户答案状态
      // console.log("[QuestionContext] 🔄 题目ID变化，清空用户答案状态:", {
      //   previous: previousQuestionIdRef.current,
      //   current: String(currentQuestionId),
      // });
      clearUserAnswer();
      setIsSelfEvaluationSubmitted(false); // 🆕 重置自评提交状态
      previousQuestionIdRef.current = String(currentQuestionId);

      // 📊 埋点：题目加载完成
      if (viewModelData.currentQuestion) {
        trackEventWithExercise("exercise_question_load");
      }
    }
  }, [
    viewModelData.currentQuestion?.questionId,
    clearUserAnswer,
    studyType,
    studySessionId,
    viewModelData.currentQuestion,
    trackEventWithExercise,
  ]);

  // 🔧 监听选择题首次错误状态，自动清空选择
  useEffect(() => {
    if (viewModelData.questionState === "first_attempt_incorrect") {
      const isChoiceQuestion =
        viewModelData.currentQuestion?.questionType ===
          QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE ||
        viewModelData.currentQuestion?.questionType ===
          QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE;

      if (isChoiceQuestion) {
        // console.log(
        //   "[QuestionContext] 🔄 检测到选择题首次错误，自动清空用户选择",
        //   {
        //     questionId: viewModelData.currentQuestion?.questionId,
        //     questionState: viewModelData.questionState,
        //   }
        // );
        clearChoiceAnswer();
      }
    }
  }, [
    viewModelData.questionState,
    viewModelData.currentQuestion?.questionType,
    viewModelData.currentQuestion?.questionId,
    clearChoiceAnswer,
  ]);

  // 检查答案是否完整
  const isAnswerComplete = useMemo(() => {
    if (!viewModelData.currentQuestion) return false;

    const questionType = viewModelData.currentQuestion.questionType;

    switch (questionType) {
      case QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE:
      case QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE:
        return userAnswerData.choiceAnswers.length > 0;

      case QUESTION_TYPE.QUESTION_TYPE_JUDGMENT:
        return userAnswerData.choiceAnswers.length > 0;

      case QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK:
        return true;
      // 🔧 修复：填空题必须所有空都填上才能提交
      // return userAnswerData?.subjectiveAnswer?.every(answer => answer.trim() !== '');

      case QUESTION_TYPE.QUESTION_TYPE_QA:
        return true; // 允许提交空答案以触发确认对话框

      default:
        return false;
    }
  }, [userAnswerData, viewModelData.currentQuestion]);

  // 🔧 重写 handleUncertainClick 方法，同时更新 viewmodel 和 context 状态
  const handleUncertainClick = useCallback(() => {
    // 调用原始的 viewmodel 方法
    viewModelData.handleUncertainClick();
    // 同时清空 context 中的用户答案数据
    setUserAnswerData({
      choiceAnswers: [],
      subjectiveAnswer: [],
      englishFillBlankAnswers: [],
      inputMode: "keyboard",
      imgFiles: [],
    });
    // 🆕 重置自评提交状态
    setIsSelfEvaluationSubmitted(false);
  }, [viewModelData]);

  // 🆕 重写 handleContinue 方法，添加总时间追踪功能
  const handleContinue = useCallback(() => {
    // console.log(`[QuestionContext] handleContinue`, {
    //   viewModelData,
    //   currentTimeSpent: viewModelData.getTimeSpent(),
    //   totalTimeSpent: totalTimeSpent,
    //   onComplete: onComplete,
    //   lastSubmitResult: viewModelData.lastSubmitResult,
    //   isLastQuestion: !viewModelData.lastSubmitResult?.hasNextQuestion,
    // });

    // 在调用原始 handleContinue 前，累加当前题目的时间
    const currentTimeSpent = viewModelData.getTimeSpent();
    if (currentTimeSpent > 0) {
      addToTotalTime(currentTimeSpent);
      // console.log(
      //   "[QuestionContext] 累加当前题目时间到总时间:",
      //   currentTimeSpent,
      //   "ms, 新总时间:",
      //   totalTimeSpent + currentTimeSpent
      // );
    }

    // 检查是否是最后一题（没有下一题）
    const isLastQuestion = !viewModelData.lastSubmitResult?.hasNextQuestion;

    if (isLastQuestion) {
      // 最后一题，调用 onComplete 并传递总时间
      const finalTotalTime = totalTimeSpent + currentTimeSpent;
      // console.log(
      //   "[QuestionContext] 练习完成，传递总时间给 onComplete:",
      //   finalTotalTime,
      //   "ms"
      // );
      if (onComplete) {
        onComplete(finalTotalTime);
      }
    } else {
      // 不是最后一题，继续下一题
      // console.log(
      //   "[QuestionContext] 继续下一题，当前累计总时间:",
      //   totalTimeSpent + currentTimeSpent,
      //   "ms"
      // );
      viewModelData.handleContinue();
    }
  }, [viewModelData, addToTotalTime, totalTimeSpent, onComplete]);

  // 🔥 性能优化：使用 useMemo 稳定 Context 值，减少不必要重渲染
  const contextValue: QuestionContextValue = useMemo(
    () => ({
      ...viewModelData,
      studyType: studyType,
      studySessionId: studySessionId,
      // 原始配置
      // mode: config.mode,
      questionData: firstQuestionData,
      onComplete: onComplete,
      onBack: onBack,
      // 答案状态管理
      userAnswerData,
      updateUserAnswer,
      clearUserAnswer,
      clearChoiceAnswer,
      isAnswerComplete,
      // 🆕 自评状态管理
      isSelfEvaluationSubmitted,
      setSelfEvaluationSubmitted,
      // 🆕 总时间管理
      getTotalTimeSpent,
      addToTotalTime,
      // 🔧 使用重写的方法
      handleUncertainClick,
      handleContinue,

      // 🔥 转场管理
      transitionState: transitionViewModel.transitionState,
      executeTransitionSequence: transitionViewModel.executeTransitionSequence,
      clearTransitionRecords: transitionViewModel.clearTransitionRecords,
      nextTransition: transitionViewModel.nextTransition,
      widgetIndex: widgetIndex,

      // 🆕 埋点方法
      trackEventWithExercise,

      // 🆕 下一题状态
      hasNextQuestion: viewModelData.lastSubmitResult?.hasNextQuestion ?? true,

      // 🆕 学生作答数据
      studentAnswer: viewModelData.studentAnswer,
    }),
    [
      viewModelData,
      studyType,
      studySessionId,
      firstQuestionData,
      onComplete,
      onBack,
      userAnswerData,
      updateUserAnswer,
      clearUserAnswer,
      clearChoiceAnswer,
      isAnswerComplete,
      isSelfEvaluationSubmitted,
      setSelfEvaluationSubmitted,
      getTotalTimeSpent,
      addToTotalTime,
      handleUncertainClick,
      handleContinue,
      transitionViewModel,
      trackEventWithExercise,
      widgetIndex,
    ]
  );

  return (
    <ExerciseQuestionContext.Provider value={contextValue}>
      {children}
    </ExerciseQuestionContext.Provider>
  );
}

export function useQuestionContext() {
  const context = useContext(ExerciseQuestionContext);
  return context;
}
