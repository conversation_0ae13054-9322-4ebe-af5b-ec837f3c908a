# Exercise Contexts 层

## 📋 概述

Exercise Contexts 层负责提供全局状态管理和跨组件数据共享，作为 MVVM 架构中的协调层，连接 ViewModel 和 View 层，提供统一的状态管理接口。

## 🏗️ 架构设计

### MVVM 分层职责

```
View Layer (views/exercise/)
    ↓ 使用
Context Layer (contexts/) ← 当前层
    ↓ 协调
ViewModel Layer (viewmodels/exercise/)
    ↓ 调用  
Model Layer (models/exercise/)
```

### 核心原则

- **状态协调**：协调不同层级的状态管理
- **数据共享**：提供跨组件的数据共享机制
- **解耦合**：减少组件间的直接依赖
- **性能优化**：避免不必要的重渲染

## 📁 文件结构

```
contexts/
├── README.md                           # 当前文档
├── exercise-context.tsx        # 练习会话全局状态
└── question-context.tsx                # 题目级状态管理
```

## 🔧 核心 Contexts

### 1. ExerciseSessionContext
**职责**：管理整个练习会话的全局状态

**提供的状态**：
```typescript
interface ExerciseSessionContextValue {
  // 会话状态
  sessionState: SessionState;
  questionState: QuestionState;
  
  // 反馈数据
  feedbackData: any;
  clearFeedbackData: () => void;
  
  // 操作方法
  handleSubmitAnswer: (answer: QuestionAnswer) => Promise<void>;
  handleContinue: () => void;
  
  // 加载状态
  isLoading: boolean;
  error: Error | null;
}
```

**使用示例**：
```typescript
// Provider 包装
export function ExerciseSessionProvider({
  courseId,
  lessonId,
  studyType,
  children
}: ExerciseSessionProviderProps) {
  const sessionViewModel = useExerciseSessionViewModel(
    courseId,
    lessonId,
    studyType
  );

  return (
    <ExerciseSessionContext.Provider value={sessionViewModel}>
      {children}
    </ExerciseSessionContext.Provider>
  );
}

// 在组件中使用
const {
  sessionState,
  questionState,
  handleSubmitAnswer
} = useExerciseSessionContext();
```

**状态管理特性**：
- 会话级别的状态持久化
- 跨组件的状态同步
- 统一的错误处理
- 自动的状态更新

### 2. QuestionContext
**职责**：管理单个题目的状态和操作

**提供的状态**：
```typescript
interface QuestionContextValue {
  // 题目状态
  question: Question;
  isAnswerComplete: boolean;
  
  // 操作方法
  submitCurrentAnswer: () => Promise<void>;
  
  // 状态管理
  setQuestionState: (state: QuestionState) => void;
}
```

**协调机制**：
```typescript
// 协调 ExerciseSessionContext 和具体题目操作
const submitCurrentAnswer = useCallback(async () => {
  if (!isAnswerComplete) return;
  
  // 构建答案数据
  const answerData = buildAnswerFromCurrentState();
  
  // 调用会话级别的提交方法
  await handleSubmitAnswer(answerData);
}, [isAnswerComplete, handleSubmitAnswer]);
```

**使用场景**：
- 题目级别的状态管理
- 答案完整性检查
- 题目操作的协调
- 状态变化的通知

## 🔄 状态流转机制

### 状态层级结构
```
ExerciseSessionContext (全局)
    ├── sessionState (会话状态)
    ├── questionState (当前题目状态)
    └── feedbackData (反馈数据)
        ↓ 协调
QuestionContext (题目级)
    ├── isAnswerComplete (答案完整性)
    └── submitCurrentAnswer (提交操作)
```

### 状态同步流程
```
用户操作 (View)
    ↓
QuestionContext 状态更新
    ↓
协调方法调用
    ↓
ExerciseSessionContext 状态更新
    ↓
ViewModel 业务逻辑处理
    ↓
Model 层 API 调用
    ↓
状态更新通知
    ↓
View 重新渲染
```

### 数据流向图
```mermaid
graph TD
    A[User Interaction] --> B[QuestionContext]
    B --> C[submitCurrentAnswer]
    C --> D[ExerciseSessionContext]
    D --> E[handleSubmitAnswer]
    E --> F[ViewModel Logic]
    F --> G[Model API]
    G --> H[State Update]
    H --> I[View Re-render]
```

## 🎯 Context 设计模式

### Provider 模式
```typescript
// 分层 Provider 结构
export function ExercisePage({ courseId, lessonId, studyType }) {
  return (
    <ExerciseSessionProvider
      courseId={courseId}
      lessonId={lessonId}
      studyType={studyType}
    >
      <QuestionProvider>
        <ExercisePageContent />
      </QuestionProvider>
    </ExerciseSessionProvider>
  );
}
```

### Hook 封装模式
```typescript
// 自定义 Hook 封装 Context 使用
export const useExerciseSessionContext = () => {
  const context = useContext(ExerciseSessionContext);
  
  if (!context) {
    throw new Error(
      'useExerciseSessionContext must be used within ExerciseSessionProvider'
    );
  }
  
  return context;
};

// 带类型安全的 Hook
export const useQuestionState = (): QuestionContextValue => {
  const context = useContext(QuestionContext);
  
  if (!context) {
    throw new Error(
      'useQuestionState must be used within QuestionProvider'
    );
  }
  
  return context;
};
```

### 选择性订阅模式
```typescript
// 只订阅需要的状态片段
export const useSessionProgress = () => {
  const { sessionState } = useExerciseSessionContext();
  
  return useMemo(() => ({
    currentIndex: sessionState.currentQuestionIndex,
    progress: sessionState.progressPercentage,
    isLastQuestion: sessionState.isLastQuestion
  }), [
    sessionState.currentQuestionIndex,
    sessionState.progressPercentage,
    sessionState.isLastQuestion
  ]);
};
```

## ⚡ 性能优化策略

### Context 分割
```typescript
// 按功能分割 Context，避免不必要的重渲染
const SessionStateContext = createContext<SessionState>();
const SessionActionsContext = createContext<SessionActions>();

// 分别提供状态和操作
export const useSessionState = () => useContext(SessionStateContext);
export const useSessionActions = () => useContext(SessionActionsContext);
```

### 记忆化优化
```typescript
// 使用 useMemo 优化 Context 值
const ExerciseSessionProvider = ({ children, ...props }) => {
  const sessionViewModel = useExerciseSessionViewModel(props);
  
  // 记忆化 Context 值，避免不必要的重渲染
  const contextValue = useMemo(() => ({
    ...sessionViewModel,
    // 稳定的引用
  }), [sessionViewModel]);
  
  return (
    <ExerciseSessionContext.Provider value={contextValue}>
      {children}
    </ExerciseSessionContext.Provider>
  );
};
```

### 选择性更新
```typescript
// 使用 useCallback 稳定函数引用
const handleSubmitAnswer = useCallback(async (answer: QuestionAnswer) => {
  try {
    setIsLoading(true);
    const result = await submitAnswer(answer);
    updateSessionState(result);
  } catch (error) {
    setError(error);
  } finally {
    setIsLoading(false);
  }
}, [submitAnswer, updateSessionState]);
```

## 🔗 与其他层的交互

### 与 ViewModel 层协调
```typescript
// Context 作为 ViewModel 的容器
export const ExerciseSessionProvider = ({ children, ...props }) => {
  // 使用 ViewModel Hook
  const sessionViewModel = useExerciseSessionViewModel(props);
  
  // 提供给子组件
  return (
    <ExerciseSessionContext.Provider value={sessionViewModel}>
      {children}
    </ExerciseSessionContext.Provider>
  );
};
```

### 与 View 层交互
```typescript
// View 层通过 Context 获取状态和方法
const QuestionView = () => {
  // 获取全局状态
  const { questionState, feedbackData } = useExerciseSessionContext();
  
  // 获取题目级状态
  const { isAnswerComplete, submitCurrentAnswer } = useQuestionState();
  
  return (
    <div>
      {/* 基于状态渲染 */}
      <Button 
        onClick={submitCurrentAnswer}
        disabled={!isAnswerComplete}
      >
        提交
      </Button>
    </div>
  );
};
```

### 状态持久化
```typescript
// Context 层处理状态持久化
export const ExerciseSessionProvider = ({ children, ...props }) => {
  const sessionViewModel = useExerciseSessionViewModel(props);
  
  // 自动保存状态
  useEffect(() => {
    const saveState = debounce(() => {
      localStorage.setItem(
        'exercise_session_state',
        JSON.stringify(sessionViewModel.sessionState)
      );
    }, 1000);
    
    saveState();
  }, [sessionViewModel.sessionState]);
  
  return (
    <ExerciseSessionContext.Provider value={sessionViewModel}>
      {children}
    </ExerciseSessionContext.Provider>
  );
};
```

## 🧪 测试策略

### Context 测试
```typescript
// Context Provider 测试
const renderWithContext = (component: React.ReactElement) => {
  return render(
    <ExerciseSessionProvider
      courseId="test-course"
      lessonId="test-lesson"
      studyType={studyType.IN_CLASS}
    >
      {component}
    </ExerciseSessionProvider>
  );
};

// Hook 测试
test('useExerciseSessionContext provides correct values', () => {
  const TestComponent = () => {
    const { sessionState, questionState } = useExerciseSessionContext();
    return (
      <div>
        <span data-testid="session-state">{sessionState.currentQuestionIndex}</span>
        <span data-testid="question-state">{questionState}</span>
      </div>
    );
  };
  
  renderWithContext(<TestComponent />);
  
  expect(screen.getByTestId('session-state')).toHaveTextContent('0');
  expect(screen.getByTestId('question-state')).toHaveTextContent('answering');
});
```

### 状态同步测试
```typescript
// 测试状态同步机制
test('state synchronization between contexts', async () => {
  const TestComponent = () => {
    const { handleSubmitAnswer } = useExerciseSessionContext();
    const { submitCurrentAnswer } = useQuestionState();
    
    return (
      <button onClick={submitCurrentAnswer}>
        Submit
      </button>
    );
  };
  
  renderWithContext(<TestComponent />);
  
  fireEvent.click(screen.getByText('Submit'));
  
  // 验证状态同步
  await waitFor(() => {
    expect(mockHandleSubmitAnswer).toHaveBeenCalled();
  });
});
```

## 📚 最佳实践

### ✅ 推荐做法
1. **Context 分层**：按功能和层级合理分割 Context
2. **性能优化**：使用 useMemo 和 useCallback 优化性能
3. **类型安全**：完整的 TypeScript 类型定义
4. **错误边界**：提供 Context 使用的错误检查

### ❌ 避免做法
1. **过度使用**：不要为每个状态都创建 Context
2. **深层嵌套**：避免过深的 Provider 嵌套
3. **频繁更新**：避免高频率的 Context 值更新
4. **业务逻辑**：Context 不应包含复杂业务逻辑

## 🔄 迁移指南

### 从 Props Drilling 迁移
```typescript
// 迁移前：Props 层层传递
const ExercisePage = ({ courseId, lessonId }) => {
  const [sessionState, setSessionState] = useState();
  
  return (
    <QuestionView 
      sessionState={sessionState}
      onSubmit={handleSubmit}
      // ... 更多 props
    />
  );
};

// 迁移后：Context 提供状态
const ExercisePage = ({ courseId, lessonId }) => {
  return (
    <ExerciseSessionProvider courseId={courseId} lessonId={lessonId}>
      <QuestionView />
    </ExerciseSessionProvider>
  );
};
```

### 状态管理重构
```typescript
// 重构前：分散的状态管理
const [questionState, setQuestionState] = useState();
const [feedbackData, setFeedbackData] = useState();
const [isLoading, setIsLoading] = useState();

// 重构后：统一的 Context 管理
const {
  questionState,
  feedbackData,
  isLoading,
  handleSubmitAnswer
} = useExerciseSessionContext();
```

## 📖 相关文档

- [ViewModels 层文档](../viewmodels/exercise/README.md)
- [Views 层文档](../views/exercise/README.md)
- [Models 层文档](../models/exercise/README.md)
- [React Context 最佳实践](../../../../docs/react/context-best-practices.md)