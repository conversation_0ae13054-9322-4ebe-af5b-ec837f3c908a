/**
 * 错题本模块 - Model 层
 * 负责错题本相关的数据获取、提交和管理
 */

import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import { get, post, deleted } from '@/app/utils/fetcher';
// 🔥 移除 zod schema 导入，不再需要运行时校验
import type {
  GetWrongQuestionsParams,
  GetWrongQuestionsResponse,
  ManualAddWrongQuestionPayload,
  ManualAddWrongQuestionResponse,
  DeleteWrongQuestionsParams,
  DeleteWrongQuestionsResponse,
} from './types';

// ==================== 数据获取 Hooks ====================

/**
 * 获取学生错题本列表
 * 支持分页和多维度筛选
 */
export function useWrongQuestions(params?: GetWrongQuestionsParams) {
  const shouldFetch = params?.studentId;

  // 构建查询参数
  const queryParams = shouldFetch
    ? {
      studentId: params.studentId.toString(),
      ...(params.page && { page: params.page.toString() }),
      ...(params.pageSize && { pageSize: params.pageSize.toString() }),
      ...(params.addMethod && { addMethod: params.addMethod.toString() }),
      ...(params.courseId && { courseId: params.courseId.toString() }),
    }
    : undefined;

  const { data, error, isLoading, mutate } = useSWR(
    shouldFetch ? ['/api/v1/study/wrong_questions', queryParams] : null,
    async ([url, query]) => {
      const response = await get<unknown>(url, { query });
      // 🔥 移除 zod 校验，直接使用响应数据
      return transformWrongQuestionsData(response as any);
    }
  );

  return {
    wrongQuestions: data?.data || [],
    pageInfo: data?.pageInfo,
    error,
    isLoading,
    refreshWrongQuestions: mutate,
  };
}

/**
 * 获取学生错题本列表（带自动刷新）
 * 适用于需要实时更新的场景
 */
export function useWrongQuestionsWithRefresh(
  params?: GetWrongQuestionsParams,
  refreshInterval?: number
) {
  const shouldFetch = params?.studentId;

  const queryParams = shouldFetch
    ? {
      studentId: params.studentId.toString(),
      ...(params.page && { page: params.page.toString() }),
      ...(params.pageSize && { pageSize: params.pageSize.toString() }),
      ...(params.addMethod && { addMethod: params.addMethod.toString() }),
      ...(params.courseId && { courseId: params.courseId.toString() }),
    }
    : undefined;

  const { data, error, isLoading, mutate } = useSWR(
    shouldFetch ? ['/api/v1/study/wrong_questions', queryParams] : null,
    async ([url, query]) => {
      const response = await get<unknown>(url, { query });
      // 🔥 移除 zod 校验，直接使用响应数据
      return transformWrongQuestionsData(response as any);
    },
    {
      refreshInterval: refreshInterval || 0,
    }
  );

  return {
    wrongQuestions: data?.data || [],
    pageInfo: data?.pageInfo,
    error,
    isLoading,
    refreshWrongQuestions: mutate,
  };
}

// ==================== 数据提交 Hooks ====================

/**
 * 手动添加错题到错题本
 * 学生主动选择错因标签添加错题
 */
export function useManualAddWrongQuestion() {
  const { trigger, isMutating, error } = useSWRMutation(
    '/api/v1/study/wrong_question/add',
    async (url, { arg }: { arg: ManualAddWrongQuestionPayload }) => {
      const response = await post<unknown>(url, { arg });
      // 🔥 移除 zod 校验，直接使用响应数据
      return transformManualAddWrongQuestionData(response as any);
    }
  );

  return {
    manualAddWrongQuestion: trigger,
    isAdding: isMutating,
    addError: error,
  };
}

/**
 * 从错题本删除错题
 * 支持批量删除操作
 */
export function useDeleteWrongQuestions() {
  const { trigger, isMutating, error } = useSWRMutation(
    '/api/v1/study/wrong_questions',
    async (url, { arg }: { arg: DeleteWrongQuestionsParams }) => {
      const queryParams = {
        studentId: arg.studentId.toString(),
        wrongQuestionIds: arg.wrongQuestionIds,
      };

      // 使用项目统一的fetcher，但需要自定义DELETE方法
      const response = await deleted(url, { query: queryParams });
      // 🔥 移除 zod 校验，直接使用响应数据
      return transformDeleteWrongQuestionsData(response as any);
    }
  );

  return {
    deleteWrongQuestions: trigger,
    isDeleting: isMutating,
    deleteError: error,
  };
}

// ==================== 数据转换函数 ====================

/**
 * 转换错题本列表数据
 * 将API响应转换为前端使用的格式
 * 🔥 移除 zod 类型依赖，使用 any 类型
 */
function transformWrongQuestionsData(
  apiData: any
): GetWrongQuestionsResponse {
  return {
    data: apiData.data.map((item: any) => ({
      wrongQuestionId: item.wrongQuestionId,
      questionId: item.questionId,
      addMethod: item.addMethod,
      errorReasonTags: item.errorReasonTags,
      addTime: item.addTime,
      questionContent: item.questionContent,
    })),
    pageInfo: {
      page: apiData.pageInfo.page,
      pageSize: apiData.pageInfo.pageSize,
      total: apiData.pageInfo.total,
    },
  };
}

/**
 * 转换手动添加错题响应数据
 * 🔥 移除 zod 类型依赖，使用 any 类型
 */
function transformManualAddWrongQuestionData(
  apiData: any
): ManualAddWrongQuestionResponse {
  return {
    wrongQuestionId: apiData.wrongQuestionId,
    addMethod: apiData.addMethod,
  };
}

/**
 * 转换删除错题响应数据
 * 🔥 移除 zod 类型依赖，使用 any 类型
 */
function transformDeleteWrongQuestionsData(
  apiData: any
): DeleteWrongQuestionsResponse {
  return {
    deletedCount: apiData.deletedCount,
    failedIds: apiData.failedIds,
  };
}