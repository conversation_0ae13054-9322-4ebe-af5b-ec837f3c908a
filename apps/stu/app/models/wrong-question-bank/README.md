# 错题本模块 (Wrong Question Bank)

错题本模块负责管理学生的错题记录，包括获取错题列表、手动添加错题和删除错题等功能。

## 🎯 核心功能

- **错题列表获取** - 支持分页和多维度筛选
- **手动添加错题** - 学生主动添加错题并选择错因标签
- **批量删除错题** - 支持删除单个或多个错题记录
- **实时数据更新** - 支持自动刷新和手动刷新

## 📋 可用 Hooks

| Hook 名称 | 描述 | 主要接口 | 依赖 API |
|-----------|------|----------|----------|
| `useWrongQuestions` | 获取错题本列表 | `wrongQuestions, pageInfo, isLoading, error, refreshWrongQuestions` | GET `/api/v1/study/wrong_questions` |
| `useWrongQuestionsWithRefresh` | 获取错题本列表（带自动刷新） | `wrongQuestions, pageInfo, isLoading, error, refreshWrongQuestions` | GET `/api/v1/study/wrong_questions` |
| `useManualAddWrongQuestion` | 手动添加错题 | `manualAddWrongQuestion, isAdding, addError` | POST `/api/v1/study/wrong_question/add` |
| `useDeleteWrongQuestions` | 删除错题 | `deleteWrongQuestions, isDeleting, deleteError` | DELETE `/api/v1/study/wrong_questions` |

## 🚀 使用示例

### 获取错题列表

```typescript
import { useWrongQuestions } from '@/models/wrong-question-bank';

function WrongQuestionList() {
  const { wrongQuestions, pageInfo, isLoading, error, refreshWrongQuestions } = useWrongQuestions({
    studentId: 1001,
    page: 1,
    pageSize: 20,
    addMethod: 1, // 1:自动添加, 2:手动添加
    courseId: 2001,
  });

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>加载失败</div>;

  return (
    <div>
      {wrongQuestions.map(question => (
        <div key={question.wrongQuestionId}>
          <h3>{question.questionContent}</h3>
          <p>错因：{question.errorReasonTags.join(', ')}</p>
          <p>添加方式：{question.addMethod === 1 ? '自动添加' : '手动添加'}</p>
        </div>
      ))}
      <div>
        第 {pageInfo?.page} 页，共 {pageInfo?.total} 条记录
      </div>
    </div>
  );
}
```

### 手动添加错题

```typescript
import { useManualAddWrongQuestion } from '@/models/wrong-question-bank';

function AddWrongQuestionForm() {
  const { manualAddWrongQuestion, isAdding, addError } = useManualAddWrongQuestion();

  const handleSubmit = async () => {
    try {
      const result = await manualAddWrongQuestion({
        studentId: 1001,
        questionId: 3001,
        errorReasonTags: ['计算错误', '概念理解不清'],
        notes: '这道题需要重点复习',
      });
      console.log('添加成功:', result);
    } catch (error) {
      console.error('添加失败:', error);
    }
  };

  return (
    <div>
      <button onClick={handleSubmit} disabled={isAdding}>
        {isAdding ? '添加中...' : '添加到错题本'}
      </button>
      {addError && <div>添加失败: {addError.message}</div>}
    </div>
  );
}
```

### 删除错题

```typescript
import { useDeleteWrongQuestions } from '@/models/wrong-question-bank';

function DeleteWrongQuestionButton({ wrongQuestionIds }: { wrongQuestionIds: number[] }) {
  const { deleteWrongQuestions, isDeleting, deleteError } = useDeleteWrongQuestions();

  const handleDelete = async () => {
    try {
      const result = await deleteWrongQuestions({
        studentId: 1001,
        wrongQuestionIds: wrongQuestionIds.join(','),
      });
      console.log('删除结果:', result);
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  return (
    <div>
      <button onClick={handleDelete} disabled={isDeleting}>
        {isDeleting ? '删除中...' : '删除错题'}
      </button>
      {deleteError && <div>删除失败: {deleteError.message}</div>}
    </div>
  );
}
```

### 带自动刷新的错题列表

```typescript
import { useWrongQuestionsWithRefresh } from '@/models/wrong-question-bank';

function LiveWrongQuestionList() {
  const { wrongQuestions, isLoading } = useWrongQuestionsWithRefresh(
    {
      studentId: 1001,
      page: 1,
      pageSize: 20,
    },
    5000 // 每5秒自动刷新
  );

  return (
    <div>
      {isLoading && <div>刷新中...</div>}
      {wrongQuestions.map(question => (
        <div key={question.wrongQuestionId}>
          {question.questionContent}
        </div>
      ))}
    </div>
  );
}
```

## 📊 核心数据结构

### 错题记录 (WrongQuestion)
```typescript
interface WrongQuestion {
  wrongQuestionId: number;    // 错题记录ID
  questionId: number;         // 题目ID
  addMethod: 1 | 2;          // 添加方式 (1:自动添加, 2:手动添加)
  errorReasonTags: string[]; // 错因标签
  addTime: number;           // 添加时间（UTC秒数）
  questionContent: string;   // 题目内容
}
```

### 分页信息 (PageInfo)
```typescript
interface PageInfo {
  page: number;      // 当前页码
  pageSize: number;  // 每页数量
  total: number;     // 总记录数
}
```

## 🔧 API 接口说明

### GET /api/v1/study/wrong_questions
获取学生错题本列表，支持分页和筛选。

**查询参数：**
- `studentId` (必需) - 学生ID
- `page` (可选) - 页码，从1开始
- `pageSize` (可选) - 每页数量
- `addMethod` (可选) - 添加方式筛选 (1:自动添加, 2:手动添加)
- `courseId` (可选) - 课程ID筛选

### POST /api/v1/study/wrong_question/add
手动添加错题到错题本。

**请求体：**
```typescript
{
  studentId: number;        // 学生ID
  questionId: number;       // 题目ID
  errorReasonTags: string[]; // 错因标签
  notes?: string;           // 学生备注
}
```

### DELETE /api/v1/study/wrong_questions
从错题本删除错题，支持批量删除。

**查询参数：**
- `studentId` (必需) - 学生ID
- `wrongQuestionIds` (必需) - 错题记录ID列表，多个ID用逗号分隔

## ⚠️ 注意事项

1. **学生ID必需** - 所有接口都需要提供有效的学生ID
2. **批量删除格式** - 删除接口的ID列表需要用逗号分隔的字符串格式
3. **错因标签** - 手动添加时必须提供至少一个错因标签
4. **去重处理** - 系统会自动处理重复添加的情况
5. **权限控制** - 学生只能操作自己的错题记录

## 🧪 测试覆盖

- ✅ 基础功能测试 - 所有 Hook 的基本功能
- ✅ Schema 校验测试 - 数据格式验证
- ✅ 错误处理测试 - 异常情况处理
- ✅ 边界条件测试 - 参数边界值验证