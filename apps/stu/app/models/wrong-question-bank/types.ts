/**
 * 错题本模块 - TypeScript 类型定义（前端传参）
 */

// ==================== 请求参数类型 ====================

/**
 * 获取错题本列表的查询参数
 */
export interface GetWrongQuestionsParams {
  /** 学生ID */
  studentId: number;
  /** 页码 (从1开始) */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
  /** 添加方式筛选 (1:自动添加, 2:手动添加) */
  addMethod?: 1 | 2;
  /** 课程ID筛选 */
  courseId?: number;
}

/**
 * 手动添加错题到错题本的请求参数
 */
export interface ManualAddWrongQuestionPayload {
  /** 学生ID */
  studentId: number;
  /** 题目ID */
  questionId: number;
  /** 错因标签 */
  errorReasonTags: string[];
  /** 学生备注 */
  notes?: string;
}

/**
 * 删除错题的查询参数
 */
export interface DeleteWrongQuestionsParams {
  /** 学生ID */
  studentId: number;
  /** 错题记录ID列表，多个ID用逗号分隔 */
  wrongQuestionIds: string;
}

// ==================== 响应数据类型 ====================

/**
 * 错题记录数据
 */
export interface WrongQuestion {
  /** 错题记录ID */
  wrongQuestionId: number;
  /** 题目ID */
  questionId: number;
  /** 添加方式 (1:自动添加, 2:手动添加) */
  addMethod: 1 | 2;
  /** 错因标签 */
  errorReasonTags: string[];
  /** 添加时间（UTC秒数） */
  addTime: number;
  /** 题目内容 */
  questionContent: string;
}

/**
 * 分页信息
 */
export interface PageInfo {
  /** 当前页码 */
  page: number;
  /** 每页数量 */
  pageSize: number;
  /** 总记录数 */
  total: number;
}

/**
 * 获取错题本列表的响应数据
 */
export interface GetWrongQuestionsResponse {
  /** 错题列表 */
  data: WrongQuestion[];
  /** 分页信息 */
  pageInfo: PageInfo;
}

/**
 * 手动添加错题的响应数据
 */
export interface ManualAddWrongQuestionResponse {
  /** 错题记录ID */
  wrongQuestionId: number;
  /** 添加方式 */
  addMethod: 2;
}

/**
 * 删除错题的响应数据
 */
export interface DeleteWrongQuestionsResponse {
  /** 删除成功的数量 */
  deletedCount: number;
  /** 删除失败的ID列表 */
  failedIds: number[];
}