import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { SWRConfig } from 'swr';
import React from 'react';

// Mock fetcher
vi.mock('@/app/utils/fetcher', () => ({
  get: vi.fn(),
  post: vi.fn(),
}));

// Mock SWR 相关模块
vi.mock('swr', () => ({
  default: vi.fn(),
  SWRConfig: vi.fn(({ children }) => children),
}));

vi.mock('swr/mutation', () => ({
  default: vi.fn()
}));

// Mock fetch for DELETE requests
global.fetch = vi.fn();

// 在所有 vi.mock 调用之后再 import 被测试的模块
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import {
  useWrongQuestions,
  useWrongQuestionsWithRefresh,
  useManualAddWrongQuestion,
  useDeleteWrongQuestions,
} from '../wrong-question-bank-model';

// SWR 测试包装器
const createWrapper = () => {
  return ({ children }: { children: React.ReactNode }) => {
    return React.createElement(SWRConfig, { value: { provider: () => new Map(), dedupingInterval: 0 } }, children);
  };
};

describe('Wrong Question Bank Model - Basic Functionality', () => {
  let mockUseSWR: any;
  let mockUseSWRMutation: any;
  let mockFetcher: any;
  let mockFetch: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    
    // 动态导入Mock模块
    const swr = await import('swr');
    const swrMutation = await import('swr/mutation');
    mockUseSWR = swr.default;
    mockUseSWRMutation = swrMutation.default;
    
    // 导入fetcher
    mockFetcher = await import('@/app/utils/fetcher');
    
    // Mock fetch
    mockFetch = global.fetch as any;
  });

  describe('useWrongQuestions', () => {
    it('should fetch wrong questions when studentId is provided', async () => {
      const mockResponse = {
        data: [
          {
            wrongQuestionId: 6001,
            questionId: 3001,
            addMethod: 1,
            errorReasonTags: ['计算错误', '概念理解不清'],
            addTime: **********,
            questionContent: '下列哪个选项是正确的？',
          },
          {
            wrongQuestionId: 6002,
            questionId: 3002,
            addMethod: 2,
            errorReasonTags: ['需要复习'],
            addTime: 1717488100,
            questionContent: '这道题需要重点复习',
          },
        ],
        pageInfo: {
          page: 1,
          pageSize: 20,
          total: 45,
        },
      };

      // Mock useSWR 返回值
      (mockUseSWR as any).mockReturnValue({
        data: mockResponse,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      const { result } = renderHook(
        () => useWrongQuestions({
          studentId: 1001,
          page: 1,
          pageSize: 20,
          addMethod: 1,
          courseId: 2001,
        }),
        { wrapper: createWrapper() }
      );

      expect(result.current.wrongQuestions).toHaveLength(2);
      expect(result.current.wrongQuestions[0]?.wrongQuestionId).toBe(6001);
      expect(result.current.wrongQuestions[0]?.addMethod).toBe(1);
      expect(result.current.pageInfo?.total).toBe(45);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should not fetch when studentId is missing', () => {
      // Mock useSWR 返回值
      (mockUseSWR as any).mockReturnValue({
        data: undefined,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      const { result } = renderHook(
        () => useWrongQuestions(),
        { wrapper: createWrapper() }
      );

      expect(result.current.wrongQuestions).toEqual([]);
      expect(result.current.pageInfo).toBeUndefined();
      expect(result.current.isLoading).toBe(false);
    });

    it('should handle fetch errors', () => {
      const mockError = new Error('Failed to fetch wrong questions');
      
      // Mock useSWR 返回错误
      (mockUseSWR as any).mockReturnValue({
        data: undefined,
        error: mockError,
        isLoading: false,
        mutate: vi.fn(),
      });

      const { result } = renderHook(
        () => useWrongQuestions({ studentId: 1001 }),
        { wrapper: createWrapper() }
      );

      expect(result.current.wrongQuestions).toEqual([]);
      expect(result.current.error).toEqual(mockError);
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('useWrongQuestionsWithRefresh', () => {
    it('should fetch wrong questions with refresh interval', () => {
      const mockResponse = {
        data: [
          {
            wrongQuestionId: 6001,
            questionId: 3001,
            addMethod: 1,
            errorReasonTags: ['计算错误'],
            addTime: **********,
            questionContent: '测试题目',
          },
        ],
        pageInfo: {
          page: 1,
          pageSize: 20,
          total: 1,
        },
      };

      // Mock useSWR 返回值
      (mockUseSWR as any).mockReturnValue({
        data: mockResponse,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      const { result } = renderHook(
        () => useWrongQuestionsWithRefresh(
          { studentId: 1001, page: 1, pageSize: 20 },
          5000
        ),
        { wrapper: createWrapper() }
      );

      expect(result.current.wrongQuestions).toHaveLength(1);
      expect(result.current.wrongQuestions[0]?.wrongQuestionId).toBe(6001);
      expect(result.current.pageInfo?.total).toBe(1);
    });
  });

  describe('useManualAddWrongQuestion', () => {
    it('should add wrong question manually', async () => {
      const mockResponse = {
        wrongQuestionId: 6003,
        addMethod: 2,
      };

      // Mock useSWRMutation 返回值
      (mockUseSWRMutation as any).mockReturnValue({
        trigger: vi.fn().mockResolvedValue(mockResponse),
        isMutating: false,
        error: null,
      });

      const { result } = renderHook(
        () => useManualAddWrongQuestion(),
        { wrapper: createWrapper() }
      );

      const payload = {
        studentId: 1001,
        questionId: 3001,
        errorReasonTags: ['需要复习'],
        notes: '这道题需要重点复习',
      };

      let addResult: any;
      await act(async () => {
        addResult = await result.current.manualAddWrongQuestion(payload);
      });

      expect(addResult.wrongQuestionId).toBe(6003);
      expect(addResult.addMethod).toBe(2);
      expect(result.current.isAdding).toBe(false);
      expect(result.current.addError).toBeNull();
    });

    it('should handle add wrong question errors', async () => {
      const mockError = new Error('添加失败');

      // Mock useSWRMutation 返回错误
      (mockUseSWRMutation as any).mockReturnValue({
        trigger: vi.fn().mockRejectedValue(mockError),
        isMutating: false,
        error: mockError,
      });

      const { result } = renderHook(
        () => useManualAddWrongQuestion(),
        { wrapper: createWrapper() }
      );

      const payload = {
        studentId: 1001,
        questionId: 3001,
        errorReasonTags: ['需要复习'],
      };

      await act(async () => {
        await expect(result.current.manualAddWrongQuestion(payload)).rejects.toThrow('添加失败');
      });

      expect(result.current.addError).toEqual(mockError);
    });
  });

  describe('useDeleteWrongQuestions', () => {
    it('should delete wrong questions successfully', async () => {
      const mockResponse = {
        deletedCount: 2,
        failedIds: [],
      };

      // Mock fetch 返回值
      (mockFetch as any).mockResolvedValue({
        ok: true,
        json: vi.fn().mockResolvedValue(mockResponse),
      });

      // Mock useSWRMutation 返回值
      (mockUseSWRMutation as any).mockReturnValue({
        trigger: vi.fn().mockResolvedValue(mockResponse),
        isMutating: false,
        error: null,
      });

      const { result } = renderHook(
        () => useDeleteWrongQuestions(),
        { wrapper: createWrapper() }
      );

      const payload = {
        studentId: 1001,
        wrongQuestionIds: '6001,6002',
      };

      let deleteResult: any;
      await act(async () => {
        deleteResult = await result.current.deleteWrongQuestions(payload);
      });

      expect(deleteResult.deletedCount).toBe(2);
      expect(deleteResult.failedIds).toEqual([]);
      expect(result.current.isDeleting).toBe(false);
      expect(result.current.deleteError).toBeNull();
    });

    it('should handle delete errors', async () => {
      const mockError = new Error('删除失败');

      // Mock useSWRMutation 返回错误
      (mockUseSWRMutation as any).mockReturnValue({
        trigger: vi.fn().mockRejectedValue(mockError),
        isMutating: false,
        error: mockError,
      });

      const { result } = renderHook(
        () => useDeleteWrongQuestions(),
        { wrapper: createWrapper() }
      );

      const payload = {
        studentId: 1001,
        wrongQuestionIds: '6001,6002',
      };

      await act(async () => {
        await expect(result.current.deleteWrongQuestions(payload)).rejects.toThrow('删除失败');
      });

      expect(result.current.deleteError).toEqual(mockError);
    });

    it('should handle HTTP errors in delete operation', async () => {
      // Mock fetch 返回 HTTP 错误
      (mockFetch as any).mockResolvedValue({
        ok: false,
        status: 404,
      });

      // Mock useSWRMutation 抛出错误
      (mockUseSWRMutation as any).mockReturnValue({
        trigger: vi.fn().mockRejectedValue(new Error('HTTP error! status: 404')),
        isMutating: false,
        error: new Error('HTTP error! status: 404'),
      });

      const { result } = renderHook(
        () => useDeleteWrongQuestions(),
        { wrapper: createWrapper() }
      );

      const payload = {
        studentId: 1001,
        wrongQuestionIds: '6001',
      };

      await act(async () => {
        await expect(result.current.deleteWrongQuestions(payload)).rejects.toThrow('HTTP error! status: 404');
      });
    });
  });

  describe('Loading states', () => {
    it('should show loading state for wrong questions fetch', () => {
      // Mock useSWR 返回加载状态
      (mockUseSWR as any).mockReturnValue({
        data: undefined,
        error: null,
        isLoading: true,
        mutate: vi.fn(),
      });

      const { result } = renderHook(
        () => useWrongQuestions({ studentId: 1001 }),
        { wrapper: createWrapper() }
      );

      expect(result.current.isLoading).toBe(true);
      expect(result.current.wrongQuestions).toEqual([]);
    });

    it('should show mutating state for add operation', () => {
      // Mock useSWRMutation 返回加载状态
      (mockUseSWRMutation as any).mockReturnValue({
        trigger: vi.fn(),
        isMutating: true,
        error: null,
      });

      const { result } = renderHook(
        () => useManualAddWrongQuestion(),
        { wrapper: createWrapper() }
      );

      expect(result.current.isAdding).toBe(true);
    });

    it('should show mutating state for delete operation', () => {
      // Mock useSWRMutation 返回加载状态
      (mockUseSWRMutation as any).mockReturnValue({
        trigger: vi.fn(),
        isMutating: true,
        error: null,
      });

      const { result } = renderHook(
        () => useDeleteWrongQuestions(),
        { wrapper: createWrapper() }
      );

      expect(result.current.isDeleting).toBe(true);
    });
  });
});