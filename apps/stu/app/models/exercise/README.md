# Exercise Model 层

## 概述
练习模块的数据模型层，负责与后端API交互，遵循MVVM架构模式。

## 🔥 重要更新：API参数补全 (2024)

### next_question 接口参数变更

#### 新增必填参数
根据最新的后端接口定义，`/api/v1/study_session/next_question` 接口现在需要以下必填参数：

1. **knowledgeId** (number) - 知识点ID，必须
2. **phaseId** (number) - 学段ID，必须  
3. **subjectId** (number) - 科目ID，必须
4. **textbookId** (number) - 教材ID，必须
5. **studyType** (number) - 学习类型，必须

#### 可选参数
- **lessonId** (number) - 课程ID，AI课必须
- **questionSetId** (number) - 题集ID，巩固练习需提供
- **studySessionId** (number) - 会话ID，巩固练习需提供，AI课首次请求不需要
- **widgetIndex** (number) - 组件序号，AI课必须

### 🔥 类型安全的参数管理

#### ExerciseUrlParams 联合类型
使用TypeScript联合类型确保不同学习类型的参数正确性：

```typescript
export type ExerciseUrlParams = {
  knowledgeId: number; // 知识点ID，必须
  phaseId: number; // 学段ID，必须
  subjectId: number; // 科目ID，必须
  textbookId: number; // 教材ID，必须
} & (
  // 巩固练习参数
  {
    studyType: StudyType.REINFORCEMENT_EXERCISE;
    studySessionId: number;
    questionSetId?: number;
  } |
  // 拓展练习参数
  {
    studyType: StudyType.EXPAND_EXERCISE;
    studySessionId: number;
    questionSetId?: number;
  } |
  // AI课参数
  {
    studyType: StudyType.AI_COURSE;
    lessonId: number;
    widgetIndex: number;
  }
)
```

#### 智能参数构建
根据`studyType`自动构建正确的参数结构：

```typescript
// AI课参数
if (urlParams.studyType === 1) {
  apiParams.lessonId = urlParams.lessonId.toString();
  apiParams.widgetIndex = urlParams.widgetIndex.toString();
} 
// 巩固练习或拓展练习参数
else if (urlParams.studyType === 2 || urlParams.studyType === 3) {
  apiParams.studySessionId = urlParams.studySessionId.toString();
  if (urlParams.questionSetId) {
    apiParams.questionSetId = urlParams.questionSetId.toString();
  }
}
```

#### URL示例

**巩固练习**：
```
/exercise?studySessionId=123&studyType=2&knowledgeId=456&phaseId=1&subjectId=2&textbookId=789&questionSetId=101
```

**AI课练习**：
```
/api/v1/study_session/next_question?knowledgeId=456&lessonId=123&widgetIndex=0&studyType=1&phaseId=1&subjectId=2&textbookId=789
```

## 核心功能

### useGetNextQuestion Hook
- **路径**: `exercise-model.ts`
- **职责**: 获取下一题数据，支持多种学习模式
- **特点**:
  - 🔥 类型安全的参数处理
  - 支持直接模式和URL模式
  - 自动参数完整性检查
  - 详细的错误信息和调试日志

### 数据转换器
- **路径**: `transformers.ts`
- **职责**: API响应数据格式转换
- **特点**: 统一的数据格式，适配前端组件需求

### 类型定义
- **路径**: `types.ts`
- **职责**: 完整的TypeScript类型定义
- **特点**: 
  - 🔥 ExerciseUrlParams联合类型确保参数正确性
  - 支持所有必填和可选参数
  - 类型安全的参数验证

## 最佳实践

1. **参数传递**: 优先使用URL参数，确保页面刷新后状态保持
2. **类型安全**: 使用联合类型确保不同学习类型的参数正确性
3. **错误处理**: 检查API返回的错误信息，提供用户友好的提示
4. **性能优化**: 使用SWR缓存，避免重复请求
5. **调试支持**: 开启console日志，便于问题排查