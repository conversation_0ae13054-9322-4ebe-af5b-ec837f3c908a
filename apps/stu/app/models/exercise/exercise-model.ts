"use client";

import { get, post } from "@/app/utils/fetcher";
import { StudyType } from "@repo/core/enums";
import {
  ApiGetNextQuestionData,
  SubmitAnswerPayload,
  SubmitAnswerResponse,
} from "@repo/core/exercise/model/types";
import useSWRMutation from "swr/mutation";
// ApiGetNextQuestionData 已从 types 导入
import { toast } from "@repo/core/components/stu-toast";

/**
 * 统一的错误处理方法
 *
 * 提取错误对象中的错误消息，支持多层嵌套的错误结构
 *
 * @param error 错误对象
 * @param defaultMessage 默认错误消息
 * @returns 提取的错误消息
 */

function handleApiError(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  error: any,
  defaultMessage: string = "网络请求失败"
): string {
  return error?.message?.message || error?.message || defaultMessage;
}

/**
 * 提交题目答案并获取反馈
 *
 * 学生提交题目答案，系统判断答案正确性，记录作答详情，提供即时的情感化反馈。
 * 包含正确/错误/连胜状态反馈，并自动推进到下一题或完成练习。
 *
 * @returns SWR Mutation Hook 返回值
 */
export function useSubmitStudyAnswer() {
  const { trigger, isMutating, error } = useSWRMutation(
    "/api/v1/study_session/submit_answer",
    async (url, { arg }: { arg: SubmitAnswerPayload }) => {
      try {
        const response = await post<SubmitAnswerResponse>(url, { arg });
        const transformedResponse = {
          ...response,
          nextQuestionInfo: {
            ...response.nextQuestionInfo,
            questionTags: response.nextQuestionTags || [],
          },
        };
        return transformedResponse as SubmitAnswerResponse;
      } catch (err) {
        toast.error(handleApiError(err));
        throw err;
      }
    }
  );

  return {
    submitAnswer: trigger,
    isSubmitting: isMutating,
    submitError: error,
  };
}

/**
 * 获取下一题（仅手动触发模式）
 *
 * 🔧 严格的手动触发模式：
 * - 绝对不会自动调用API
 * - 只返回传入的 firstQuestionData（初始题目）
 * - 所有后续题目通过 useSubmitStudyAnswer 获取
 *
 * @param params 配置参数
 * @returns 手动触发Hook返回值
 */
export function useGetNextQuestion(params: {
  firstQuestionData?: ApiGetNextQuestionData;
  studyType: StudyType;
  studySessionId: number;
}) {
  // 🔥 关键：使用 useSWRMutation，但绝不自动触发
  const { trigger, isMutating, error } = useSWRMutation(
    "/api/v1/study_session/next_question",
    async (url) => {
      // console.log(`[useGetNextQuestion] 手动触发API请求`, { url, params });

      // 🔧 判断是否需要发起请求
      const shouldFetch = params.studyType !== StudyType.AI_COURSE;

      if (!shouldFetch) {
        // AI课程不需要调用API，直接返回首题数据
        return params.firstQuestionData || null;
      }

      // 构建API参数
      const apiParams: Record<string, string> = {
        studySessionId: params.studySessionId.toString(),
        studyType: params.studyType.toString(),
      };

      // 从URL中提取所有参数
      if (typeof window !== "undefined") {
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.forEach((value, key) => {
          apiParams[key] = value;
        });
      }

      try {
        const response = await get<ApiGetNextQuestionData>(url, {
          query: apiParams,
        });
        const transformedResponse = {
          ...response,
          questionInfo: {
            ...response.questionInfo,
            questionTags: response.questionTags || [],
          },
        };
        return transformedResponse as ApiGetNextQuestionData;
      } catch (error) {
        toast.error(handleApiError(error));
        throw error;
      }
    }
  );

  // 🔥 关键变更：只返回 firstQuestionData，绝不返回任何API获取的data
  // 这确保了只有初始数据会被使用，后续所有数据都来自submit接口
  return {
    questionInfo: params.firstQuestionData || null, // 🔥 移除 || data，确保不会使用API数据
    error: error,
    isLoading: isMutating,
    trigger, // �� 手动获取方法（备用）
  };
}

/**
 * 退出练习会话请求参数
 */
export interface ExitSessionParams {
  studySessionId: number;
  questionId?: string; // 当前题目ID，用于保存答题进度
  answerDuration?: number; // 当前题目已答题时长（毫秒），用于恢复计时器
  widgetIndex?: number; // 组件索引，用于标识当前练习组件位置
}
/**
 * 退出练习会话
 *
 * 学生中途退出练习时调用此接口，系统自动保存当前进度。
 * 支持缓存和数据库双重保障，确保进度不丢失。
 *
 * @returns SWR Mutation Hook 返回值
 */
export function useExitStudySession() {
  const { trigger, isMutating, error } = useSWRMutation(
    "/api/v1/study_session/exit",
    async (url, { arg }: { arg: ExitSessionParams }) => {
      console.log("[useExitStudySession] 退出练习会话:", arg);
      // 退出接口使用 POST 方法，参数通过 body 传递
      const response = await post(url, { arg });
      return response;
    }
  );

  return {
    exitSession: trigger,
    isExiting: isMutating,
    exitError: error,
  };
}

/**
 * 校验图片答案
 *
 * 用于校验学生提交的图片答案是否合法,包括:
 * - 图片格式检查
 * - 图片内容审核
 * - 图片答案预判断
 *
 * @returns SWR Mutation Hook 返回值
 */
export interface CheckAnswerPictureRequest {
  pictureUrls: string[];
}

export interface CheckAnswerPictureResponse {
  isPass: boolean;
}

export function useCheckAnswerPicture() {
  const { trigger, isMutating, error } = useSWRMutation(
    "/api/v1/study_session/answer/picture/check",
    async (url, { arg }: { arg: CheckAnswerPictureRequest }) => {
      const response = await post<CheckAnswerPictureResponse>(url, { arg });
      return response;
    }
  );

  return {
    checkPicture: trigger,
    isChecking: isMutating,
    checkError: error,
  };
}

// 定义问一问接口的返回类型
interface AskQuestionResponse {
  isShow: boolean;
}

export function useAskQuestion(params: { studySessionId: number }) {
  const { trigger, isMutating, error, data } = useSWRMutation(
    "/api/v1/lesson/qa/is_show",
    async (url) => {
      const response = await get<AskQuestionResponse>(url, {
        query: { studySessionId: params.studySessionId.toString() },
        prefix: "/study-support-api",
      });
      return response;
    }
  );

  return {
    askQuestion: trigger,
    isAsking: isMutating,
    askError: error,
    askData: data,
  };
}
