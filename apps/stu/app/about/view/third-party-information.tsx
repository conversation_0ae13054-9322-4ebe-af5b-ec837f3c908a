"use client";

import { getStatusBarHeight } from "@/app/utils/device";
import React, { useRef } from "react";

interface ThirdPartyInformationProps {
  onBack: () => void;
}

// 第三方信息共享清单内容
const ThirdPartyInformationContent = () => (
  <div className="space-y-6 text-sm">
    <div className="space-y-4">
      <section>
        <div className="space-y-4">
          <div className="rounded-lg border border-gray-200 p-4">
            <h3 className="mb-3 font-medium text-gray-900">
              1. 腾讯TBS浏览器内核
            </h3>
            <div className="space-y-2 text-sm text-gray-700">
              <p>
                <strong>涉及个人信息：</strong>
                设备信息（设备型号、操作系统、CPU类型）、应用信息（宿主应用包名，版本号）、Wi-Fi状态和参数（不包含wifi
                mac）、IP地址、AndroidID
              </p>
              <p>
                <strong>使用目的：</strong>
                为用户提供更好的Webview内核，提供更好的网页浏览服务
              </p>
              <p>
                <strong>使用场景：</strong>在用户浏览 Web 内容时使用
              </p>
              <p>
                <strong>合作方主体：</strong>深圳市腾讯计算机系统有限公司
              </p>
              <p>
                <strong>收集方式：</strong>SDK 自行采集
              </p>
              <p>
                <strong>官网链接：</strong>
                <a
                  href="https://x5.tencent.com/tbs.html"
                  className="text-blue-600"
                >
                  https://x5.tencent.com/tbs.html
                </a>
              </p>
              <p>
                <strong>合作方隐私政策：</strong>
                <a
                  href="https://rule.tencent.com/rule/1c4e2b4b-d0f6-4a75-a5c6-1cfce00a390d"
                  className="text-blue-600"
                >
                  查看详情
                </a>
              </p>
            </div>
          </div>

          <div className="rounded-lg border border-gray-200 p-4">
            <h3 className="mb-3 font-medium text-gray-900">
              2. 火山引擎语音识别
            </h3>
            <div className="space-y-2 text-sm text-gray-700">
              <p>
                <strong>涉及个人信息：</strong>
                音频内容，设备品牌（系统属性）、设备型号（系统属性）、操作系统（系统属性）、操作系统api版本（系统属性）、user
                agent、CPU信息（频率、型号、架构）、应用版本、IP地址、网络访问模式（WIFI状态）
              </p>
              <p>
                <strong>使用目的：</strong>语音转文字功能
              </p>
              <p>
                <strong>使用场景：</strong>问一问语音转文字输入
              </p>
              <p>
                <strong>合作方主体：</strong>
                北京火山引擎科技有限公司、北京抖音信息服务有限公司
              </p>
              <p>
                <strong>收集方式：</strong>SDK 自行采集
              </p>
              <p>
                <strong>官网链接：</strong>
                <a href="https://www.volcengine.com/" className="text-blue-600">
                  https://www.volcengine.com/
                </a>
              </p>
              <p>
                <strong>合作方隐私政策：</strong>
                <a
                  href="https://www.volcengine.com/docs/6561/108794"
                  className="text-blue-600"
                >
                  查看详情
                </a>
              </p>
            </div>
          </div>

          <div className="rounded-lg border border-gray-200 p-4">
            <h3 className="mb-3 font-medium text-gray-900">3. 神策数据统计</h3>
            <div className="space-y-2 text-sm text-gray-700">
              <p>
                <strong>涉及个人信息：</strong>设备标识符（Android
                ID）、网络信息（网络类型、运营商名称、IP
                地址）、设备硬件信息（设备型号、设备屏幕分辨率、设备硬件制造商、设备产品名称）、操作系统信息（系统名称、系统版本、系统语言）、应用信息（时区、应用
                app_id、应用名称、应用版本号）
              </p>
              <p>
                <strong>使用目的：</strong>基础数据收集与建模，驱动业务决策分析
              </p>
              <p>
                <strong>使用场景：</strong>业务数据搜集、APP性能数据搜集
              </p>
              <p>
                <strong>合作方主体：</strong>神策网络科技（北京）有限公司
              </p>
              <p>
                <strong>收集方式：</strong>SDK 自行采集
              </p>
              <p>
                <strong>官网链接：</strong>
                <a href="https://www.sensorsdata.cn/" className="text-blue-600">
                  https://www.sensorsdata.cn/
                </a>
              </p>
              <p>
                <strong>合作方隐私政策：</strong>
                <a
                  href="https://manual.sensorsdata.cn/sa/docs/tech_sdk_client_privacy_policy/v0300"
                  className="text-blue-600"
                >
                  查看详情
                </a>
              </p>
            </div>
          </div>

          <div className="rounded-lg border border-gray-200 p-4">
            <h3 className="mb-3 font-medium text-gray-900">4. 友盟APM</h3>
            <div className="space-y-2 text-sm text-gray-700">
              <p>
                <strong>涉及个人信息：</strong>设备信息（Android ID）、网络信息
              </p>
              <p>
                <strong>使用目的：</strong>APP 运行稳定性监控、Crash
                问题上报分析
              </p>
              <p>
                <strong>使用场景：</strong>APP 稳定性监控、性能监控
              </p>
              <p>
                <strong>合作方主体：</strong>
                友盟同欣（北京）科技有限公司、北京锐讯灵通科技有限公司
              </p>
              <p>
                <strong>收集方式：</strong>SDK 自行采集
              </p>
              <p>
                <strong>官网链接：</strong>
                <a href="https://www.umeng.com/apm" className="text-blue-600">
                  https://www.umeng.com/apm
                </a>
              </p>
              <p>
                <strong>合作方隐私政策：</strong>
                <a
                  href="https://www.umeng.com/page/policy"
                  className="text-blue-600"
                >
                  查看详情
                </a>
              </p>
            </div>
          </div>

          <div className="rounded-lg border border-gray-200 p-4">
            <h3 className="mb-3 font-medium text-gray-900">5. 阿里云推送</h3>
            <div className="space-y-2 text-sm text-gray-700">
              <p>
                <strong>涉及个人信息：</strong>设备信息（品牌、型号、系统版本）
              </p>
              <p>
                <strong>使用目的：</strong>
                用于识别推送设备，实现向单个设备推送功能
              </p>
              <p>
                <strong>使用场景：</strong>APP 消息推送、站内信通知
              </p>
              <p>
                <strong>合作方主体：</strong>杭州阿里云智能科技有限公司
              </p>
              <p>
                <strong>收集方式：</strong>SDK 自行采集
              </p>
              <p>
                <strong>官网链接：</strong>
                <a
                  href="https://help.aliyun.com/document_detail/2584336.html"
                  className="text-blue-600"
                >
                  https://help.aliyun.com/document_detail/2584336.html
                </a>
              </p>
              <p>
                <strong>合作方隐私政策：</strong>
                <a
                  href="https://terms.aliyun.com/legal-agreement/terms/suit_bu1_ali_cloud/suit_bu1_ali_cloud202112071754_83380.html?spm=a2c4g.11186623.0.0.70a73aa5JjsHrc"
                  className="text-blue-600"
                >
                  查看详情
                </a>
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
);

export const ThirdPartyInformationView: React.FC<
  ThirdPartyInformationProps
> = ({ onBack: _onBack }) => {
  const statusBarHeight = useRef(getStatusBarHeight());

  return (
    <div
      className="relative flex h-screen flex-col bg-[#F7F6F5]"
      style={{
        paddingTop: statusBarHeight.current + "px",
      }}
    >
      <div className="font-resource-han-rounded flex-1 overflow-y-auto px-8 py-8">
        <ThirdPartyInformationContent />
      </div>
    </div>
  );
};
