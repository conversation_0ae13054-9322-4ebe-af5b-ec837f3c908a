"use client";

import { getStatusBarHeight } from "@/app/utils/device";
import React, { useRef } from "react";

interface PermissionRequestProps {
  onBack: () => void;
}

// 权限申请说明内容
const PermissionRequestContent = () => (
  <div className="space-y-6 text-sm">
    <div className="space-y-4">
      <section>
        <div className="space-y-2 leading-relaxed text-gray-700">
          <p>
            为保障小鹿爱学的安全稳定运行，我们可能申请或使用操作系统的相关权限；为保障你的知情权，我们通过下列列表将产品可能申请、使用的相关操作系统权限进行展示，你可以根据实际需要对相关权限进行管理；权限的类型与目的可能随产品升级变动，我们会及时调整列表以便你知悉。
          </p>
        </div>
      </section>

      <section>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-300 bg-white text-sm">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                  权限名称
                </th>
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                  权限功能说明
                </th>
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                  使用场景或目的说明
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  android.permission.INTERNET
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  访问网络
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  用于网络请求、API调用、在线内容访问
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  android.permission.ACCESS_NETWORK_STATE
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  查看网络状态
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  检测网络连接状态，优化网络请求策略
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  android.permission.ACCESS_WIFI_STATE
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  查看WiFi状态
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  获取WiFi连接状态，网络诊断和优化
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  android.permission.READ_EXTERNAL_STORAGE
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  允许读取外部存储文件
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  访问用户照片、视频、文档等文件，用于帮助用户发布信息、上传文件
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  android.permission.WRITE_EXTERNAL_STORAGE
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  允许写入外部存储文件
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  保存下载内容、缓存文件、用户生成内容
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  android.permission.READ_MEDIA_IMAGES
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  允许访问图片媒体文件
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  允许读取存储中的图片文件，用于帮助用户发布信息、上传图片
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  android.permission.READ_MEDIA_VIDEO
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  允许访问视频媒体文件
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  允许读取存储中的视频文件，用于帮助用户发布信息、上传视频
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  android.permission.READ_MEDIA_AUDIO
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  允许访问音频媒体文件
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  允许读取存储中的音频文件，用于帮助用户发布信息、上传音频
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  android.permission.RECORD_AUDIO
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  允许应用程序录制音频
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  语音录制、语音识别、音频通话功能
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  android.permission.CAMERA
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  允许应用程序访问摄像头
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  拍照、视频录制、扫码、视频通话
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  android.permission.SYSTEM_ALERT_WINDOW
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  允许在其他应用之上显示窗口
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  悬浮窗、画中画、系统级通知显示
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>
    </div>
  </div>
);

export const PermissionRequestView: React.FC<PermissionRequestProps> = ({
  onBack: _onBack,
}) => {
  const statusBarHeight = useRef(getStatusBarHeight());

  return (
    <div
      className="relative flex h-screen flex-col bg-[#F7F6F5]"
      style={{
        paddingTop: statusBarHeight.current + "px",
      }}
    >
      <div className="font-resource-han-rounded flex-1 overflow-y-auto px-8 py-8">
        <PermissionRequestContent />
      </div>
    </div>
  );
};
