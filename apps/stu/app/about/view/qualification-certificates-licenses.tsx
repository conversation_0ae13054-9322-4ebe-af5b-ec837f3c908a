"use client";

import { getStatusBarHeight } from "@/app/utils/device";
import Image from "next/image";
import React, { useRef } from "react";

interface QualificationCertificatesLicensesProps {
  onBack: () => void;
}

// 资质证照公示内容
const QualificationCertificatesLicensesContent = () => (
  <div className="space-y-6 text-sm">
    <section>
      <div className="flex justify-center">
        <Image
          src="https://static.xiaoluxue.com/agreements/yzzz.png"
          alt="资质证照"
          width={800}
          height={600}
          className="h-auto max-w-full rounded-lg shadow-md"
          style={{ maxHeight: "80vh" }}
        />
      </div>
    </section>
  </div>
);

export const QualificationCertificatesLicensesView: React.FC<
  QualificationCertificatesLicensesProps
> = ({ onBack: _onBack }) => {
  const statusBarHeight = useRef(getStatusBarHeight());

  return (
    <div
      className="relative flex h-screen flex-col bg-[#F7F6F5]"
      style={{
        paddingTop: statusBarHeight.current + "px",
      }}
    >
      <div className="font-resource-han-rounded flex-1 overflow-y-auto px-8 py-8">
        <QualificationCertificatesLicensesContent />
      </div>
    </div>
  );
};
