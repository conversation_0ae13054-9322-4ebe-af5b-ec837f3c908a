"use client";

import { getStatusBarHeight } from "@/app/utils/device";
import React, { useRef } from "react";

interface PersonalInformationProps {
  onBack: () => void;
}

// 个人信息收集清单内容
const PersonalInformationContent = () => (
  <div className="space-y-6 text-sm">
    <div className="space-y-4">
      <section>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-300 bg-white text-sm">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                  信息类型
                </th>
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                  信息字段
                </th>
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                  使用场景和目的
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td
                  className="whitespace-nowrap border border-gray-300 px-4 py-3 align-top font-medium text-gray-900"
                  rowSpan={5}
                >
                  用户基本信息
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  姓名
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  用于账号信息展示、为用户提供课程服务
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  学校
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  用于账号信息展示、为用户提供课程服务
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  班级
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  用于账号信息展示、为用户提供课程服务
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  年级
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  用于账号信息展示、为用户提供课程服务
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  手机号
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  用于注册账号、登录产品
                </td>
              </tr>
              <tr>
                <td
                  className="whitespace-nowrap border border-gray-300 px-4 py-3 align-top font-medium text-gray-900"
                  rowSpan={2}
                >
                  用户使用信息
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  上课情况、学习时长、答题情况
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  提供课程服务，有针对性提供学习课程、资料和内容及学情分析。
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  评论及其他发布的内容
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  用于课堂、师生互动
                </td>
              </tr>
              <tr>
                <td className="whitespace-nowrap border border-gray-300 px-4 py-3 align-top font-medium text-gray-900">
                  用户设备信息
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  硬件型号、操作系统版本号、设备标识符、网络设备硬件地址、蓝牙、基站、软件版本号、网络接入方式、类型、状态、网络质量数据
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  问题排查、运营与安全保障
                </td>
              </tr>
              <tr>
                <td className="whitespace-nowrap border border-gray-300 px-4 py-3 align-top font-medium text-gray-900">
                  日志信息
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  操作、使用、服务日志
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  问题排查、运营与安全保障
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>
    </div>
  </div>
);

export const PersonalInformationView: React.FC<PersonalInformationProps> = ({
  onBack: _onBack,
}) => {
  const statusBarHeight = useRef(getStatusBarHeight());

  return (
    <div
      className="relative flex h-screen flex-col bg-[#F7F6F5]"
      style={{
        paddingTop: statusBarHeight.current + "px",
      }}
    >
      <div className="font-resource-han-rounded flex-1 overflow-y-auto px-8 py-8">
        <PersonalInformationContent />
      </div>
    </div>
  );
};
