"use client";

import { FeedbackType, StudyType } from "@repo/core/enums";
import { StudyTypeThemeProvider } from "@repo/core/exercise";
import { useState } from "react";
import useScreen from "../../hooks/use-screen";
import { useTransitionViewModel } from "../../viewmodels/exercise/transition-viewmodel";
import { TransitionView } from "../../views/exercise/transition-view";

export default function FeedbackDemoPage() {
  const screen = useScreen();

  // 🔥 新的转场系统
  const transitionViewModel = useTransitionViewModel();
  const {
    transitionState,
    executeTransitionSequence,
    nextTransition,
    clearTransitionRecords,
    resetTransitionSystem,
  } = transitionViewModel;

  // 🎯 新增：题组模拟状态
  const [groupSimulation, setGroupSimulation] = useState({
    totalGroups: 5,
    currentGroupIndex: 0,
    groupNames: [
      "分式运算基础",
      "分式方程应用",
      "分式不等式",
      "综合应用题",
      "压轴挑战题",
    ],
  });

  // 切换到下一个题组
  const switchToNextGroup = async () => {
    const nextIndex =
      (groupSimulation.currentGroupIndex + 1) % groupSimulation.totalGroups;
    const nextGroupName = groupSimulation.groupNames[nextIndex] || "新题组";

    console.log("[Demo] 题组切换:", {
      currentIndex: groupSimulation.currentGroupIndex,
      nextIndex,
      currentGroupName:
        groupSimulation.groupNames[groupSimulation.currentGroupIndex],
      nextGroupName,
      totalGroups: groupSimulation.totalGroups,
    });

    // 显示题组转场
    await executeTransitionSequence({
      groupInfo: {
        groupName: nextGroupName,
        groupIndex: nextIndex + 1,
        groupCount: groupSimulation.totalGroups,
        groupQuestionCount: 10,
        preGroupName:
          groupSimulation.groupNames[groupSimulation.currentGroupIndex],
      },
    });

    // 更新题组状态
    setGroupSimulation((prev) => ({
      ...prev,
      currentGroupIndex: nextIndex,
    }));

    console.log("[Demo] 题组转场完成");
  };

  // 获取设备类型
  const getDeviceType = () => {
    const { width } = screen;
    if (width <= 768) return "移动设备";
    if (width <= 1024) return "平板设备";
    return "桌面设备";
  };

  // 获取设备信息
  const getDeviceInfo = () => {
    const { width, height } = screen;
    const deviceType = getDeviceType();
    const aspectRatio = (width / height).toFixed(2);

    return {
      deviceType,
      width,
      height,
      aspectRatio,
      userAgent:
        typeof window !== "undefined" ? window.navigator.userAgent : "Unknown",
    };
  };

  const deviceInfo = getDeviceInfo();

  // 显示单个反馈
  const showFeedback = async (
    type: FeedbackType,
    title: string,
    content: string
  ) => {
    console.log("[Demo] 显示单个反馈:", { type, title, content });

    await executeTransitionSequence({
      specialFeedbacks: [
        {
          type,
          title,
          content,
        },
      ],
    });

    console.log("[Demo] 单个反馈播放完成");
  };

  // 组合转场演示
  const showMultipleFeedbacks = async () => {
    console.log("[Demo] 组合转场演示开始");

    clearTransitionRecords();
    await executeTransitionSequence({
      groupInfo: {
        groupName: "新的题组",
        groupIndex: groupSimulation.currentGroupIndex + 1,
        groupCount: groupSimulation.totalGroups,
        groupQuestionCount: 10,
      },
      specialFeedbacks: [
        {
          type: FeedbackType.AnswerCarelessly,
          title: "别气馁",
          content: "试着自己看看解析",
        },
        {
          type: FeedbackType.ContinuousCorrect,
          title: "连续正确！",
          content: "太棒了！连续答对3题！",
        },
      ],
    });

    console.log("[Demo] 组合转场演示完成");
  };

  const feedbackTypes: {
    type: FeedbackType;
    label: string;
    title: string;
    content: string;
    bgColor: string;
    description: string;
    autoCloseDelay?: number;
  }[] = [
    {
      type: FeedbackType.ContinuousCorrect,
      label: "连续正确反馈",
      title: "连续正确！",
      content: "你太棒了！连续答对多题！",
      bgColor: "bg-[#8B5CF6] hover:bg-[#7C3AED]",
      description: "连续多次正确答案的特殊反馈动效",
    },
    {
      type: FeedbackType.AnswerCarelessly,
      label: "粗心反馈",
      title: "认真作答",
      content: "有助于掌握度提升～",
      bgColor: "bg-[#F97316] hover:bg-[#EA580C]",
      description: "粗心错误时的反馈动效",
    },
    {
      type: FeedbackType.DifficultyUp,
      label: "难度升级反馈",
      title: "难度升级！",
      content: "迎接挑战吧！",
      bgColor: "bg-[#EC4899] hover:bg-[#DB2777]",
      description: "难度升级时的特殊反馈动效",
    },
    {
      type: FeedbackType.DifficultyDown,
      label: "难度下降反馈",
      title: "难度下降",
      content: "继续加油！",
      bgColor: "bg-[#0EA5E9] hover:bg-[#0284C7]",
      description: "难度下降时的特殊反馈动效",
    },
    {
      type: FeedbackType.Resume,
      label: "恢复练习转场动画",
      title: "空间几何~",
      content: "让我们继续练习",
      bgColor: "bg-[#6366F1] hover:bg-[#4F46E5]",
      description: "恢复练习转场动画",
      autoCloseDelay: 1400,
    },
  ];

  // 题组转场动画数据
  const groupTransitionData = {
    label: "题组转场动画",
    title: "新的题组",
    content: "让我们开始新的挑战",
    bgColor: "bg-[#14B8A6] hover:bg-[#0D9488]",
    description: "切换题组时的转场动画",
  };

  return (
    <div className="font-resource-han-rounded min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      {/* <FeedbackResourcePreloader /> */}
      {/* <FeedbackAnimationPreheater /> */}
      <div className="mx-auto max-w-4xl">
        {/* 页面标题 */}
        <div className="mb-12 text-center">
          <h1 className="mb-4 text-4xl font-bold text-gray-800">
            答题反馈动效展示
          </h1>
          <p className="text-lg text-gray-600">
            点击下方按钮查看不同类型的答题反馈动效和转场动画
          </p>
        </div>

        {/* 反馈类型网格 */}
        <div className="mb-12 grid grid-cols-1 gap-8 md:grid-cols-2">
          {feedbackTypes.map((feedback) => (
            <div
              key={feedback.type}
              className="rounded-2xl bg-white p-6 shadow-lg transition-shadow duration-300 hover:shadow-xl"
            >
              <div className="mb-4">
                <h3 className="mb-2 text-xl font-semibold text-gray-800">
                  {feedback.label}
                </h3>
                <p className="text-sm text-gray-600">{feedback.description}</p>
              </div>

              <div className="mb-4 rounded-lg bg-gray-50 p-4">
                <div className="text-sm text-gray-700">
                  <div>
                    <strong>主文字：</strong>
                    {feedback.title}
                  </div>
                  <div>
                    <strong>鼓励文字：</strong>
                    {feedback.content}
                  </div>
                </div>
              </div>

              <button
                onClick={() =>
                  showFeedback(feedback.type, feedback.title, feedback.content)
                }
                className={`w-full rounded-lg px-6 py-3 font-medium text-white transition-colors duration-200 ${feedback.bgColor}`}
                disabled={transitionState.isPlayingTransitions}
              >
                {transitionState.isPlayingTransitions
                  ? "动效播放中..."
                  : `查看${feedback.label}`}
              </button>
            </div>
          ))}

          {/* 题组转场动画单独处理 */}
          <div className="rounded-2xl bg-white p-6 shadow-lg transition-shadow duration-300 hover:shadow-xl">
            <div className="mb-4">
              <h3 className="mb-2 text-xl font-semibold text-gray-800">
                {groupTransitionData.label}
              </h3>
              <p className="text-sm text-gray-600">
                {groupTransitionData.description}
              </p>
            </div>

            <div className="mb-4 rounded-lg bg-gray-50 p-4">
              <div className="text-sm text-gray-700">
                <div>
                  <strong>主文字：</strong>
                  {groupTransitionData.title}
                </div>
                <div>
                  <strong>鼓励文字：</strong>
                  {groupTransitionData.content}
                </div>
              </div>
            </div>

            <button
              onClick={switchToNextGroup}
              className={`w-full rounded-lg px-6 py-3 font-medium text-white transition-colors duration-200 ${groupTransitionData.bgColor}`}
              disabled={transitionState.isPlayingTransitions}
            >
              {transitionState.isPlayingTransitions
                ? "动效播放中..."
                : `查看${groupTransitionData.label}`}
            </button>
          </div>
        </div>

        {/* 组合转场演示 */}
        <div className="mb-8 rounded-2xl bg-white p-6 shadow-lg">
          <h3 className="mb-4 flex items-center text-xl font-semibold text-gray-800">
            <span className="mr-3 h-3 w-3 rounded-full bg-[#8B5CF6]"></span>
            组合转场演示
          </h3>
          <p className="mb-4 text-gray-600">
            演示多个转场按顺序自动播放的效果，模拟真实答题场景中的连续转场
          </p>
          <button
            onClick={showMultipleFeedbacks}
            className="w-full rounded-lg bg-[#8B5CF6] px-6 py-3 font-medium text-white transition-colors duration-200 hover:bg-[#7C3AED]"
            disabled={transitionState.isPlayingTransitions}
          >
            {transitionState.isPlayingTransitions
              ? "组合转场播放中..."
              : "组合转场演示 (题组转场 → 特殊反馈)"}
          </button>
        </div>

        {/* 题组模拟切换 */}
        <div className="mb-8 rounded-2xl bg-white p-6 shadow-lg">
          <h3 className="mb-4 flex items-center text-xl font-semibold text-gray-800">
            <span className="mr-3 h-3 w-3 rounded-full bg-[#14B8A6]"></span>
            题组模拟切换
          </h3>
          <div className="mb-4">
            <p className="mb-3 text-gray-600">
              模拟真实答题场景中的题组切换，每点击一次切换到下一个题组
            </p>
            <div className="mb-4 rounded-lg bg-gray-50 p-4">
              <div className="space-y-2 text-sm">
                <div>
                  <strong>当前题组:</strong>{" "}
                  {groupSimulation.currentGroupIndex + 1} /{" "}
                  {groupSimulation.totalGroups}
                </div>
                <div>
                  <strong>题组名称:</strong>{" "}
                  {
                    groupSimulation.groupNames[
                      groupSimulation.currentGroupIndex
                    ]
                  }
                </div>
                <div>
                  <strong>进度:</strong>
                  <div className="mt-2 flex items-center gap-1">
                    {Array.from(
                      { length: groupSimulation.totalGroups },
                      (_, index) => (
                        <div
                          key={index}
                          className={`h-4 w-4 rounded-full ${
                            index === groupSimulation.currentGroupIndex
                              ? "bg-[#14B8A6]"
                              : index < groupSimulation.currentGroupIndex
                                ? "bg-[#F4A267]"
                                : "bg-gray-200"
                          }`}
                        />
                      )
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <button
            onClick={switchToNextGroup}
            className="w-full rounded-lg bg-[#14B8A6] px-6 py-3 font-medium text-white transition-colors duration-200 hover:bg-[#0D9488]"
            disabled={transitionState.isPlayingTransitions}
          >
            {transitionState.isPlayingTransitions
              ? "题组转场播放中..."
              : "切换到下一个题组"}
          </button>
        </div>

        {/* 系统控制 */}
        <div className="mb-8 rounded-2xl bg-white p-6 shadow-lg">
          <h3 className="mb-4 flex items-center text-xl font-semibold text-gray-800">
            <span className="mr-3 h-3 w-3 rounded-full bg-red-500"></span>
            系统控制
          </h3>
          <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
            <button
              onClick={resetTransitionSystem}
              className="rounded bg-red-600 px-4 py-2 text-sm text-white hover:bg-red-700"
            >
              重置转场系统
            </button>
            <button
              onClick={clearTransitionRecords}
              className="rounded bg-gray-600 px-4 py-2 text-sm text-white hover:bg-gray-700"
            >
              清理转场记录
            </button>
          </div>
        </div>

        {/* 设备信息展示模块 */}
        <div className="mb-8 rounded-2xl bg-white p-6 shadow-lg">
          <h3 className="mb-4 flex items-center text-xl font-semibold text-gray-800">
            <span className="mr-3 h-3 w-3 rounded-full bg-green-500"></span>
            当前设备信息
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="rounded-lg bg-blue-50 p-4">
              <div className="mb-1 text-sm font-medium text-blue-600">
                设备类型
              </div>
              <div className="text-lg font-semibold text-blue-800">
                {deviceInfo.deviceType}
              </div>
            </div>
            <div className="rounded-lg bg-green-50 p-4">
              <div className="mb-1 text-sm font-medium text-green-600">
                屏幕尺寸
              </div>
              <div className="text-lg font-semibold text-green-800">
                {deviceInfo.width} × {deviceInfo.height}
              </div>
            </div>
            <div className="rounded-lg bg-purple-50 p-4">
              <div className="mb-1 text-sm font-medium text-purple-600">
                宽高比
              </div>
              <div className="text-lg font-semibold text-purple-800">
                {deviceInfo.aspectRatio}
              </div>
            </div>
            <div className="rounded-lg bg-orange-50 p-4">
              <div className="mb-1 text-sm font-medium text-orange-600">
                响应式断点
              </div>
              <div className="text-lg font-semibold text-orange-800">
                {deviceInfo.width <= 768
                  ? "Mobile"
                  : deviceInfo.width <= 1024
                    ? "Tablet"
                    : "Desktop"}
              </div>
            </div>
          </div>
          <div className="mt-4 rounded-lg bg-gray-50 p-3">
            <div className="mb-1 text-sm font-medium text-gray-600">
              User Agent
            </div>
            <div className="break-all text-xs text-gray-500">
              {deviceInfo.userAgent}
            </div>
          </div>
          <div className="mt-4 rounded-lg bg-blue-50 p-3">
            <div className="mb-2 text-sm font-medium text-blue-800">
              主文本位置调整逻辑
            </div>
            <div className="space-y-1 text-xs text-blue-600">
              <div>
                <strong>移动设备</strong> (≤768px): 46%, 35%
              </div>
              <div>
                <strong>平板设备</strong> (769px-1024px):
              </div>
              <div className="ml-4">• 宽高比 ≤1.75 (如1.73): 46%, 35.5%</div>
              <div className="ml-4">• 宽高比 {">"} 1.75: 46.1%, 36%</div>
              <div>
                <strong>桌面设备</strong> ({">"} 1024px):
              </div>
              <div className="ml-4">
                • 宽高比 ≥1.8 (如开发环境1.82): 46.2%, 37%
              </div>
              <div className="ml-4">• 宽高比 {"<"} 1.8: 46.1%, 36.5%</div>
            </div>
          </div>
        </div>
        {/* 使用说明 */}
        <div className="rounded-2xl bg-white p-6 shadow-lg">
          <h3 className="mb-4 text-xl font-semibold text-gray-800">使用说明</h3>
          <div className="space-y-3 text-gray-600">
            <div className="flex items-start">
              <span className="mr-3 mt-2 inline-block h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></span>
              <span>每个反馈动效会自动播放 2-3 秒钟</span>
            </div>
            <div className="flex items-start">
              <span className="mr-3 mt-2 inline-block h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></span>
              <span>正确反馈使用多层 SVG 叠加和 Canvas 渐变文字</span>
            </div>
            <div className="flex items-start">
              <span className="mr-3 mt-2 inline-block h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></span>
              <span>连续正确反馈包含全屏效果和金属光效动画</span>
            </div>
            <div className="flex items-start">
              <span className="mr-3 mt-2 inline-block h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></span>
              <span>错误和粗心反馈提供温和的鼓励性视觉设计</span>
            </div>
            <div className="flex items-start">
              <span className="mr-3 mt-2 inline-block h-2 w-2 flex-shrink-0 rounded-full bg-purple-500"></span>
              <span>支持多层反馈叠加：基础反馈 + 特殊反馈按顺序自动播放</span>
            </div>
            <div className="flex items-start">
              <span className="mr-3 mt-2 inline-block h-2 w-2 flex-shrink-0 rounded-full bg-purple-500"></span>
              <span>
                恢复练习转场动画使用全屏透明背景，左下角显示IP角色和对话气泡
              </span>
            </div>
          </div>
        </div>

        {/* 技术信息 */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>
            技术栈：React + TypeScript + Framer Motion + Tailwind CSS + Canvas
            API
          </p>
        </div>
      </div>

      <StudyTypeThemeProvider studyType={StudyType.AI_COURSE}>
        {/* 转场视图 */}
        <TransitionView
          currentTransition={transitionState.currentTransition || null}
          nextTransition={nextTransition}
        />
      </StudyTypeThemeProvider>
    </div>
  );
}
