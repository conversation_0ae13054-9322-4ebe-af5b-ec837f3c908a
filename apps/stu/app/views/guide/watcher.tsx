import {
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { CallbackListener } from "@remotion/player";
import { useEffect } from "react";
import { useGuideViewContext } from "./guide-view-context";

//DEBUG 用于查看两个播放器不同步的情况
const Watcher = () => {
  const { refPlayer, refVolcenginePlayer, durationInFrames } =
    useGuideViewContext();

  const remotionCurrentFrame = useSignal(0);
  const volcengineCurrentFrame = useSignal(0);
  const duration = useComputed(() => {
    return remotionCurrentFrame.value - volcengineCurrentFrame.value;
  });

  useEffect(() => {
    const player = refPlayer.current;
    const volcenginePlayer = refVolcenginePlayer.value;
    if (!player || !volcenginePlayer) return;

    const updater: CallbackListener<"frameupdate"> = (e) => {
      remotionCurrentFrame.value = e.detail.frame;
      volcengineCurrentFrame.value =
        Math.round(volcenginePlayer.currentTime) * 24;
    };

    player.addEventListener("frameupdate", updater);

    return () => player.removeEventListener("frameupdate", updater);
  }, [refPlayer.current, refVolcenginePlayer.value]);

  useSignalEffect(() => {
    if (Math.abs(duration.value) > 24) {
      refPlayer.current?.seekTo(volcengineCurrentFrame.value);
    }
  });

  return (
    <div className="text-red-1 absolute right-10 top-40 z-50 font-bold">
      <div>
        文稿: {remotionCurrentFrame.value}/{durationInFrames}
      </div>
      <div>
        视频: {volcengineCurrentFrame.value}/
        {Math.round((refVolcenginePlayer.value?.duration ?? 0) * 24 + 24)}
      </div>
      <div>差值: {duration.value}</div>
    </div>
  );
};

export default Watcher;
