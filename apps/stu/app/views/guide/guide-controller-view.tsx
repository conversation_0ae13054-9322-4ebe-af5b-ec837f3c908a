import { toast } from "@/app/components/common/toast";
import {
  PureIconButton,
  TranslucentGlassButton,
  TranslucentGlassButtonGroup,
} from "@/app/components/guide/guide-buttons";
import IconSpin from "@/public/icons/animate-spin.svg";
import IconConfig from "@/public/icons/config.svg";
import IconFastBackward from "@/public/icons/fast-backward.svg";
import IconFastForward from "@/public/icons/fast-forward.svg";
import IconPause from "@/public/icons/pause.svg";
import IconPlane from "@/public/icons/plane.svg";
import IconPlay from "@/public/icons/play.svg";
import { Switch } from "@repo/ui/components/switch";
import { cn } from "@repo/ui/lib/utils";
import { ComponentProps, FC, ReactNode, useEffect } from "react";
import { useDebouncedCallback, useThrottledCallback } from "use-debounce";
import { GuideMode } from "../course/course-view-context";
import { useGuideViewContext } from "./guide-view-context";

const RateSelector: FC<
  { onSelected: (rate: number) => void; rate: number } & ComponentProps<"ul">
> = ({ onSelected, rate, ...props }) => {
  const rates = [0.75, 1.0, 1.25, 1.5, 1.75, 2.0];

  const handleRateChange = (rate: number) => {
    onSelected?.(rate);
  };

  return (
    <ul className="flex flex-row flex-wrap gap-2" {...props}>
      {rates.map((it, index) => (
        <ol
          key={index}
          onClick={() => {
            handleRateChange(it);
          }}
          className={cn(
            "flex h-10 w-16 flex-col items-center justify-center rounded-lg px-5 py-2.5",
            it === rate
              ? "outline-main-red text-dim-red bg-[#FFE5DE] outline-1 outline-offset-[-1px]"
              : "bg-stone-900/5 text-zinc-800/60"
          )}
        >
          <span className="text-xs font-normal leading-tight">{it}x</span>
        </ol>
      ))}
    </ul>
  );
};

const ConfigItem: FC<{ label: string; children: ReactNode }> = ({
  label,
  children,
}) => {
  return (
    <div className="flex w-full flex-col gap-3">
      <div className="justify-center self-stretch text-base font-bold leading-snug text-zinc-800/90">
        {label}
      </div>

      {children}
    </div>
  );
};

const FollowModeController: FC = () => {
  const {
    showSubtitle,
    isPlaying,
    forwardTo,
    togglePlay,
    showPlayerControls,
    hidePlayerControls,
    trackEventWithLessonId,
    canPlay,
    playRate,
    userPreferences,
  } = useGuideViewContext();

  // 3s 后无操作隐藏播放器控制器
  const autoHideControls = useDebouncedCallback(() => {
    hidePlayerControls();
  }, 3000);

  const handleShowRateSelected = (rate: number) => {
    playRate.value = rate;
    trackEventWithLessonId("doc_speed_change");
  };

  const toggleConfigs = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    const value = userPreferences.peek();
    userPreferences.value = {
      ...value,
      showPlayerConfigs: !value.showPlayerConfigs,
    };
  };

  const handleShowSubtitle = (checked: boolean) => {
    showSubtitle.value = checked;
  };

  const handleForwardTo = useThrottledCallback((frame: number) => {
    forwardTo(frame);
    trackEventWithLessonId("doc_seek_10s_click");
  }, 1000);

  useEffect(() => {
    if (showPlayerControls.value) {
      autoHideControls();
    }
  }, [showPlayerControls.value, autoHideControls]);

  if (!showPlayerControls.value) return null;

  return (
    <div
      data-name="guide-controller::follow"
      className="z-60 w-68 pointer-events-none absolute bottom-0 right-0 flex h-full flex-col justify-end"
      style={{
        background:
          "linear-gradient(90deg, rgba(250, 248, 246, 0) -0.72%, rgba(250, 248, 246, 0.95) 11.59%, #FAF8F6 100%)",
      }}
    >
      <div
        className="pointer-events-auto flex h-auto w-full flex-col items-start justify-end gap-8 px-8 pb-8"
        onClick={(e) => {
          e.stopPropagation();
          autoHideControls();
        }}
      >
        {userPreferences.value.showPlayerConfigs && (
          <>
            <ConfigItem label="字幕">
              <div className="bg-fill-4 flex flex-row items-center justify-between rounded-lg p-5">
                {showSubtitle.value ? (
                  <span className="text-dim-red text-sm font-normal">
                    字幕已打开
                  </span>
                ) : (
                  <span className="text-text-4 text-sm font-normal">
                    字幕已关闭
                  </span>
                )}
                <Switch
                  checked={showSubtitle.value}
                  onCheckedChange={(checked) => handleShowSubtitle(checked)}
                  className="data-[state=checked]:bg-main-red bg-[#1F1D1B1A]"
                />
              </div>
            </ConfigItem>
            <ConfigItem label="倍速">
              <RateSelector
                rate={playRate.value}
                onSelected={handleShowRateSelected}
              />
            </ConfigItem>
          </>
        )}
        <div className="flex w-full flex-row items-center justify-between">
          <TranslucentGlassButtonGroup className="px-1">
            <PureIconButton
              className="flex size-10 items-center justify-center"
              icon={<IconConfig />}
              onClick={toggleConfigs}
            />
          </TranslucentGlassButtonGroup>

          <TranslucentGlassButtonGroup>
            <PureIconButton
              className="flex size-10 items-center justify-center"
              onClick={() => handleForwardTo(-10)}
              icon={<IconFastBackward />}
            />

            <PureIconButton
              className="flex size-10 items-center justify-center"
              onClick={togglePlay}
              icon={
                !canPlay ? (
                  <IconSpin className="size-6 animate-spin" />
                ) : isPlaying ? (
                  <IconPause />
                ) : (
                  <IconPlay />
                )
              }
            />
            <PureIconButton
              className="flex size-10 items-center justify-center"
              onClick={() => handleForwardTo(10)}
              icon={<IconFastForward />}
            />
          </TranslucentGlassButtonGroup>
        </div>
      </div>
    </div>
  );
};

const FreeModeController: FC = () => {
  const { setFollowMode } = useGuideViewContext();

  const handleClick = () => {
    setFollowMode();
    toast.show("已定位到老师位置", {
      delay: 600,
    });
  };

  return (
    <div
      data-name="guide-controller::free"
      className="w-50 z-60 absolute bottom-8 right-4"
    >
      <div className="flex h-full w-full flex-col items-center justify-end">
        <TranslucentGlassButton icon={<IconPlane />} onClick={handleClick}>
          <span className="font-bold">跟随老师</span>
        </TranslucentGlassButton>
      </div>
    </div>
  );
};

export const GuideControllerView: FC = () => {
  const { guideMode } = useGuideViewContext();

  return (
    <>
      {guideMode.value === GuideMode.follow ? (
        <FollowModeController />
      ) : (
        <FreeModeController />
      )}
    </>
  );
};
