import Character from "@repo/core/public/assets/stu-exercise/images/continuous-correct-ip1.png";
import LightEffect from "@repo/core/public/assets/stu-exercise/images/light-effect.png";
import { cn } from "@repo/ui/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import Image from "next/image";
import React, { useEffect, useMemo, useState } from "react";
import useScreen from "../../../hooks/use-screen";
import { RandomFragments } from "./widgets/random-fragments";
import { UniversalFragment } from "./widgets/universal-fragment";

interface CorrectFeedbackModalProps {
  isOpen: boolean;
  onClose?: () => void;
  mainText?: string;
  subText?: string;
  className?: string;
  autoCloseDelay?: number; // 自动关闭延迟时间（毫秒），默认3000ms
}

/**
 * [已优化 - 保留原逻辑] 正确答案反馈弹窗组件
 * - 应用了 React.memo 和 will-change 优化
 * - Transform 样式通过 style 属性编写
 * - 保留了内部 isVisible 和 isExiting 状态及相关逻辑
 */
// 优化: 使用 React.memo 包裹组件，防止不必要的重渲染
export const ContinuousCorrectFeedbackModal: React.FC<CorrectFeedbackModalProps> =
  React.memo(
    ({
      isOpen,
      onClose,
      mainText = "太棒了！",
      subText = "同学巩固得很牢固～",
      className,
      autoCloseDelay = 3000,
    }) => {
      const screen = useScreen();

      // 🔥 添加内部状态管理，支持渐隐效果
      const [isVisible, setIsVisible] = useState(false);
      const [isExiting, setIsExiting] = useState(false);

      // 计算响应式字体大小和尺寸
      const responsiveStyles = useMemo(() => {
        const baseWidth = 1000; // 设计稿基准宽度
        const scale = Math.min(screen.width / baseWidth, 1.2);

        return {
          mainFontSize: `clamp(1.5rem, ${2.6 * scale}rem, 3rem)`,
          subFontSize: `clamp(0.8rem, ${1.4 * scale}rem, 1.5rem)`,
        };
      }, [screen.width]);

      // 🔥 监听 isOpen 变化，管理显示状态
      useEffect(() => {
        if (isOpen) {
          setIsVisible(true);
          setIsExiting(false);
        } else {
          // 开始退出动画
          setIsExiting(true);
          // 延迟移除组件，给退出动画时间
          const timer = setTimeout(() => {
            setIsVisible(false);
            setIsExiting(false);
          }, 300); // 与 exit transition 时间匹配
          return () => clearTimeout(timer);
        }
      }, [isOpen]);

      // 🔥 修改自动关闭逻辑，先触发退出动画
      useEffect(() => {
        if (isOpen && onClose && autoCloseDelay > 0) {
          const timer = setTimeout(() => {
            onClose(); // 这会触发 isOpen 变为 false，进而触发退出动画
          }, autoCloseDelay);

          return () => clearTimeout(timer);
        }
      }, [isOpen, onClose, autoCloseDelay]);

      // 🔥 使用新的状态控制渲染
      if (!isVisible) return null;

      return (
        <AnimatePresence>
          <motion.div
            className={cn(
              "fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center",
              className
            )}
            initial={{ opacity: 0 }}
            animate={{ opacity: isExiting ? 0 : 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            // 优化: 添加 will-change 提示GPU加速
            style={{ willChange: "opacity" }}
          >
            {/* 全屏随机碎片 */}
            <RandomFragments count={12} />
            {/* 全屏背景层 */}
            <div
              className="h-full w-full opacity-95"
              style={{
                backdropFilter: "blur(10px)",
                background: "var(--study-feedback-continuous-correct-primary)",
                // 优化: 为高消耗属性添加 will-change
                willChange: "backdrop-filter",
              }}
            >
              {/* 背景光晕层 */}
              <div className="fixed z-0 h-full w-full">
                {/* 右下角光晕 */}
                <div
                  className="-bottom-15 absolute right-0 h-[20.6875rem] w-[33.6875rem] flex-shrink-0 rounded-[33.6875rem] opacity-30"
                  style={{
                    filter: "blur(100px)",
                    background:
                      "var(--study-feedback-continuous-correct-right-bottom)",
                    // 重写 & 优化
                    transform: "translate(53%, 45%)",
                    willChange: "filter, transform",
                  }}
                />
                {/* 上方第一个光晕 */}
                <div
                  className="absolute -top-8 left-1/2 h-[7.625rem] w-[74.875rem] flex-shrink-0 rounded-[74.875rem] opacity-30"
                  style={{
                    filter: "blur(100px)",
                    background:
                      "var(--study-feedback-continuous-correct-top-one)",
                    // 重写 & 优化
                    transform: "translateX(-50%)",
                    willChange: "filter, transform",
                  }}
                />
                {/* 上方第二个光晕 */}
                <div
                  className="-top-30 left-2/5 absolute h-[14.6875rem] w-[23.875rem] flex-shrink-0 rounded-[23.875rem] opacity-30"
                  style={{
                    filter: "blur(100px)",
                    background:
                      "var(--study-feedback-continuous-correct-top-two)",
                    willChange: "filter",
                  }}
                />
              </div>

              {/* 前景层 */}
              <div
                className="fixed top-1/2 z-10 h-[40vh] w-full"
                style={{ transform: "translateY(-50%)" }}
              >
                {/* 前景层-上 */}
                <motion.div
                  className="my-auto flex items-center justify-end"
                  initial={{
                    opacity: 0.4,
                    transform: "translate3d(90vw, -35vh, 0)",
                  }}
                  animate={{
                    opacity: 1,
                    transform: "translate3d(0px, 0px, 0)",
                  }}
                  transition={{ duration: 0.2, delay: 0.2, ease: "easeOut" }}
                  style={{ willChange: "transform, opacity" }}
                >
                  {/* CSS渐变装饰条-上 */}
                  <div
                    className="absolute -right-[2vw] top-[-1vh] h-[4vh] w-[100vw] flex-shrink-0 opacity-40"
                    style={{
                      background:
                        "var(--study-feedback-continuous-correct-top-gradient)",
                      // 重写
                      transform: "rotate(-10deg) skewX(-14deg)",
                    }}
                  />
                  {/* 主要矩形 */}
                  <div
                    className="relative -right-0 top-[4vh] z-10 -mr-[3vw] h-[25vh] w-[85vw] flex-shrink-0 rounded-l-[2vw] drop-shadow-[0px_0px_40px_rgba(255,149,151,0.50)]"
                    style={{
                      // 重写
                      transform: "rotate(-10deg) skewX(-14deg)",
                    }}
                  >
                    {/* 上边框光效 */}
                    <motion.div
                      className="absolute left-[-8vw] z-40 h-[12vh] translate-y-[-5.2vh]"
                      animate={{
                        x: "85vw",
                        opacity: [1, 1, 1, 0],
                      }}
                      transition={{
                        duration: 0.8,
                        delay: 0.5,
                        ease: [0.2, 0.1, 0.2, 1.2],
                      }}
                    >
                      <Image
                        width={300}
                        height={26}
                        src={LightEffect}
                        alt="Light effect"
                        className="h-full w-full object-cover"
                        priority
                      />
                    </motion.div>
                    {/* 下边框Canvas金属反射光效 */}
                    <motion.div
                      className="absolute right-[-8vw] z-40 h-[12vh] translate-y-[18.2vh]"
                      animate={{
                        x: "-85vw",
                        opacity: [1, 1, 1, 0],
                      }}
                      transition={{
                        duration: 0.8,
                        delay: 0.5,
                        ease: [0.2, 0.1, 0.2, 1.2],
                      }}
                    >
                      <Image
                        width={300}
                        height={26}
                        src={LightEffect}
                        alt="Light effect"
                        className="h-full w-full object-cover"
                        style={{ transform: "rotate(180deg)" }}
                        priority
                      />
                    </motion.div>

                    {/* 内容区域 */}
                    <div
                      className="z-10 h-full w-full overflow-hidden rounded-l-[2vw] p-[0.2vw]"
                      style={{
                        background: `linear-gradient(45deg,rgba(255, 255, 255, 1) 0%,rgba(255, 255, 255, 0.8) 20%,rgba(255, 255, 255, 0.4) 50%,rgba(255, 255, 255, 0) 100%)`,
                      }}
                    >
                      <div
                        className="relative h-full w-full overflow-hidden rounded-l-[calc(2vw-0.2vw)]"
                        style={{
                          background: `var(--study-feedback-continuous-correct-center-gradient)`,
                        }}
                      >
                        <div className="absolute left-[5vw] top-[1.8vh] z-20">
                          {/* 主文字 */}
                          <div className="relative overflow-visible px-4 py-2">
                            <div
                              className="pointer-events-none absolute inset-0"
                              style={{
                                height: "30%",
                                top: "60%",
                                background:
                                  "linear-gradient(88deg, #FFD24D 0.4%, rgba(255, 210, 77, 0.00) 99.22%)",
                                zIndex: 0,
                                transform: "skewX(-30deg)",
                              }}
                            />
                            <motion.div
                              className="relative z-10 h-full w-full font-black leading-[1.25]"
                              style={{
                                textShadow:
                                  "0px 0px 18px rgba(150, 150, 150, 0.10)",
                                fontSize: responsiveStyles.mainFontSize,
                                background:
                                  "linear-gradient(262deg, #FFF1B1 32.71%, #FFF 88.2%)",
                                WebkitBackgroundClip: "text",
                                backgroundClip: "text",
                                WebkitTextFillColor: "transparent",
                                fontWeight: "900",
                                transform: "skewX(-10deg)",
                                willChange: "opacity",
                              }}
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{
                                duration: 0.6,
                                delay: 0.5,
                                ease: "easeOut",
                              }}
                            >
                              {mainText}
                            </motion.div>
                          </div>
                          {/* 副文字 */}
                          <motion.div
                            className="mt-[0.5vh] h-full w-full font-medium leading-[1.25] text-[#FFFCED]"
                            style={{
                              textShadow:
                                "0px 0px 18px rgba(150, 150, 150, 0.10)",
                              fontSize: responsiveStyles.subFontSize,
                              transform: "skewX(-10deg)",
                              willChange: "opacity",
                            }}
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{
                              duration: 0.6,
                              delay: 0.8,
                              ease: "easeOut",
                            }}
                          >
                            {subText}
                          </motion.div>
                        </div>
                        {/* 由上角 */}
                        <div className="z-11 skew-x-14 rotate-10 absolute right-[3px] top-0">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="107"
                            height="107"
                            viewBox="0 0 107 107"
                            fill="none"
                          >
                            <path
                              opacity="0.2"
                              d="M0.356441 53.9053L0.356442 36.2969L17.9648 36.2969L17.9648 53.9053L0.356441 53.9053ZM0.35742 18.6885L0.35742 1.08007L17.9658 1.08007L17.9658 18.6885L0.35742 18.6885ZM0.357416 89.1221L0.357417 71.5137L17.9658 71.5137L17.9658 53.9062L35.5742 53.9062L35.5742 71.5146L17.9658 71.5146L17.9658 89.1221L0.357416 89.1221ZM17.9658 36.2978L17.9658 18.6894L35.5742 18.6895L35.5742 36.2978L17.9658 36.2978ZM17.9658 106.731L17.9658 89.123L35.5742 89.123L35.5742 106.731L17.9658 106.731ZM36.3564 53.9062L36.3564 36.2978L53.9648 36.2978L53.9648 53.9062L36.3564 53.9062ZM53.9658 1.08008L53.9658 -16.5273L36.3574 -16.5273L36.3574 -34.1357L53.9658 -34.1357L53.9658 -16.5283L71.5742 -16.5283L71.5742 1.08008L53.9658 1.08008ZM36.3574 18.6885L36.3574 1.08008L53.9658 1.08008L53.9658 18.6885L36.3574 18.6885ZM53.9658 71.5146L53.9658 89.1221L36.3574 89.1221L36.3574 71.5137L53.9658 71.5137L53.9658 53.9062L71.5742 53.9062L71.5742 71.5146L53.9658 71.5146ZM71.5742 53.9053L71.5742 36.2979L53.9658 36.2978L53.9658 18.6895L71.5742 18.6895L71.5742 36.2969L89.1826 36.2969L89.1826 53.9053L71.5742 53.9053ZM53.9658 106.731L53.9658 89.123L71.5742 89.123L71.5742 106.731L53.9658 106.731ZM71.5742 -16.5283L71.5742 -34.1367L89.1826 -34.1367L89.1826 -16.5283L71.5742 -16.5283ZM89.1826 36.2969L89.1826 18.6895L71.5742 18.6895L71.5742 1.08105L89.1826 1.08105L89.1826 18.6885L106.791 18.6885L106.791 36.2969L89.1826 36.2969ZM89.1826 106.73L89.1826 89.123L71.5742 89.123L71.5742 71.5146L89.1826 71.5146L89.1826 89.1221L106.791 89.1221L106.791 106.73L89.1826 106.73ZM89.1826 1.08008L89.1826 -16.5283L106.791 -16.5283L106.791 1.08008L89.1826 1.08008ZM89.1826 71.5146L89.1826 53.9062L106.791 53.9062L106.791 71.5146L89.1826 71.5146Z"
                              fill="url(#paint0_linear_2257_2128)"
                            />
                            <defs>
                              <linearGradient
                                id="paint0_linear_2257_2128"
                                x1="122.5"
                                y1="11"
                                x2="62.9733"
                                y2="108.104"
                                gradientUnits="userSpaceOnUse"
                              >
                                <stop stopColor="white" />
                                <stop
                                  offset="1"
                                  stopColor="white"
                                  stopOpacity="0"
                                />
                              </linearGradient>
                            </defs>
                          </svg>
                        </div>
                        {/* 左下角 */}
                        <div className="z-11 skew-x-14 rotate-8 absolute bottom-[-2px] left-[0px]">
                          <svg
                            width="124"
                            height="84"
                            viewBox="0 0 124 84"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              opacity="0.2"
                              d="M73.6266 76.9944L56.1822 79.3924L53.7842 61.948L71.2286 59.55L73.6266 76.9944ZM18.8944 66.7431L1.45103 69.141L3.84902 86.5853L-13.5953 88.9833L-15.9933 71.539L1.45007 69.1411L-0.947917 51.6968L16.4964 49.2988L18.8944 66.7431ZM38.7367 81.7895L21.2924 84.1875L18.8944 66.7431L36.3388 64.3452L38.7367 81.7895ZM108.514 72.1976L91.0698 74.5956L88.6718 57.1512L71.2284 59.5491L68.8305 42.1047L86.2748 39.7067L88.6728 57.1511L106.116 54.7532L108.514 72.1976ZM68.8295 42.1048L51.3861 44.5027L53.7841 61.947L36.3397 64.345L33.9418 46.9007L51.3851 44.5028L48.9872 27.0585L66.4315 24.6605L68.8295 42.1048ZM123.561 52.3551L106.117 54.7531L103.719 37.3087L121.164 34.9108L123.561 52.3551ZM-0.947917 51.6968L-18.3923 54.0948L-20.7902 36.6504L-3.3459 34.2524L-0.947917 51.6968ZM48.9872 27.0585L31.5438 29.4563L33.9418 46.9007L16.4974 49.2987L14.0994 31.8543L31.5428 29.4565L29.1448 12.0121L46.5892 9.61412L48.9872 27.0585ZM118.765 17.4665L101.321 19.8644L103.719 37.3087L86.2748 39.7067L83.8768 22.2624L101.32 19.8645L98.9222 2.42018L116.367 0.0221945L118.765 17.4665ZM14.0985 31.8544L-3.3459 34.2524L-5.74388 16.8081L11.7005 14.4101L14.0985 31.8544ZM83.8768 22.2624L66.4325 24.6604L64.0345 7.21601L81.4788 4.81803L83.8768 22.2624Z"
                              fill="url(#paint0_linear_2257_2109)"
                            />
                            <defs>
                              <linearGradient
                                id="paint0_linear_2257_2109"
                                x1="-21.4173"
                                y1="32.0888"
                                x2="101.01"
                                y2="-10.1939"
                                gradientUnits="userSpaceOnUse"
                              >
                                <stop stopColor="white" />
                                <stop
                                  offset="1"
                                  stopColor="white"
                                  stopOpacity="0"
                                />
                              </linearGradient>
                            </defs>
                          </svg>
                        </div>
                      </div>
                    </div>

                    {/* 角色图片 */}
                    <div
                      className="absolute bottom-[-2.1vh] right-[10vw] z-30 flex items-center justify-center"
                      style={{ transform: "skewX(14deg) rotate(8deg)" }}
                    >
                      <div
                        className="absolute bottom-[-1.8vh] right-[-1vw] h-[9vh] w-[35vw]"
                        style={{ transform: "rotate(-8deg)" }}
                      >
                        <div
                          className="h-full w-full rounded-br-[5vw] rounded-tr-[5vw] opacity-40"
                          style={{
                            background:
                              "var(--study-feedback-continuous-correct-center-two-gradient)",
                          }}
                        />
                      </div>
                      <div className="relative">
                        <motion.div
                          initial={{ scale: 0, opacity: 0, y: 0 }}
                          animate={{ scale: 1, opacity: 1, y: 0 }}
                          transition={{
                            duration: 1.0,
                            delay: 0.6,
                            ease: [0.175, 0.885, 0.32, 1.275],
                            type: "spring",
                            stiffness: 200,
                            damping: 15,
                          }}
                          style={{
                            transformOrigin: "bottom center",
                            willChange: "transform, opacity",
                          }}
                        >
                          <Image
                            src={Character}
                            alt="Correct feedback character"
                            width={223}
                            height={223}
                            className="relative z-10 h-auto max-h-[25vw] w-auto max-w-[25vw]"
                            priority
                          />
                        </motion.div>
                        {/* 星星装饰 */}
                        <UniversalFragment
                          shape="star"
                          size={20}
                          color={{
                            type: "gradient",
                            primary: "#6BE0E2",
                            secondary: "#4FC3F7",
                            direction: "diagonal",
                          }}
                          animationType="float"
                          delay={1.0}
                          duration={2}
                          position={{ x: 10, y: 8 }}
                        />

                        <UniversalFragment
                          shape="star"
                          size={16}
                          position={{ x: 100, y: 30 }}
                          color={{
                            type: "gradient",
                            primary: "#FFE082",
                            secondary: "#FFE082",
                            direction: "diagonal",
                          }}
                          animationType="float"
                          delay={1.0}
                          duration={2}
                        />
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* 前景层-下 */}
                <motion.div
                  className="w-full"
                  initial={{ transform: "translate(-92vw, 48vh)" }}
                  animate={{ transform: "translate(-3vw, 12vh)" }}
                  transition={{ duration: 0.2, delay: 0.2, ease: "easeOut" }}
                  style={{ willChange: "transform" }}
                >
                  <div
                    className="h-[4vh] w-full flex-shrink-0 opacity-40"
                    style={{
                      background: `var(--study-feedback-continuous-correct-bottom-gradient)`,
                      transform: "rotate(-10deg) skewX(-14deg)",
                    }}
                  />
                </motion.div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      );
    }
  );

// 添加 displayName 便于在 React DevTools 中调试
ContinuousCorrectFeedbackModal.displayName = "ContinuousCorrectFeedbackModal";
