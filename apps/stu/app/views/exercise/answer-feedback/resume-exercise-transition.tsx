"use client";

import IpBg from "@repo/core/public/assets/stu-exercise/images/resume-exercise-ip.png";
import { cn } from "@repo/ui/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import { DialogBubble } from "./widgets/dialog-bubble";
interface ResumeExerciseTransitionProps {
  isOpen: boolean;
  onClose?: () => void;
  mainText?: string;
  subText?: string;
  className?: string;
  autoCloseDelay?: number; // 自动关闭延迟时间（毫秒），默认2000ms
}

/**
 * 恢复练习转场动画组件
 * 全屏透明背景，左下角显示IP角色和对话气泡，包含恢复练习的提示文字
 */
export const ResumeExerciseTransition: React.FC<
  ResumeExerciseTransitionProps
> = ({
  isOpen,
  onClose,
  mainText = "空间几何~",
  subText = "让我们继续练习",
  className,
  autoCloseDelay = 1200,
}) => {
  // 🔥 添加内部状态管理，支持渐隐效果
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  // 文本换行检测状态
  const [isMainTextOverflowing, setMainTextOverflowing] = useState(false);
  const [isSubTextOverflowing, setSubTextOverflowing] = useState(false);

  // 文本元素引用
  const mainTextRef = useRef<HTMLDivElement>(null);
  const subTextRef = useRef<HTMLDivElement>(null);

  // 🔥 监听 isOpen 变化，管理内部状态
  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      setIsExiting(false);
    } else {
      // 开始退出动画
      setIsExiting(true);
      // 延迟隐藏组件，给退出动画时间
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 300); // 与动画时长一致
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // 检测文本是否溢出需要换行
  useEffect(() => {
    const checkTextOverflow = () => {
      const maxWidth = 300; // 对应 max-w-[300px]

      // 创建临时测量元素的通用函数
      const measureTextWidth = (
        text: string,
        fontSize: string,
        fontWeight: string
      ) => {
        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");
        if (!context) return 0;

        context.font = `${fontWeight} ${fontSize}`;
        return context.measureText(text).width;
      };

      // 检测主文本 (text-2xl = 24px, font-bold)
      const mainTextWidth = measureTextWidth(mainText, "24px", "bold");

      setMainTextOverflowing(mainTextWidth > maxWidth);

      // 检测副文本 (text-[1.0625rem] = 17px, font-normal)
      const subTextWidth = measureTextWidth(subText, "17px", "normal");
      setSubTextOverflowing(subTextWidth > maxWidth);
    };

    // 延迟检测，确保DOM渲染完成
    if (isVisible) {
      const timer = setTimeout(checkTextOverflow, 100);
      return () => clearTimeout(timer);
    }
  }, [isVisible, mainText, subText]);

  // 🔥 修改自动关闭逻辑：先渐隐，再通知父组件
  useEffect(() => {
    // 使用一个变量来跟踪定时器是否已被清除，防止组件卸载后依然执行
    let isCancelled = false;
    let mainTimer: NodeJS.Timeout;
    let exitTimer: NodeJS.Timeout;

    if (isVisible && onClose && autoCloseDelay > 0) {
      mainTimer = setTimeout(() => {
        if (isCancelled) return;

        // 开始渐隐动画
        setIsExiting(true);

        // 延迟通知父组件关闭
        exitTimer = setTimeout(() => {
          if (isCancelled) return;
          onClose();
        }, 300); // 给渐隐动画 300ms 时间
      }, autoCloseDelay);
    }

    return () => {
      isCancelled = true;
      clearTimeout(mainTimer);
      clearTimeout(exitTimer);
    };
  }, [isVisible, onClose, autoCloseDelay]);

  // 🔥 使用新的状态控制渲染
  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        className={cn(
          "resume-exercise-transition absolute inset-0 left-0 top-0 z-50 flex h-full w-full items-center justify-center",
          className
        )}
        initial={{ opacity: 0 }}
        animate={{ opacity: isExiting ? 0 : 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2, ease: "easeOut" }}
        // 优化: 添加 will-change 提示GPU加速
        style={{ willChange: "opacity" }}
      >
        {/* 全屏透明背景 */}
        <div className="resume-exercise-bg h-full w-full bg-transparent">
          {/* 左下角内容容器 */}
          <div className="resume-exercise-content absolute bottom-0 left-0 flex items-start">
            {/* 中心强模糊层 */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{
                duration: 0.2,
                delay: 0.05,
                ease: [0.25, 0.46, 0.45, 0.94],
              }}
              className="center-blur-element absolute left-[40%] top-[50%] z-10 h-1/2 w-0 rounded-[5rem]"
              style={{
                boxShadow: "0 0 10vw 25vw rgba(255, 255, 255)",
              }}
            />
            {/* IP 角色图片 */}
            <motion.div
              className="resume-exercise-character relative z-10"
              initial={{
                x: -80,
                opacity: 0,
                transformOrigin: "bottom center",
              }}
              animate={{
                x: 0,
                opacity: 1,
              }}
              transition={{
                duration: 0.12,
                delay: 0.05,

                ease: [0.25, 0.46, 0.45, 0.94],
              }}
            >
              <Image
                src={IpBg}
                alt="Resume exercise character"
                width={300}
                height={300}
                className="h-auto max-h-[38vw] w-auto max-w-[28vw] translate-x-[-4.6vw]"
                priority
              />
            </motion.div>
            {/* 对话气泡容器 */}
            <motion.div
              className="resume-exercise-dialog absolute left-[14vw] z-20"
              style={{
                top: isMainTextOverflowing ? "8vh" : "10vh",
              }}
              initial={{
                scale: 0.8,
                opacity: 0,
              }}
              animate={{
                scale: 1,
                opacity: 1,
              }}
              transition={{
                duration: 0.2,
                delay: 0.1,
                ease: [0.25, 0.46, 0.45, 0.94],
              }}
            >
              {/* 对话气泡背景 */}
              <div className="resume-dialog-bubble">
                <DialogBubble>
                  {/* 文字容器 - 绝对定位在气泡中心 */}
                  <div className="resume-dialog-text flex w-auto max-w-[35vw] flex-col items-start justify-center">
                    {/* 副文本 */}
                    <motion.div
                      ref={subTextRef}
                      className={cn(
                        "resume-dialog-subtext text-text-1 mb-1 inline-block max-w-[300px] break-words text-[1.0625rem] font-normal",
                        isSubTextOverflowing
                          ? "whitespace-normal"
                          : "whitespace-nowrap"
                      )}
                      style={{ fontFamily: "Resource Han Rounded SC" }}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{
                        duration: 0.1,
                        delay: 0.1,
                        ease: "easeOut",
                      }}
                    >
                      {subText}
                    </motion.div>

                    {/* 主文本容器 */}
                    <motion.div
                      ref={mainTextRef}
                      className={cn(
                        "resume-dialog-maintext text-text-1 inline-block max-w-[300px] break-words text-2xl font-bold",
                        isMainTextOverflowing
                          ? "min-w-[30vw] whitespace-normal"
                          : "whitespace-nowrap"
                      )}
                    >
                      {mainText}
                    </motion.div>
                  </div>
                </DialogBubble>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
