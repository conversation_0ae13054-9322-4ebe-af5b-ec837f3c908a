"use client";

import { cn } from "@repo/ui/lib/utils";
import { motion } from "framer-motion";
import React, { useEffect, useState } from "react";

interface QuestionGroupTransitionProps {
  isOpen: boolean;
  onClose?: () => void;
  groupName?: string; // 题组名称
  className?: string;
  autoCloseDelay?: number; // 自动关闭延迟时间（毫秒），默认4000ms
  currentGroupCount?: number; // 题组总数
  currentGroupIndex?: number; // 当前题组索引 (0-based)
  previousGroupName?: string; // 上一个题组名称
}

/**
 * 题组转场动画组件
 * 设计理念：激活节点固定在中间，时间轴滑动到正确位置
 *
 * 🔧 线段调试参数指南：
 *
 * 1. 线段数量控制（第285行 segmentCount）：
 *    - 当前值：4（已优化为更紧凑）
 *    - 想要更紧凑 → 改为3
 *    - 想要稍微分散 → 改为5
 *
 * 2. 线段间距控制（第291行 divider）：
 *    - 当前值：6
 *    - 想要更集中 → 改为7或8
 *    - 想要更分散 → 改为5
 *
 * 3. 线段长度控制（第304行 width）：
 *    - 当前值：1.0%（已优化为更短）
 *    - 想要更短 → 改为0.8%
 *    - 想要稍长 → 改为1.2%
 *
 * 🔧 节点间距调试参数指南：
 *
 * 4. 节点间距控制（第184行 maxNodeWidth）：
 *    - 当前值：50（节点集中在中心50%区域）
 *    - 想要节点更紧凑 → 改为40、30、25
 *    - 想要节点分散一些 → 改为60、70
 *    - 恢复原始分布 → 改为100
 *
 * 💡 推荐调试组合：
 *    - 极紧凑：segmentCount=3, divider=7, width='0.8%', maxNodeWidth=30
 *    - 当前配置：segmentCount=4, divider=6, width='1.0%', maxNodeWidth=50（推荐）
 *    - 适中：segmentCount=5, divider=6, width='1.2%', maxNodeWidth=70
 */

// 🔧 时间轴配置常量
const TIMELINE_CONFIG = {
  // 节点配置
  maxNodeWidth: 70, // 📍 主要调试点：节点区域宽度
  nodeSize: 50, // 节点尺寸(px)
  staticNodeSize: 16, // 静态节点背景尺寸(px)

  // 线段配置
  segmentCount: 5, // 每个区间的线段数量
  segmentDivider: 6, // 线段分布分段数
  segmentWidth: "1.0%", // 线段宽度
  segmentHeight: "7px", // 线段高度

  // 动画配置
  nodeActivationDelay: 300, // 节点激活延迟(ms)
  textTransitionDelay: 600, // 文字转换延迟(ms) - 在节点激活后开始
  timelineMovingDelay: 800, // 时间轴移动延迟(ms)
  segmentsActivatingDelay: 1800, // 线段激活延迟(ms)
  timelineMoveDuration: 1.0, // 时间轴移动时长(s)

  // 遮罩配置
  fadeWidth: "8vw", // 渐隐遮罩宽度
  timelineWidth: "70vw", // 时间轴总宽度
} as const;

// 🔧 位置计算工具类
class TimelineCalculator {
  constructor(
    private currentGroupCount: number,
    private currentGroupIndex: number,
    private config: typeof TIMELINE_CONFIG
  ) {}

  // 计算居中偏移量
  private getCenterOffset(): number {
    return (100 - this.config.maxNodeWidth) / 2;
  }

  // 计算节点间距
  getNodeSpacing(): number {
    return this.currentGroupCount <= 1
      ? 0
      : this.config.maxNodeWidth / (this.currentGroupCount - 1);
  }

  // 计算节点在时间轴上的实际位置
  getNodePosition(nodeIndex: number): number {
    if (this.currentGroupCount === 1) return 50;
    return this.getCenterOffset() + nodeIndex * this.getNodeSpacing();
  }

  // 计算时间轴偏移量
  getTimelineOffset(): number {
    if (this.currentGroupCount <= 1) return 0;

    const currentNodePosition = this.getNodePosition(this.currentGroupIndex);
    return 50 - currentNodePosition; // 让当前节点到达中心
  }

  // 计算上一个位置的偏移量
  getPreviousTimelineOffset(): number {
    if (this.currentGroupCount <= 1) return 0;

    const previousIndex = Math.max(0, this.currentGroupIndex - 1);
    const previousNodePosition = this.getNodePosition(previousIndex);
    return 50 - previousNodePosition;
  }

  // 计算两个节点间的线段位置
  getSegmentPositions(sectionIndex: number): number[] {
    const node1Position = this.getNodePosition(sectionIndex);
    const node2Position = this.getNodePosition(sectionIndex + 1);
    const sectionWidth = node2Position - node1Position;

    const positions: number[] = [];
    for (let i = 0; i < this.config.segmentCount; i++) {
      const segmentPosition =
        node1Position + (sectionWidth / this.config.segmentDivider) * (i + 1);
      positions.push(segmentPosition);
    }
    return positions;
  }
}

// 🔧 动画状态管理钩子
function useTransitionAnimation(isOpen: boolean) {
  const [animationPhase, setAnimationPhase] = useState<
    | "initial"
    | "node_activated"
    | "text_transitioning"
    | "timeline_moving"
    | "segments_activating"
  >("initial");

  useEffect(() => {
    if (!isOpen) {
      setAnimationPhase("initial");
      return;
    }

    // 节点激活
    const nodeTimer = setTimeout(() => {
      setAnimationPhase("node_activated");
    }, TIMELINE_CONFIG.nodeActivationDelay);

    // 文字转换（在节点激活后开始）
    const textTimer = setTimeout(() => {
      setAnimationPhase("text_transitioning");
    }, TIMELINE_CONFIG.textTransitionDelay);

    // 时间轴移动
    const timelineTimer = setTimeout(() => {
      setAnimationPhase("timeline_moving");
    }, TIMELINE_CONFIG.timelineMovingDelay);

    // 线段激活
    const segmentsTimer = setTimeout(() => {
      setAnimationPhase("segments_activating");
    }, TIMELINE_CONFIG.segmentsActivatingDelay);

    return () => {
      clearTimeout(nodeTimer);
      clearTimeout(textTimer);
      clearTimeout(timelineTimer);
      clearTimeout(segmentsTimer);
    };
  }, [isOpen]);

  return animationPhase;
}

// 🔧 时间轴线段组件
interface TimelineSegmentsProps {
  currentGroupCount: number;
  currentGroupIndex: number;
  animationPhase: string;
  calculator: TimelineCalculator;
}

const TimelineSegments: React.FC<TimelineSegmentsProps> = ({
  currentGroupCount,
  currentGroupIndex,
  animationPhase,
  calculator,
}) => {
  if (currentGroupCount <= 1) return null;

  return (
    <div className="timeline-lines absolute inset-0 flex items-center">
      {Array.from({ length: currentGroupCount - 1 }, (_, sectionIndex) => {
        const segmentPositions = calculator.getSegmentPositions(sectionIndex);

        return segmentPositions.map((segmentPosition, segmentIndex) => {
          // 🔧 修复：激活的应该是当前节点前一个区间的线段（currentGroupIndex - 1）
          const isActiveSection =
            currentGroupIndex - 1 === sectionIndex &&
            animationPhase === "segments_activating";

          return (
            <motion.div
              key={`${sectionIndex}-${segmentIndex}`}
              className="timeline-segment absolute rounded-full"
              style={{
                width: TIMELINE_CONFIG.segmentWidth,
                height: TIMELINE_CONFIG.segmentHeight,
                left: `${segmentPosition}%`,
                transform: "translateX(-50%)",
                backgroundColor: "rgba(255, 230, 212, 0.32)", // 统一的未激活色
              }}
              animate={{
                scale: isActiveSection ? [1, 1.3, 1] : 1,
              }}
              transition={{
                duration: isActiveSection ? 0.4 : 0.2,
                delay: isActiveSection ? segmentIndex * 0.1 : 0, // 波浪式激活
                ease: isActiveSection ? "backOut" : "easeOut",
              }}
            />
          );
        });
      }).flat()}
    </div>
  );
};

// 🔧 时间轴节点组件
interface TimelineNodesProps {
  currentGroupCount: number;
  calculator: TimelineCalculator;
}

const TimelineNodes: React.FC<TimelineNodesProps> = ({
  currentGroupCount,
  calculator,
}) => {
  return (
    <>
      {Array.from({ length: currentGroupCount }, (_, nodeIndex) => {
        const nodePosition = calculator.getNodePosition(nodeIndex);

        return (
          <div
            key={nodeIndex}
            className="timeline-node absolute"
            style={{
              left: `${nodePosition}%`,
              transform: "translateX(-50%)",
              width: `${TIMELINE_CONFIG.nodeSize}px`,
              height: `${TIMELINE_CONFIG.nodeSize}px`,
              zIndex: 10,
            }}
          >
            {/* 静态节点背景 */}
            <div
              className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform rounded-full bg-[#FFE6D4]"
              style={{
                width: `${TIMELINE_CONFIG.staticNodeSize}px`,
                height: `${TIMELINE_CONFIG.staticNodeSize}px`,
              }}
            />
          </div>
        );
      })}
    </>
  );
};

// 🔧 题组名称渐变组件
interface GroupNameTransitionProps {
  previousGroupName?: string;
  currentGroupName: string;
  animationPhase: string;
}

const GroupNameTransition: React.FC<GroupNameTransitionProps> = ({
  previousGroupName,
  currentGroupName,
  animationPhase,
}) => {
  const isTransitioning =
    animationPhase === "text_transitioning" ||
    animationPhase === "timeline_moving" ||
    animationPhase === "segments_activating";

  // 如果没有上一个题组名称，直接显示当前题组名称
  if (!previousGroupName) {
    return (
      <motion.div
        className="question-group-main-text whitespace-nowrap text-center text-white"
        style={{
          fontFamily: "DFP King Gothic GB Semibold",
          fontSize: "2rem",
          fontWeight: 400,
          lineHeight: "150%",
        }}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{
          duration: 0.8,
          delay: 0.4,
        }}
      >
        {currentGroupName}
      </motion.div>
    );
  }

  return (
    <div className="question-group-main-text-container relative whitespace-nowrap text-center text-white">
      {/* 上一个题组名称 - 优雅渐隐 */}
      <motion.div
        className="question-group-main-text absolute inset-0 flex items-center justify-center"
        style={{
          fontFamily: "DFP King Gothic GB Semibold",
          fontSize: "2rem",
          fontWeight: 400,
          lineHeight: "150%",
        }}
        initial={{ opacity: 1, scale: 1 }}
        animate={{
          opacity: isTransitioning ? 0 : 1,
          // scale: isTransitioning ? 0.75 : 1,
        }}
        transition={{
          duration: 0.8,
          ease: [0.16, 1, 0.3, 1], // 更柔和的缓动曲线
        }}
      >
        {previousGroupName}
      </motion.div>

      {/* 当前题组名称 - 优雅渐显 */}
      <motion.div
        className="question-group-main-text relative text-center text-white"
        style={{
          fontFamily: "DFP King Gothic GB Semibold",
          fontSize: "2rem",
          fontWeight: 400,
          lineHeight: "150%",
        }}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{
          opacity: isTransitioning ? 1 : 0,
          // scale: isTransitioning ? 1 : 0.65,
        }}
        transition={{
          duration: 0.8,
          delay: 0.4, // 增加延迟，让上一个文字先开始消失
          ease: [0.16, 1, 0.3, 1], // 同样柔和的缓动曲线
        }}
      >
        {currentGroupName}
      </motion.div>
    </div>
  );
};

// 🔧 激活节点组件
interface ActiveNodeProps {
  animationPhase: string;
}

const ActiveNode: React.FC<ActiveNodeProps> = ({ animationPhase }) => {
  const isActive =
    animationPhase === "node_activated" ||
    animationPhase === "text_transitioning" ||
    animationPhase === "timeline_moving" ||
    animationPhase === "segments_activating";

  if (!isActive) return null;

  return (
    <div
      className="timeline-center-active-node absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform"
      style={{
        width: `${TIMELINE_CONFIG.nodeSize}px`,
        height: `${TIMELINE_CONFIG.nodeSize}px`,
        zIndex: 30,
      }}
    >
      <motion.div
        className="timeline-active-node absolute inset-0"
        initial={{ scale: 0, opacity: 0 }}
        animate={{
          scale: 1,
          opacity: 1,
        }}
        exit={{ scale: 0, opacity: 0 }}
        transition={{
          duration: 0.4,
          ease: "backOut",
        }}
      >
        {/* SVG激活效果 - 保留原始橙色配色 */}
        <motion.svg
          width={TIMELINE_CONFIG.nodeSize}
          height={TIMELINE_CONFIG.nodeSize}
          viewBox="0 0 50 50"
          fill="none"
          className="h-full w-full"
          animate={{
            scale: [1, 1.15, 1],
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          {/* 外圈 */}
          <motion.ellipse
            cx="25"
            cy="25"
            rx="25"
            ry="25"
            fill="#F4A267"
            animate={{
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 0.8,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
          {/* 中圈 */}
          <motion.ellipse
            cx="24.9997"
            cy="25.0001"
            rx="16.6667"
            ry="16.6667"
            fill="#FFA666"
            animate={{
              opacity: [0.8, 1, 0.8],
            }}
            transition={{
              duration: 0.8,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
          {/* 内圈 */}
          <ellipse
            cx="25.0003"
            cy="25.0001"
            rx="7.14286"
            ry="7.14286"
            fill="#FFE6D4"
          />
        </motion.svg>
      </motion.div>
    </div>
  );
};

export const QuestionGroupTransition: React.FC<
  QuestionGroupTransitionProps
> = ({
  isOpen,
  onClose,
  groupName = "新题组",
  className,
  autoCloseDelay = 2000,
  currentGroupCount = 5,
  currentGroupIndex = 0,
  previousGroupName,
}) => {
  // 🔧 使用抽离的动画状态管理
  const animationPhase = useTransitionAnimation(isOpen);

  // 🔧 使用抽离的位置计算工具
  const calculator = new TimelineCalculator(
    currentGroupCount,
    currentGroupIndex,
    TIMELINE_CONFIG
  );
  const timelineOffset = calculator.getTimelineOffset();
  const previousTimelineOffset = calculator.getPreviousTimelineOffset();

  // 自动关闭逻辑
  useEffect(() => {
    if (isOpen && autoCloseDelay > 0) {
      const timer = setTimeout(() => {
        onClose?.();
      }, autoCloseDelay);
      return () => clearTimeout(timer);
    }
  }, [isOpen, autoCloseDelay, onClose]);

  if (!isOpen) {
    return null;
  }

  return (
    <motion.div
      className={cn(
        "question-group-transition-overlay pb-30 font-resource-han-rounded fixed inset-0 z-[9999] flex flex-col items-center justify-center",
        className
      )}
      style={{
        background: "rgba(31, 29, 27, 0.60)",
      }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* 文本区域 */}
      <motion.div
        className="question-group-text-area mb-3 flex flex-col items-center gap-[0.625rem]"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{
          duration: 0.5,
          delay: 0.2,
          ease: [0.25, 0.46, 0.45, 0.94],
        }}
      >
        {/* 题组名称渐变 */}
        <GroupNameTransition
          previousGroupName={previousGroupName}
          currentGroupName={groupName}
          animationPhase={animationPhase}
        />

        {/* 题目数量 - 在文字转换时渐显 */}
        <motion.div
          className="question-group-sub-text whitespace-nowrap text-center text-white"
          style={{
            fontFamily: "DFP King Gothic GB Semibold",
            fontSize: "1.5rem",
            fontWeight: 400,
            lineHeight: "150%",
            letterSpacing: "0.24rem",
          }}
          initial={{ opacity: 0, scale: 0.75, y: 10 }}
          animate={{
            opacity: 1,
            scale: 1,
            y: 0,
          }}
          transition={{
            duration: 0.6,
            delay: 1,
            ease: [0.25, 0.46, 0.45, 0.94],
          }}
        >
          约{currentGroupCount}道题
        </motion.div>
      </motion.div>

      {/* 横向滑动时间轴 - 70vw固定宽度 */}
      <motion.div
        className="question-group-timeline relative flex items-center justify-center overflow-hidden"
        style={{
          width: "70vw", // 固定宽度70vw
          height: "6.25rem",
        }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{
          duration: 0.5,
          delay: 0.3,
        }}
      >
        {/* 🔧 方案1: CSS遮罩渐隐 (推荐) */}
        <div
          className="timeline-viewport relative h-full w-full"
          style={{
            maskImage:
              "linear-gradient(to right, transparent 0%, black 8vw, black calc(100% - 8vw), transparent 100%)",
            WebkitMaskImage:
              "linear-gradient(to right, transparent 0%, black 8vw, black calc(100% - 8vw), transparent 100%)",
          }}
        >
          {/* 时间轴容器 - 带滑动动画 */}
          <motion.div
            className="timeline-container absolute inset-0 flex items-center overflow-hidden"
            style={{
              width:
                currentGroupCount <= 1
                  ? "100%"
                  : `${Math.max(100, currentGroupCount * 20)}%`,
              left: "0%",
            }}
            initial={{
              x: `${previousTimelineOffset}%`,
            }}
            animate={{
              x:
                animationPhase === "timeline_moving" ||
                animationPhase === "segments_activating"
                  ? `${timelineOffset}%`
                  : `${previousTimelineOffset}%`,
            }}
            transition={{
              duration:
                animationPhase === "timeline_moving" ||
                animationPhase === "segments_activating"
                  ? currentGroupIndex === 0
                    ? 0
                    : TIMELINE_CONFIG.timelineMoveDuration
                  : 0,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
          >
            <TimelineSegments
              currentGroupCount={currentGroupCount}
              currentGroupIndex={currentGroupIndex}
              animationPhase={animationPhase}
              calculator={calculator}
            />

            <TimelineNodes
              currentGroupCount={currentGroupCount}
              calculator={calculator}
            />
          </motion.div>

          <ActiveNode animationPhase={animationPhase} />
        </div>
      </motion.div>
    </motion.div>
  );
};
