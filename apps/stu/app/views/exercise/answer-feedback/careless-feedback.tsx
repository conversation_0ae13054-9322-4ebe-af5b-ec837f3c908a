"use client";

import { useQuestionContext } from "@/app/contexts/question-context";
import { EnhancedCanvasText } from "@repo/core/components/EnhancedCanvasText";
import { FeedbackType } from "@repo/core/enums";
import IpBgSvg from "@repo/core/public/assets/stu-exercise/images/careless-ip-bg.svg";
import Character from "@repo/core/public/assets/stu-exercise/images/careless-ip1.png";
import { cn } from "@repo/ui/lib/utils";
import { motion } from "framer-motion";
import Image from "next/image";
import React, { useEffect, useMemo, useState } from "react";
import useScreen from "../../../hooks/use-screen";

interface CorrectFeedbackModalProps {
  isOpen: boolean;
  onClose?: () => void;
  mainText?: string;
  subText?: string;
  className?: string;
  autoCloseDelay?: number; // 自动关闭延迟时间（毫秒），默认3000ms
}

/**
 * [已优化 - 保留原逻辑] 粗心反馈弹窗组件
 * - 应用了 React.memo 和 will-change 优化
 * - Transform 样式通过 style 属性编写
 * - 保留了内部 isExiting 状态和延时关闭逻辑
 */
// 优化: 使用 React.memo 包裹组件，防止不必要的重渲染
export const CarelessFeedbackModal: React.FC<CorrectFeedbackModalProps> =
  React.memo(
    ({
      isOpen,
      onClose,
      mainText = "认真作答",
      subText = "有助于掌握度提升～",
      className,
      autoCloseDelay = 3000,
    }) => {
      const screen = useScreen();
      const { trackEventWithExercise } = useQuestionContext();

      // 🔥 内部状态控制渐隐效果
      const [isExiting, setIsExiting] = useState(false);

      // 🔥 修改自动关闭逻辑：先渐隐，再通知父组件
      useEffect(() => {
        // 使用一个变量来跟踪定时器是否已被清除，防止组件卸载后依然执行
        let isCancelled = false;
        let mainTimer: NodeJS.Timeout;
        let exitTimer: NodeJS.Timeout;

        if (isOpen && onClose && autoCloseDelay > 0) {
          trackEventWithExercise?.("answer_feedback_show", {
            feedback_type: FeedbackType.AnswerCarelessly,
            feedback_content: subText,
          });
          mainTimer = setTimeout(() => {
            if (isCancelled) return;

            // 开始渐隐动画
            setIsExiting(true);

            // 延迟通知父组件关闭
            exitTimer = setTimeout(() => {
              if (isCancelled) return;
              onClose();
            }, 300); // 给渐隐动画 300ms 时间
          }, autoCloseDelay);
        }

        return () => {
          isCancelled = true;
          clearTimeout(mainTimer);
          clearTimeout(exitTimer);
        };
      }, [isOpen, onClose, autoCloseDelay, subText, trackEventWithExercise]);

      // 保留: 重置退出状态
      useEffect(() => {
        if (isOpen) {
          setIsExiting(false);
        }
      }, [isOpen]);

      // 根据屏幕尺寸和宽高比计算主文本位置
      const mainTextPosition = useMemo(() => {
        const { width, height } = screen;
        const aspectRatio = width / height;

        if (width <= 768) {
          // 移动设备
          return { left: "46%", top: "35%" };
        }
        // 平板及桌面设备
        if (aspectRatio >= 1.7) {
          return { left: "46%", top: "37.5%" };
        }
        return { left: "46%", top: "38.5%" };
      }, [screen.width, screen.height]);

      // 使用 isOpen 控制组件是否渲染，这是必要的
      if (!isOpen) {
        return null;
      }

      return (
        <motion.div
          className={cn(
            "fixed inset-0 z-50 flex items-center justify-center",
            className
          )}
          initial={{ opacity: 0 }}
          animate={{ opacity: isExiting ? 0 : 1 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          // 优化: 添加 will-change 提示GPU加速
          style={{ willChange: "opacity" }}
        >
          {/* 弹窗内容容器 */}
          <div className="relative mx-[2vw] w-full max-w-[60vw]">
            {/* 背景和装饰SVG */}
            <motion.div
              className="absolute inset-0 flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2, ease: "easeOut" }}
              style={{ willChange: "opacity" }}
            >
              <motion.div
                className="absolute z-10 flex h-[26vw] items-center justify-center"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2, ease: "easeOut" }}
                style={{ willChange: "transform, opacity" }}
              >
                <IpBgSvg width={"50vw"} />
                {/* 主文字 */}
                <motion.div
                  className="absolute z-20"
                  style={{
                    left: mainTextPosition.left,
                    top: mainTextPosition.top,
                    willChange: "transform, opacity",
                  }}
                  initial={{ opacity: 0, scale: 0.7, x: "1.5vw" }}
                  animate={{ opacity: 1, scale: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.4, ease: "backOut" }}
                >
                  {/* 重写: transform 样式移至 style */}
                  <div style={{ transform: "skewX(-15deg)" }}>
                    <EnhancedCanvasText
                      text={mainText}
                      fontSize={Math.min((3.5 * screen.width) / 100, 48)}
                      fontWeight={900}
                      gradientColors={["#3898FA"]}
                      strokeColor="#FFFFFF"
                      strokeWidth={4}
                    />
                  </div>
                </motion.div>

                {/* 副文字 */}
                <motion.div
                  className="absolute left-[46.5%] top-[66%] z-20 text-[2.3vw] font-normal leading-[125%] text-[rgba(51,48,45,0.70)]"
                  initial={{ opacity: 0, y: "1vh" }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.6, ease: "easeOut" }}
                  style={{
                    // 重写: transform 样式移至 style
                    transform: "skewX(-15deg)",
                    // 优化: 添加 will-change
                    willChange: "transform, opacity",
                  }}
                >
                  {subText}
                </motion.div>
              </motion.div>
            </motion.div>

            {/* 角色图片 */}
            <div
              className="absolute inset-0 top-[-4.2vh] z-20 flex items-center justify-center"
              // 重写: transform 样式移至 style
              style={{ transform: "translateX(-18%)" }}
            >
              <div className="relative">
                <motion.div
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{
                    duration: 1.0,
                    delay: 0.2,
                    ease: [0.175, 0.885, 0.32, 1.275],
                    type: "spring",
                    stiffness: 200,
                    damping: 15,
                  }}
                  style={{
                    transformOrigin: "bottom center",
                    willChange: "transform, opacity",
                  }}
                >
                  <Image
                    src={Character}
                    alt="Careless feedback character"
                    width={223}
                    height={223}
                    className="relative z-30 h-auto w-[25vw]"
                    priority
                  />
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      );
    }
  );

// 添加 displayName 便于在 React DevTools 中调试
CarelessFeedbackModal.displayName = "CarelessFeedbackModal";
