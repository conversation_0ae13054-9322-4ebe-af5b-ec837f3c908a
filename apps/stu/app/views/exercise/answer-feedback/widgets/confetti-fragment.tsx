import React, { useMemo } from "react";
import { UniversalFragment, FragmentShape } from "./universal-fragment";

/**
 * 礼花效果组件 - 生成多个随机碎片的礼花爆炸效果
 */
export const ConfettiFragment: React.FC<{
    count?: number; // 碎片数量，默认10个
    delay?: number; // 整体延迟
    duration?: number; // 动画持续时间
    spread?: number; // 散布范围（角度），默认60度
    force?: number; // 爆炸力度，默认150
    className?: string;
  }> = ({
    count = 10,
    delay = 0,
    duration = 1.5,
    spread = 60,
    force = 150,
    className
  }) => {

    // 预定义的碎片配置池
    const fragmentConfigs = useMemo(() => [
      // 彩色圆形
      {
        shape: 'circle' as FragmentShape,
        size: 14,
        color: { type: 'solid' as const, primary: '#FF9861' }
      },
      {
        shape: 'circle' as FragmentShape,
        size: 16,
        color: { type: 'gradient' as const, primary: '#81D7C1', secondary: '#4FC3F7', direction: 'diagonal' as const }
      },
      // 正方形变体
      {
        shape: 'square' as FragmentShape,
        size: 12,
        color: { type: 'solid' as const, primary: '#FFD630' },
        borderRadius: 2
      },
      {
        shape: 'square' as FragmentShape,
        size: 10,
        color: { type: 'gradient' as const, primary: '#ACA6FF', secondary: '#8B7EFF', direction: 'vertical' as const },
        borderRadius: 3
      },
      // 空心形状
      {
        shape: 'hollow-square' as FragmentShape,
        size: 14,
        color: { type: 'solid' as const, primary: 'transparent' },
        strokeColor: '#EBD8FF',
        strokeWidth: 3,
        borderRadius: 2
      },
      // 特殊形状
      {
        shape: 'triangle' as FragmentShape,
        size: 16,
        color: { type: 'gradient' as const, primary: '#FF6B35', secondary: '#FF9861', direction: 'horizontal' as const }
      },
      {
        shape: 'diamond' as FragmentShape,
        size: 18,
        color: { type: 'solid' as const, primary: '#FFE082' }
      },
      {
        shape: 'star' as FragmentShape,
        size: 20,
        color: { type: 'gradient' as const, primary: '#FFF052', secondary: '#FFCB5A', direction: 'diagonal' as const }
      },
      // 不规则形状
      {
        shape: 'arc-strip' as FragmentShape,
        size: 22,
        color: { type: 'gradient' as const, primary: '#C4B5FD', secondary: '#ACA6FF', direction: 'diagonal' as const }
      }
    ], []);

    // 生成多个碎片的配置
    const fragments = useMemo(() => {
      return Array.from({ length: count }, (_, index) => {
        // 随机选择配置
        const configIndex = Math.floor(Math.random() * fragmentConfigs.length);
        const baseConfig = fragmentConfigs[configIndex];

        if (!baseConfig) {
          throw new Error(`Fragment config not found at index ${configIndex}`);
        }

        // 计算散布角度和方向
        const spreadRad = (spread * Math.PI) / 180; // 转换为弧度
        const angle = (Math.random() - 0.5) * spreadRad; // -spread/2 到 +spread/2

        // 计算方向向量
        const forceVariation = force * (0.7 + Math.random() * 0.6); // 力度变化 ±30%
        const direction = {
          x: Math.sin(angle) * forceVariation,
          y: -Math.cos(angle) * forceVariation // 负值表示向上
        };

        // 随机调整尺寸 ±30%
        const sizeVariation = 0.7 + Math.random() * 0.6;

        // 随机延迟
        const fragmentDelay = delay + (Math.random() * 0.3);

        return {
          id: `confetti-${index}`,
          config: {
            ...baseConfig,
            size: Math.round(baseConfig.size * sizeVariation)
          },
          direction,
          delay: fragmentDelay
        };
      });
    }, [count, fragmentConfigs, spread, force, delay]);

    return (
      <div className={className}>
        {fragments.map((fragment) => (
          <UniversalFragment
            key={fragment.id}
            shape={fragment.config.shape}
            size={fragment.config.size}
            color={fragment.config.color}
            strokeColor={fragment.config.strokeColor}
            strokeWidth={fragment.config.strokeWidth}
            borderRadius={fragment.config.borderRadius}
            animationType="confetti"
            direction={fragment.direction}
            delay={fragment.delay}
            duration={duration}
            className="absolute"
            position={{ x: 50, y: 50 }}
          />
        ))}
      </div>
    );
  };