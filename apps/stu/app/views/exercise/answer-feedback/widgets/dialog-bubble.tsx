import React from 'react';

interface DialogBubbleProps {
  children: React.ReactNode;
  className?: string;
}

export function DialogBubble({ children, className = '' }: DialogBubbleProps) {
  return (
    <div className={`dialog-bubble-container relative inline-block  ${className}`}>
      {/* 气泡主体 */}
      <div className="dialog-bubble-main relative ml-4">
        {/* 气泡背景 */}
        <div
          className="dialog-bubble-bg bg-[#F7F6F5] border border-[rgba(51,46,41,0.06)] rounded-xl px-4 py-3 min-w-[120px] w-fit"
          style={{
            filter: 'drop-shadow(0 2px 8px rgba(51, 46, 41, 0.08))',
            maxWidth: 'calc(80vw - 32px)' // 减去左边距和尾巴宽度
          }}
        >
          {/* 气泡内容 */}
          <div
            className="dialog-bubble-content text-gray-800 text-base leading-relaxed"
            style={{
              wordWrap: 'break-word',
              whiteSpace: 'pre-wrap',
              overflowWrap: 'break-word'
            }}
          >
            {children}
          </div>
        </div>

        {/* 气泡尾巴 - 使用倾斜加圆角实现 */}
        <div
          className="dialog-bubble-tail absolute left-[-0.18vw] top-2/3 w-[1.5vw] h-[1.5vw] bg-[#F7F6F5] border-l border-b border-[rgba(51,46,41,0.06)] rounded-bl-[8px]"
          style={{
            transform: 'translateX(-6px) translateY(-50%) rotate(0) skew(-45deg, 0deg)',
            transformOrigin: 'center',
          }}
        />
      </div>
    </div>
  );
}