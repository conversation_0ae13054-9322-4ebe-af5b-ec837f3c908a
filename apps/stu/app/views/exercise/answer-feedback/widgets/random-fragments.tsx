import React, { useMemo } from "react";
import { UniversalFragment, FragmentShape, AnimationType } from "./universal-fragment";

export interface RandomFragmentsProps {
  count?: number; // 碎片数量
  className?: string;
}

// 碎片配置接口
interface FragmentConfig {
  shape: FragmentShape;
  color: {
    type: 'solid' | 'gradient';
    primary: string;
    secondary?: string;
    direction?: 'horizontal' | 'vertical' | 'diagonal';
  };
  size: number;
  strokeColor?: string;
  strokeWidth?: number;
  borderRadius?: number;
}

/**
 * 全屏随机碎片组件
 * 在屏幕上随机分布各种形状和颜色的碎片
 */
export const RandomFragments: React.FC<RandomFragmentsProps> = ({
  count = 15,
  className
}) => {
  // 预定义的形状配置
  const shapeConfigs = useMemo((): FragmentConfig[] => [
    // 紫色圆形
    {
      shape: 'circle' as FragmentShape,
      color: {
        type: 'gradient' as const,
        primary: '#ACA6FF',
        secondary: '#8B7EFF',
        direction: 'diagonal' as const
      },
      size: 16
    },
    // 白色正方形
    {
      shape: 'square' as FragmentShape,
      color: {
        type: 'solid' as const,
        primary: '#FFFFFF'
      },
      size: 14,
      strokeColor: '#E5E7EB',
      strokeWidth: 1
    },
    // 黄色圆角正方形
    {
      shape: 'square' as FragmentShape,
      color: {
        type: 'gradient' as const,
        primary: '#FFD630',
        secondary: '#FFC107',
        direction: 'vertical' as const
      },
      size: 18,
      borderRadius: 4
    },
    // 三角形
    {
      shape: 'triangle' as FragmentShape,
      color: {
        type: 'gradient' as const,
        primary: '#81D7C1',
        secondary: '#4FC3F7',
        direction: 'horizontal' as const
      },
      size: 20
    },
    // 星星
    {
      shape: 'star' as FragmentShape,
      color: {
        type: 'gradient' as const,
        primary: '#FFE082',
        secondary: '#FFC107',
        direction: 'diagonal' as const
      },
      size: 22
    },
    // 空心正方形
    {
      shape: 'hollow-square' as FragmentShape,
      color: {
        type: 'solid' as const,
        primary: 'transparent'
      },
      size: 16,
      strokeColor: '#EBD8FF',
      strokeWidth: 2,
      borderRadius: 3
    },
    // 毛毛虫/不规则斑点
    {
      shape: 'arc-strip' as FragmentShape,
      color: {
        type: 'gradient' as const,
        primary: '#ACA6FF',
        secondary: '#C4B5FD',
        direction: 'diagonal' as const
      },
      size: 24
    },
    // 钻石形
    {
      shape: 'diamond' as FragmentShape,
      color: {
        type: 'gradient' as const,
        primary: '#FF9861',
        secondary: '#FF6B35',
        direction: 'vertical' as const
      },
      size: 18
    }
  ], []);

  // 生成随机碎片配置
  const fragments = useMemo(() => {
    // 动画类型选项
    const animationTypes: AnimationType[] = ['float', 'sparkle', 'drift'];
    // 随机位置（避开中心区域）
    const getRandomPosition = () => {
      const centerX = 50;
      const centerY = 50;
      const exclusionRadius = 25; // 中心排除区域半径（百分比）

      let x, y;
      do {
        x = Math.random() * 100;
        y = Math.random() * 100;
      } while (
        Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2)) < exclusionRadius
      );

      return { x, y };
    };

    // 随机漂移方向
    const getDriftDirection = () => {
      const angle = Math.random() * 2 * Math.PI;
      const distance = 50 + Math.random() * 100; // 50-150px
      return {
        x: Math.cos(angle) * distance,
        y: Math.sin(angle) * distance
      };
    };

    return Array.from({ length: count }, (_, index) => {
      const configIndex = Math.floor(Math.random() * shapeConfigs.length);
      const config = shapeConfigs[configIndex];
      const animationIndex = Math.floor(Math.random() * animationTypes.length);
      const animationType = animationTypes[animationIndex];

      if (!config) {
        throw new Error(`Config not found at index ${configIndex}`);
      }

      return {
        id: `fragment-${index}`,
        shape: config.shape,
        color: config.color,
        strokeColor: config.strokeColor,
        strokeWidth: config.strokeWidth,
        borderRadius: config.borderRadius,
        animationType,
        position: getRandomPosition(),
        direction: getDriftDirection(),
        delay: Math.random() * 2, // 0-2秒随机延迟
        duration: 3 + Math.random() * 4, // 3-7秒随机持续时间
        // 随机尺寸变化 ±20%
        size: config.size * (0.8 + Math.random() * 0.4)
      };
    });
  }, [count, shapeConfigs]);

  return (
    <div className={`fixed inset-0 pointer-events-none z-5 ${className} random-fragment`}>
      {fragments.map((fragment) => (
        <UniversalFragment
          key={fragment.id}
          shape={fragment.shape}
          size={fragment.size}
          color={fragment.color}
          strokeColor={fragment.strokeColor}
          strokeWidth={fragment.strokeWidth}
          borderRadius={fragment.borderRadius}
          animationType={fragment.animationType}
          direction={fragment.direction}
          delay={fragment.delay}
          duration={fragment.duration}
          position={{
            x: fragment.position.x,
            y: fragment.position.y
          }}
          className="transform -translate-x-1/2 -translate-y-1/2"
        />
      ))}
    </div>
  );
};
