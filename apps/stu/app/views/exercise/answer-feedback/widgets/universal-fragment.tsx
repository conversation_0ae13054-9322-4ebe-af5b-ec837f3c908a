import { motion, Easing } from "framer-motion";
import { cn } from "@repo/ui/lib/utils";

// 形状类型定义
export type FragmentShape = 
  | 'circle' 
  | 'square' 
  | 'triangle' 
  | 'star' 
  | 'hollow-square' 
  | 'arc-strip' // 毛毛虫形状，更准确的名称是"不规则斑点"
  | 'diamond'
  | 'hexagon';

// 颜色配置类型
export interface ColorConfig {
  type: 'solid' | 'gradient';
  primary: string;
  secondary?: string; // 渐变时使用
  direction?: 'horizontal' | 'vertical' | 'diagonal'; // 渐变方向
}

// 动画类型
export type AnimationType = 'float' | 'confetti' | 'sparkle' | 'drift';

// 组件Props
export interface UniversalFragmentProps {
  shape: FragmentShape;
  size?: number; // 基础尺寸，单位px
  color: ColorConfig;
  strokeColor?: string;
  strokeWidth?: number;
  borderRadius?: number; // 对支持的形状生效
  className?: string;
  animationType?: AnimationType;
  direction?: { x: number; y: number }; // confetti动画使用
  delay?: number;
  duration?: number;
  position?: { x: number; y: number }; // 绝对定位
}

/**
 * 通用碎片组件 - 支持多种形状、颜色和动画效果
 */
export const UniversalFragment: React.FC<UniversalFragmentProps> = ({
  shape,
  size = 20,
  color,
  strokeColor,
  strokeWidth = 2,
  borderRadius = 0,
  className,
  animationType = 'float',
  direction = { x: 0, y: 0 },
  delay = 0,
  duration = 2,
  position
}) => {
  // 生成唯一ID避免渐变冲突
  const gradientId = `fragment-gradient-${Math.random().toString(36).substr(2, 9)}`;

  // 生成颜色样式
  const getColorStyle = () => {
    if (color.type === 'solid') {
      return { fill: color.primary, backgroundColor: color.primary };
    }

    // 渐变方向映射
    const gradientDirections = {
      horizontal: '90deg',
      vertical: '180deg',
      diagonal: '45deg'
    };

    const direction = gradientDirections[color.direction || 'diagonal'];
    const gradient = `linear-gradient(${direction}, ${color.primary}, ${color.secondary || color.primary})`;

    return { 
      background: gradient,
      fill: `url(#${gradientId})`
    };
  };

  // 渲染SVG渐变定义
  const renderGradientDef = () => {
    if (color.type !== 'gradient') return null;

    const coords = {
      horizontal: { x1: '0%', y1: '0%', x2: '100%', y2: '0%' },
      vertical: { x1: '0%', y1: '0%', x2: '0%', y2: '100%' },
      diagonal: { x1: '0%', y1: '0%', x2: '100%', y2: '100%' }
    };

    const coord = coords[color.direction || 'diagonal'];

    return (
      <defs>
        <linearGradient id={gradientId} {...coord}>
          <stop offset="0%" stopColor={color.primary} />
          <stop offset="100%" stopColor={color.secondary || color.primary} />
        </linearGradient>
      </defs>
    );
  };

  // 渲染不同形状
  const renderShape = () => {
    const colorStyle = getColorStyle();
    const strokeStyle = strokeColor ? { stroke: strokeColor, strokeWidth } : {};

    switch (shape) {
      case 'circle':
        return (
          <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
            {renderGradientDef()}
            <circle
              cx={size / 2}
              cy={size / 2}
              r={(size - strokeWidth) / 2}
              {...colorStyle}
              {...strokeStyle}
            />
          </svg>
        );

      case 'square':
        return (
          <div
            style={{
              width: size,
              height: size,
              borderRadius: borderRadius,
              border: strokeColor ? `${strokeWidth}px solid ${strokeColor}` : 'none',
              ...colorStyle
            }}
          />
        );

      case 'triangle':
        return (
          <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
            {renderGradientDef()}
            <polygon
              points={`${size/2},${strokeWidth} ${size-strokeWidth},${size-strokeWidth} ${strokeWidth},${size-strokeWidth}`}
              {...colorStyle}
              {...strokeStyle}
            />
          </svg>
        );

      case 'star':
        return (

          <svg width={size} height={size} viewBox="0 0 16 16">
            {renderGradientDef()}
            <path
              d="M1.17222 4.30608C0.217794 2.9079 1.70298 1.13792 3.24565 1.83506L6.9788 3.52208C7.51123 3.76269 8.13056 3.7085 8.61312 3.3791L11.9966 1.06945C13.3948 0.115024 15.1648 1.60021 14.4676 3.14288L12.7806 6.87603C12.54 7.40846 12.5942 8.02779 12.9236 8.51035L15.2332 11.8938C16.1877 13.292 14.7025 15.062 13.1598 14.3649L9.42665 12.6778C8.89422 12.4372 8.2749 12.4914 7.79234 12.8208L4.40885 15.1305C3.01067 16.0849 1.24069 14.5997 1.93783 13.057L3.62485 9.32388C3.86546 8.79145 3.81127 8.17213 3.48187 7.68957L1.17222 4.30608Z"
              {...colorStyle}
              {...strokeStyle}
            />
          </svg>
        );

      case 'hollow-square':
        return (
          <div
            style={{
              width: size,
              height: size,
              borderRadius: borderRadius,
              border: `${strokeWidth}px solid ${strokeColor || color.primary}`,
              backgroundColor: 'transparent'
            }}
          />
        );

      case 'arc-strip':
        return (
          <svg width={size} height={Math.round(size * 0.6)} viewBox="0 0 22 13">
            {renderGradientDef()}
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M1.12063 10.171C0.764842 9.24648 0.893694 8.194 1.53096 7.42251C2.9711 5.67906 5.86211 2.59626 9.29121 1.19077C12.6925 -0.203302 16.8056 -0.02202 19.0256 0.223693C20.0359 0.335519 20.8582 1.03634 21.227 1.99477C22.0929 4.24485 20.1228 6.77284 17.7324 6.86199C15.7851 6.93461 13.592 7.23917 11.6329 8.04214C9.81435 8.78753 8.20126 9.96081 6.88288 11.155C5.10949 12.7613 1.97335 12.3868 1.12063 10.171Z"
              {...colorStyle}
              {...strokeStyle}
            />
          </svg>
        );

      case 'diamond':
        return (
          <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
            {renderGradientDef()}
            <polygon
              points={`${size/2},${strokeWidth} ${size-strokeWidth},${size/2} ${size/2},${size-strokeWidth} ${strokeWidth},${size/2}`}
              {...colorStyle}
              {...strokeStyle}
            />
          </svg>
        );

      case 'hexagon':
        return (
          <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
            {renderGradientDef()}
            <polygon
              points={`${size*0.25},${strokeWidth} ${size*0.75},${strokeWidth} ${size-strokeWidth},${size*0.5} ${size*0.75},${size-strokeWidth} ${size*0.25},${size-strokeWidth} ${strokeWidth},${size*0.5}`}
              {...colorStyle}
              {...strokeStyle}
            />
          </svg>
        );

      default:
        return null;
    }
  };

  // 获取动画配置
  const getAnimationConfig = () => {
    switch (animationType) {
      case 'float':
        return {
          initial: { opacity: 0, scale: 0, y: 20 },
          animate: {
            opacity: [0, 1, 0.8, 1],
            scale: [0, 1.1, 0.9, 1],
            y: [20, 0, -5, 0],
            rotate: [0, 5, -5, 0]
          },
          transition: {
            duration,
            delay,
            ease: [0.4, 0, 0.2, 1] as Easing,
            repeat: Infinity,
            repeatType: "reverse" as const
          }
        };

      case 'sparkle':
        return {
          initial: { opacity: 0, scale: 0, rotate: 0 },
          animate: {
            opacity: [0, 1, 0.6, 1, 0],
            scale: [0, 1.2, 0.8, 1.1, 0],
            rotate: [0, 180, 360]
          },
          transition: {
            duration,
            delay,
            ease: [0.4, 0, 0.2, 1] as Easing,
            repeat: Infinity,
            repeatType: "loop" as const
          }
        };

      case 'drift':
        return {
          initial: { opacity: 0, x: 0, y: 0 },
          animate: {
            opacity: [0, 1, 1, 0],
            x: [0, direction.x * 0.5, direction.x],
            y: [0, direction.y * 0.5, direction.y],
            rotate: [0, 90, 180]
          },
          transition: {
            duration,
            delay,
            ease: [0, 0, 0.2, 1] as Easing
          }
        };

      case 'confetti': {
        // 复用原有的confetti逻辑
        const physics = {
          initialVelocityY: -40,
          gravity: 250,
          airResistance: 0.98
        };

        const trajectory = [];
        const steps = 16;
        for (let i = 0; i <= steps; i++) {
          const t = (i / steps) * duration;
          const x = direction.x * t * Math.pow(physics.airResistance, t * 10);
          const y = physics.initialVelocityY * t + 0.5 * physics.gravity * t * t;
          trajectory.push({ x, y });
        }

        return {
          initial: { x: 0, y: 0, opacity: 0, scale: 0, rotate: 0 },
          animate: {
            x: trajectory.map(p => p.x),
            y: trajectory.map(p => p.y),
            opacity: [0, 1, 1, 1, 0.8, 0.5, 0.2, 0],
            scale: [0, 1.2, 1, 1, 0.9, 0.7, 0.4, 0.1],
            rotate: [0, 90, 180, 270, 360]
          },
          transition: {
            duration,
            delay,
            ease: [0, 0, 1, 1] as Easing,
            times: [0, 0.1, 0.2, 0.4, 0.6, 0.8, 0.9, 1]
          }
        };
      }

      default:
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          transition: { duration, delay }
        };
    }
  };

  const animationConfig = getAnimationConfig();

  return (
    <motion.div
      className={cn("absolute", className)}
      style={{
        left: position ? `${position.x}%` : '50%',
        top: position ? `${position.y}%` : '50%',
        zIndex: 1
      }}
      {...animationConfig}
    >
      {renderShape()}
    </motion.div>
  );
};
