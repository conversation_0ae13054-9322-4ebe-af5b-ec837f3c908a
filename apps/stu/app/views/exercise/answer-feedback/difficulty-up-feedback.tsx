"use client";

import { useQuestionContext } from "@/app/contexts/question-context";
import { cn } from "@repo/ui/lib/utils";
import React, { useEffect } from "react";

import DifficultyUpLottie from "@/public/lottie/difficulty-up/data.json";
import Lottie from "lottie-react";
interface CorrectFeedbackModalProps {
  isOpen: boolean;
  onClose?: () => void;
  mainText?: string;
  subText?: string;
  className?: string;
  autoCloseDelay?: number; // 自动关闭延迟时间（毫秒），默认3000ms
}

/**
 * 错误答案反馈弹窗组件
 */
export const DifficultyUpFeedbackModal: React.FC<CorrectFeedbackModalProps> = ({
  isOpen,
  onClose,
  mainText = "别气馁",
  subText = "试着自己看看解析",
  className,
  autoCloseDelay = 3000,
}) => {
  const { trackEventWithExercise } = useQuestionContext();

  // 自动关闭功能
  useEffect(() => {
    if (isOpen && onClose && autoCloseDelay > 0) {
      trackEventWithExercise?.("answer_feedback_show", {
        feedback_type: "difficulty_up",
        feedback_content: subText,
        difficulty_direction: "up",
      });

      const timer = setTimeout(() => {
        onClose();
      }, autoCloseDelay);

      return () => clearTimeout(timer);
    }
  }, [isOpen, onClose, autoCloseDelay]);

  return (
    <div className="difficulty-up-feedback-container fixed inset-0 z-50 overflow-hidden">
      <Lottie
        className={cn(
          className,
          "difficulty-up-feedback-lottie absolute inset-0 h-full w-full"
        )}
        style={{
          width: "100%",
          height: "100%",
          minWidth: "100vw",
          minHeight: "100vh",
          objectFit: "cover",
        }}
        animationData={DifficultyUpLottie}
        autoPlay={true}
        loop={false}
        onComplete={onClose}
        rendererSettings={{
          preserveAspectRatio: "xMidYMid slice", // 保持宽高比并裁剪填满容器
        }}
      />
    </div>
  );
};
