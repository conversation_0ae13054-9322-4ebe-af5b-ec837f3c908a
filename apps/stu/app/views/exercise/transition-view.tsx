"use client";

import { TransitionItem } from "@/app/viewmodels/exercise/transition-viewmodel";
import type {
  NextQuestionGroupInfo,
  SpecialFeedback,
} from "@repo/core/exercise/model/types";
import React from "react";
import { FeedbackModal } from "./answer-feedback";

/**
 * 统一转场管理组件
 *
 * 负责渲染当前播放的转场：
 * - 题组转场动画
 * - 特殊反馈动画
 *
 * 🔥 简化版本：纯渲染组件，通过nextTransition获取当前播放配置
 */
export const TransitionView: React.FC<{
  currentTransition: TransitionItem | null;
  nextTransition: () => void;
}> = ({ currentTransition, nextTransition }) => {
  // 如果没有当前转场，不渲染任何内容
  if (!currentTransition) {
    return null;
  }

  // 根据转场类型渲染对应的组件
  const renderTransition = () => {
    if (currentTransition.type === "group") {
      const groupData = currentTransition.data as NextQuestionGroupInfo;
      return (
        <FeedbackModal
          key={currentTransition.id}
          isOpen={true}
          onClose={nextTransition} // 播放完成后自动推进到下一个
          autoCloseDelay={currentTransition.duration}
          groupInfo={{
            type: "question_group_transition",
            ...groupData,
          }}
        />
      );
    }

    if (currentTransition.type === "special") {
      const feedbackData = currentTransition.data as SpecialFeedback;
      return (
        <FeedbackModal
          key={currentTransition.id}
          isOpen={true}
          onClose={nextTransition} // 播放完成后自动推进到下一个
          autoCloseDelay={currentTransition.duration}
          feedbackConten={feedbackData}
        />
      );
    }

    return null;
  };

  return renderTransition();
};
