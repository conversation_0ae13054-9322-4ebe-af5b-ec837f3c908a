# Exercise Components

## 统一确认弹窗系统

### 概述

本目录包含了练习模块的统一确认弹窗系统，将原本分散的多个弹窗组件（`MultipleChoiceConfirmModal`、`SubmissionConfirmationDialog`、`ExitConfirmModal`）合并为一个统一的 `UnifiedConfirmDialog` 组件。

### 组件结构

```
components/
├── README.md                           # 本文档
├── submission-confirmation-dialog.tsx  # 统一确认弹窗组件
├── choice-question-view.tsx           # 选择题视图
├── fill-blank-answer-area-view.tsx    # 填空题答题区域
├── fill-blank-question-content-view.tsx # 填空题内容视图
├── keyboard-input-view.tsx            # 键盘输入视图
├── question-action-buttons.tsx        # 题目操作按钮
└── camera-view.tsx                    # 拍照视图
```

### UnifiedConfirmDialog 组件

#### 支持的弹窗类型

```typescript
export type ConfirmDialogType = 
  | "multipleChoiceConfirm"           // 多选题只选一个确认
  | "subjectiveEmpty"                 // 主观题空白确认
  | "subjectivePartEmpty"             // 主观题部分空白确认
  | "subjectivePhotoEmpty"            // 主观题图片空白确认
  | "exitConfirm";                    // 退出确认
```

#### 配置接口

```typescript
export interface ConfirmDialogConfig {
  isVisible: boolean;
  type?: ConfirmDialogType;
  title?: string;
  subtitle?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  isLoading?: boolean;
}
```

#### 使用示例

##### 1. 多选题确认弹窗

```tsx
<UnifiedConfirmDialog
  config={{
    isVisible: true,
    type: "multipleChoiceConfirm",
    onConfirm: handleConfirm,
    onCancel: handleCancel,
  }}
/>
```

显示效果：
- 标题：多选题答案有多个
- 确认按钮：继续提交（灰色）
- 取消按钮：我再想想（橙色）

##### 2. 填空题确认弹窗

**全部空白**:
```tsx
<UnifiedConfirmDialog
  config={{
    isVisible: true,
    type: "subjectiveEmpty",
    title: "本题未作答，确认提交吗？",
    onConfirm: handleConfirm,
    onCancel: handleCancel,
  }}
/>
```

**部分空白**:
```tsx
<UnifiedConfirmDialog
  config={{
    isVisible: true,
    type: "subjectivePartEmpty",
    title: "还有填空未作答，确认提交吗？",
    onConfirm: handleConfirm,
    onCancel: handleCancel,
  }}
/>
```

**拍照模式无图片**:
```tsx
<UnifiedConfirmDialog
  config={{
    isVisible: true,
    type: "subjectivePhotoEmpty",
    title: "本题未作答，确认提交吗？",
    onConfirm: handleConfirm,
    onCancel: handleCancel,
  }}
/>
```

##### 3. 退出确认弹窗

```tsx
<UnifiedConfirmDialog
  config={{
    isVisible: true,
    type: "exitConfirm",
    onConfirm: handleConfirm,
    onCancel: handleCancel,
    isLoading: isExiting,
  }}
/>
```

显示效果：
- 标题：确认退出练习
- 副标题：进度已保存
- 确认按钮：确认退出（灰色）
- 取消按钮：取消（橙色）

### 与 ViewModel 集成

#### QuestionSubmissionViewModel

统一管理所有题型的提交确认逻辑：

```typescript
const submissionViewModel = useQuestionSubmissionViewModel();

// 在组件中使用
<UnifiedConfirmDialog
  config={submissionViewModel.confirmationDialog}
/>
```

#### ExitViewModel

管理退出确认逻辑：

```typescript
const exitViewModel = useExitViewModel();

// 在组件中使用
<UnifiedConfirmDialog
  config={{
    isVisible: exitViewModel.showExitConfirm,
    type: "exitConfirm",
    onConfirm: handleConfirmExit,
    onCancel: exitViewModel.handleCancelExit,
    isLoading: exitViewModel.isExiting,
  }}
/>
```

### 设计原则

1. **统一性**：所有确认弹窗使用相同的视觉设计和交互模式
2. **可扩展性**：通过 `type` 参数轻松添加新的弹窗类型
3. **类型安全**：完整的 TypeScript 类型定义
4. **语义化**：每个 DOM 元素都包含语义化的 class 名称
5. **可维护性**：集中管理弹窗逻辑，避免重复代码

### 迁移指南

#### 从旧组件迁移

1. **MultipleChoiceConfirmModal** → `type: "multipleChoiceConfirm"`
2. **SubmissionConfirmationDialog** → `type: "subjectiveEmpty"` 等
3. **ExitConfirmModal** → `type: "exitConfirm"`

#### 迁移步骤

1. 将旧的弹窗组件导入替换为 `UnifiedConfirmDialog`
2. 将 props 转换为 `ConfirmDialogConfig` 格式
3. 更新状态管理逻辑
4. 删除旧的弹窗组件文件

### 技术实现

- **基础组件**：基于扩展后的 `DialogView` 组件，使用 `@repo/ui/components/press-button`
- **样式管理**：使用 Tailwind CSS + press-button 的内置样式系统
- **状态管理**：通过 ViewModel 层统一管理
- **类型安全**：完整的 TypeScript 支持
- **按钮系统**：统一使用 `buttons` 数组控制，支持颜色、禁用状态等
- **命名规范**：前置校验函数统一以 `before` 开头，如 `beforeSubmitValidation`

### DialogView 改进

1. **统一按钮组件**：使用 `press-button` 替代原生 `button`，保持项目一致性
2. **简化接口**：只保留 `buttons` 数组，更容易控制每个按钮的状态
3. **语义化样式**：所有元素都有语义化的 class 名称
4. **支持副标题**：新增 `subtitle` 属性，用于显示二级信息

### 注意事项

1. 确保在使用前正确配置 `onConfirm` 和 `onCancel` 回调
2. `isLoading` 状态会禁用所有按钮，防止重复操作
3. 不同类型的弹窗有不同的默认文案，可通过配置覆盖
4. 组件会自动处理显示/隐藏逻辑，只需控制 `isVisible` 状态
5. 按钮颜色遵循 press-button 的颜色系统：`orange`、`red`、`green`、`gray`、`white`
6. **重要**：在 `awaiting_self_evaluation` 状态下，不会弹出动画反馈，也不会设置转场状态

### 自评状态特殊处理

当 `questionState` 为 `awaiting_self_evaluation` 时：
- 不显示答题反馈动画
- 不执行转场序列
- 不设置 `transitionState` 数据
- 确保自评流程的纯净体验

### 主观题提交校验逻辑

#### 统一按答案数量处理

不再区分问答题和填空题，统一按照答案数量来判断：

1. **一个都没答**（`emptyCount === totalCount`）
   - 弹窗文案：`"本题未作答，确认提交吗？"`
   - 类型：`subjectiveEmpty`

2. **答案数量不够**（`emptyCount > 0`）
   - 弹窗文案：`"还有填空未作答，确认提交吗？"`
   - 类型：`subjectivePartEmpty`

3. **拍照模式无图片**
   - 弹窗文案：`"本题未作答，确认提交吗？"`
   - 类型：`subjectivePhotoEmpty`

#### 示例场景

- **问答题只有一个空**：如果没答就是"本题未作答"
- **填空题有多个空**：如果全部没答就是"本题未作答"，部分没答就是"还有填空未作答"
- **逻辑统一**：不需要判断题型，只看答案数量
