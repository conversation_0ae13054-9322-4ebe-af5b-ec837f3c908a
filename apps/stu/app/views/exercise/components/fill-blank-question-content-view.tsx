import { useQuestionContext } from "@/app/contexts/question-context";
import {
  FillBlankViewModel,
} from "@/app/viewmodels/exercise/fill-blank-question-viewmodel";
import { FormatMath } from "@repo/core/exercise/components";
import { NextQuestionInfo } from "@repo/core/exercise/model";
import HalfRightIconRedSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/half-right-red.svg";
import RightIconGreenSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/right-green.svg";
import WrongIconRedSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/wrong-red.svg";
import { cn } from "@repo/ui/lib/utils";
import { FC, useEffect, useRef } from "react";
import { parseContent } from "@/app/utils/parseFillBlank";

interface QuestionContentProps {
  answers: { id: number; value: string }[];
  activeBlankIndex: number;
  onBlankClick: (index: number) => void;
}

/**
 * 填空题内容渲染组件
 *
 * ✅ 已适配新的统一架构：
 * - 移除对已删除的 useExerciseSessionState 的依赖
 * - 通过统一的 question-context 和 question-viewmodel 获取数据
 * - 添加语义化 class 名便于调试
 *
 * 职责：
 * - 解析并渲染填空题的题目内容
 * - 渲染可点击的空白填入区域
 * - 显示答案状态和自评结果
 */
const FillBlankQuestionContent = ({
  answers,
  activeBlankIndex,
  onBlankClick,
  isReview = false,
  blankRefs,
  question,
  selfEvaluation,
}: QuestionContentProps & {
  correctAnswers?: string[];
  isReview?: boolean;
  blankRefs?: React.MutableRefObject<(HTMLSpanElement | null)[]>;
  question: NextQuestionInfo;
  selfEvaluation?: ("right" | "partial" | "wrong" | null)[];
}) => {
  let blankIdx = 0;
  const paragraphs = parseContent(question.questionContent.questionStem || "");
  // 计算总空数
  const totalBlanks = answers.length;
  // 是否只有一个空
  const hasSingleBlank = totalBlanks === 1;

  // 检测是否为表格内容的函数
  const isTableContent = (parts: (string | null)[]): boolean => {
    return parts.some(
      (part) =>
        typeof part === "string" &&
        (part.includes("<table") ||
          part.includes("<tr") ||
          part.includes("<td") ||
          part.includes("<th"))
    );
  };

  // 将段落转换为HTML字符串的函数，处理表格内的空白
  const convertParagraphToHtml = (parts: (string | null)[]): string => {
    return parts
      .map((part) => {
        if (part === null) {
          const i = blankIdx;
          blankIdx++;
          // 为表格内的空白生成HTML结构
          let evalResult: "right" | "partial" | "wrong" | null = null;
          if (isReview && selfEvaluation && selfEvaluation[i]) {
            evalResult = selfEvaluation[i]!;
          }

          let colorClass = "text-[#0A8AC2]";
          let borderClass = "border-b-1";
          let bgClass = "";

          if (isReview && evalResult) {
            if (evalResult === "right") {
              borderClass = "border-b-[#84D64B]";
              colorClass = "text-[#449908] border-[#84D64B]";
            } else if (evalResult === "partial") {
              borderClass = "border-b-[#FFD466]";
              colorClass = "text-[#CC6204] border-[#FFD466]";
            } else if (evalResult === "wrong") {
              borderClass = "border-b-[#FF6139]";
              colorClass = "text-[#D1320A] border-[#FF6139]";
            }
            if (activeBlankIndex === i) {
              if (evalResult === "right") {
                bgClass = "bg-[rgba(132,214,75,0.3)]";
              } else if (evalResult === "partial") {
                bgClass = "bg-[rgba(255,212,102,0.3)]";
              } else if (evalResult === "wrong") {
                bgClass = "bg-[rgba(255,123,89,0.3)]";
              }
            }
          } else {
            // 未进入解析状态，选中高亮
            if (activeBlankIndex === i) {
              borderClass = "border-b-[#58C4FA]";
              bgClass = "";
              colorClass = "text-[#0A8AC2]";
            } else {
              borderClass = "border-b-1";
              bgClass = "";
              colorClass = "text-[#0A8AC2]";
            }
          }

          const answerValue = answers[i]?.value || "";
          const isValueClass = answerValue ? "ml-3" : "";

          let iconHtml = "";
          if (isReview && evalResult) {
            if (evalResult === "right") {
              iconHtml = `<svg class="ml-1 inline-block h-4 w-4 align-middle" viewBox="0 0 16 16" fill="currentColor"><path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/></svg>`;
            } else if (evalResult === "partial") {
              iconHtml = `<svg class="ml-1 inline-block h-4 w-4 align-middle" viewBox="0 0 16 16" fill="currentColor"><path d="M2.56566 6.92433C2.87808 6.61191 3.38461 6.61191 3.69703 6.92433L7.58234 10.8096C7.89476 11.1221 7.89476 11.6286 7.58234 11.941C7.26992 12.2534 6.76339 12.2534 6.45097 11.941L2.56566 8.0557C2.25324 7.74328 2.25324 7.23675 2.56566 6.92433Z" fill="currentColor"/><path d="M8.56566 4.56569C8.87808 4.25327 9.38461 4.25327 9.69703 4.56569L13.8652 8.73383C14.1776 9.04625 14.1776 9.55278 13.8652 9.8652C13.5528 10.1776 13.0462 10.1776 12.7338 9.8652L8.56566 5.69706C8.25324 5.38464 8.25324 4.8781 8.56566 4.56569Z" fill="currentColor"/><rect x="14.5498" y="5.13135" width="10.5368" height="1.6" rx="0.8" transform="rotate(135 14.5498 5.13135)" fill="currentColor"/></svg>`;
            } else if (evalResult === "wrong") {
              iconHtml = `<svg class="ml-1 inline-block h-4 w-4 align-middle" viewBox="0 0 16 16" fill="currentColor"><path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/></svg>`;
            }
          }

          let numberIndicatorClass =
            "border-1 flex h-5 w-5 flex-shrink-0 cursor-pointer items-center justify-center rounded-full border-solid text-center text-xs";
          if (isReview && evalResult) {
            if (activeBlankIndex === i && evalResult === "right") {
              numberIndicatorClass +=
                " border-[#84D64B] bg-[#84D64B] text-white";
            } else if (activeBlankIndex === i && evalResult === "partial") {
              numberIndicatorClass +=
                " border-[#FFD466] bg-[#FFD466] text-white";
            } else if (activeBlankIndex === i && evalResult === "wrong") {
              numberIndicatorClass +=
                " border-[#FF6139] bg-[#FF6139] text-white";
            } else {
              numberIndicatorClass += ` ${colorClass}`;
            }
          } else {
            if (activeBlankIndex === i) {
              numberIndicatorClass += answers[i]?.value
                ? " border-[#58C4FA] bg-[#58C4FA] text-white"
                : " border-[#58C4FA] bg-[#58C4FA] text-white";
            } else {
              numberIndicatorClass += ` ${colorClass}`;
            }
          }

          return `
          <span
            class="fill_blank_input_area border-b-1 relative mx-1 inline-flex h-8 w-[10.625rem] cursor-pointer items-center justify-center align-top ${borderClass} ${colorClass} ${bgClass || ""}"
            data-blank-index="${i}"
            onclick="window.handleBlankClick && window.handleBlankClick(${i})"
          >
            ${
              !hasSingleBlank
                ? `<span class="fill_blank_number_indicator ${numberIndicatorClass} ${isValueClass}">
              ${i + 1}
            </span>`
                : ""
            }
            ${answerValue ? `<span class="fill_blank_answer_text ${hasSingleBlank ? "" : "ml-2"} flex-1 truncate ${colorClass}">${answerValue}</span>` : ""}
            ${iconHtml ? `<span class="fill_blank_evaluation_icon mr-3 flex items-center">${iconHtml}</span>` : ""}
          </span>
        `;
        }
        return part;
      })
      .join("");
  };

  // 渲染空白区域的函数（用于非表格内容）
  const renderBlank = (i: number) => {
    let evalResult: "right" | "partial" | "wrong" | null = null;
    if (isReview && selfEvaluation && selfEvaluation[i]) {
      evalResult = selfEvaluation[i]!;
    }
    let colorClass = "text-[#0A8AC2]";
    let borderClass = "border-b-1";
    let bgClass = "";
    let icon = null;
    if (isReview && evalResult) {
      if (evalResult === "right") {
        borderClass = "border-b-[#84D64B]";
        colorClass = "text-[#449908] border-[#84D64B]";
        icon = (
          <RightIconGreenSvg className="ml-1 inline-block h-4 w-4 align-middle" />
        );
      } else if (evalResult === "partial") {
        borderClass = "border-b-[#FFD466]";
        colorClass = "text-[#CC6204] border-[#FFD466]";
        icon = (
          <HalfRightIconRedSvg className="ml-1 inline-block h-4 w-4 align-middle" />
        );
      } else if (evalResult === "wrong") {
        borderClass = "border-b-[#FF6139]";
        colorClass = "text-[#D1320A] border-[#FF6139]";
        icon = (
          <WrongIconRedSvg className="ml-1 inline-block h-4 w-4 align-middle" />
        );
      }
      if (activeBlankIndex === i) {
        if (evalResult === "right") {
          bgClass = "bg-[rgba(132,214,75,0.3)]";
        } else if (evalResult === "partial") {
          bgClass = "bg-[rgba(255,212,102,0.3)]";
        } else if (evalResult === "wrong") {
          bgClass = "bg-[rgba(255,123,89,0.3)]";
        }
      }
    } else {
      // 未进入解析状态，选中高亮
      if (activeBlankIndex === i) {
        borderClass = "border-b-[#58C4FA]";
        bgClass = "";
        colorClass = "text-[#0A8AC2]";
      } else {
        borderClass = "border-b-1";
        bgClass = "";
        colorClass = "text-[#0A8AC2]";
      }
    }
    // 字数过多深色
    const isLong = (answers[i]?.value || "").length > 6;
    const longClass = isLong ? "" : "";
    const isValueClass = answers[i]?.value ? "ml-3" : "";
    return (
      <span
        key={"blank-" + i}
        ref={(el) => {
          if (blankRefs) blankRefs.current[i] = el;
        }}
        className={`fill_blank_input_area border-b-1 relative mx-1 inline-flex h-8 w-[10.625rem] cursor-pointer items-center justify-center align-top ${borderClass} ${colorClass} ${bgClass || ""}`}
        onClick={() => onBlankClick(i)}
      >
        {!hasSingleBlank && (
          <span
            className={cn(
              "fill_blank_number_indicator border-1 flex h-5 w-5 flex-shrink-0 cursor-pointer items-center justify-center rounded-full border-solid text-center text-xs",
              isValueClass,
              isReview && evalResult
                ? activeBlankIndex === i && evalResult === "right"
                  ? "border-[#84D64B] bg-[#84D64B] text-white"
                  : activeBlankIndex === i && evalResult === "partial"
                    ? "border-[#FFD466] bg-[#FFD466] text-white"
                    : activeBlankIndex === i && evalResult === "wrong"
                      ? "border-[#FF6139] bg-[#FF6139] text-white"
                      : colorClass
                : activeBlankIndex === i
                  ? answers[i]?.value
                    ? "border-[#58C4FA] bg-[#58C4FA] text-white"
                    : "border-[#58C4FA] bg-[#58C4FA] text-white"
                  : colorClass
            )}
          >
            {i + 1}
          </span>
        )}
        {answers[i]?.value && (
          <span
            className={`fill_blank_answer_text ${hasSingleBlank ? "" : "ml-2"} flex-1 truncate ${colorClass} ${longClass}`}
          >
            {answers[i].value}
          </span>
        )}
        {isReview && evalResult && (
          <span className="fill_blank_evaluation_icon mr-3 flex items-center">
            {icon}
          </span>
        )}
      </span>
    );
  };

  // 设置全局点击处理函数（用于表格内的空白点击）
  useEffect(() => {
    (window as any).handleBlankClick = onBlankClick;
    return () => {
      delete (window as any).handleBlankClick;
    };
  }, [onBlankClick]);

  return (
    <div className="fill_blank_question_content_container h-full w-full">
      <div className="fill_blank_content_wrapper flex flex-col gap-4">
        <div className="fill_blank_text_content text-base leading-8">
          {paragraphs.map((parts, pIdx) => {
            // 重置blankIdx，确保每个段落都有正确的空白索引
            if (pIdx === 0) blankIdx = 0;

            if (isTableContent(parts)) {
              // 表格内容：使用dangerouslySetInnerHTML渲染
              const tableHtml = convertParagraphToHtml(parts);
              return (
                <div
                  key={pIdx}
                  className="fill_blank_table_container mb-5"
                  dangerouslySetInnerHTML={{ __html: tableHtml }}
                />
              );
            } else {
              // 普通段落：使用现有的渲染逻辑
              return (
                <div
                  key={pIdx}
                  className="fill_blank_paragraph mb-5 leading-[190%]"
                >
                  {parts.map((part, idx) => {
                    if (part === null) {
                      const i = blankIdx;
                      blankIdx++;
                      return renderBlank(i);
                    }
                    return (
                      <FormatMath
                        key={"part-" + idx}
                        htmlContent={part}
                        questionId={question.questionId}
                      />
                    );
                  })}
                </div>
              );
            }
          })}
        </div>
      </div>
    </div>
  );
};

/**
 * 填空题内容视图组件
 *
 * ✅ 已适配新的统一架构：
 * - 只需要调用 useQuestionContext 获取题目数据
 * - 移除重复的ViewModel调用，简化数据获取
 *
 * 职责：
 * - 获取题目数据并传递给内容渲染组件
 * - 管理空白区域的引用和滚动行为
 */
export const FillBlankQuestionContentView: FC<{
  viewModel?: FillBlankViewModel;
}> = ({ viewModel }) => {
  // 🎯 只需要从统一Context获取题目数据
  const { currentQuestion: question } = useQuestionContext();

  // Hooks 必须在顶层调用
  const blankRefs = useRef<(HTMLSpanElement | null)[]>([]);

  // 安全地获取 activeBlankIndex，如果 viewModel 不存在则使用默认值
  // 如果 inputMode 是拍照模式，activeBlankIndex 应该为 -1
  // const rawActiveBlankIndex = viewModel?.activeBlankIndex ?? 0;
  // const inputMode = viewModel?.inputMode ?? "keyboard";
  // const activeBlankIndex = inputMode === "camera" ? 0 : rawActiveBlankIndex;

  useEffect(() => {
    if (viewModel && viewModel.activeBlankIndex >= 0) {
      const el = blankRefs.current[viewModel.activeBlankIndex];
      if (el) {
        // 查找最近的滚动容器，避免滚动整个页面
        const findScrollContainer = (
          element: HTMLElement
        ): HTMLElement | null => {
          let parent = element.parentElement;
          while (parent) {
            const style = window.getComputedStyle(parent);
            if (
              style.overflowY === "auto" ||
              style.overflowY === "scroll" ||
              style.overflow === "auto" ||
              style.overflow === "scroll" ||
              parent.classList.contains("scroll-container") ||
              // 识别 QuestionContent 中的滚动容器
              parent.classList.contains("question-main-content") ||
              // 通过父元素的类名识别
              (parent.parentElement &&
                parent.parentElement.classList.contains(
                  "question-main-content"
                ))
            ) {
              return parent;
            }
            parent = parent.parentElement;
          }
          return null;
        };

        const scrollContainer = findScrollContainer(el);

        if (scrollContainer) {
          // 在指定容器内滚动
          const containerRect = scrollContainer.getBoundingClientRect();
          const elementRect = el.getBoundingClientRect();

          // 计算元素相对于容器的位置
          const relativeTop = elementRect.top - containerRect.top;
          const containerHeight = containerRect.height;

          // 如果元素不在可视区域内，则滚动到中心位置
          if (
            relativeTop < 0 ||
            relativeTop > containerHeight - elementRect.height
          ) {
            const targetScrollTop =
              scrollContainer.scrollTop +
              relativeTop -
              containerHeight / 2 +
              elementRect.height / 2;

            scrollContainer.scrollTo({
              top: targetScrollTop,
              behavior: "smooth",
            });
          }
        } else {
          // 如果没找到滚动容器，查找具有 overflow-y-auto 的父元素
          let fallbackContainer = el.parentElement;
          while (fallbackContainer) {
            const style = window.getComputedStyle(fallbackContainer);
            if (style.overflowY === "auto" || style.overflow === "auto") {
              // 找到了具有滚动属性的容器，使用它进行滚动
              const containerRect = fallbackContainer.getBoundingClientRect();
              const elementRect = el.getBoundingClientRect();
              const relativeTop = elementRect.top - containerRect.top;
              const containerHeight = containerRect.height;

              if (
                relativeTop < 0 ||
                relativeTop > containerHeight - elementRect.height
              ) {
                const targetScrollTop =
                  fallbackContainer.scrollTop +
                  relativeTop -
                  containerHeight / 2 +
                  elementRect.height / 2;

                fallbackContainer.scrollTo({
                  top: targetScrollTop,
                  behavior: "smooth",
                });
              }
              return;
            }
            fallbackContainer = fallbackContainer.parentElement;
          }

          // 最后的备选方案：使用原来的方法
          el.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest",
          });
        }
      }
    }
  }, [viewModel]);

  if (!question) {
    return <div className="fill_blank_content_loading">加载中...</div>;
  }

  // 如果没有传入 viewModel，显示占位符
  if (!viewModel) {
    return (
      <div className="fill_blank_content_placeholder py-8 text-center">
        <div className="text-gray-500">填空题 ViewModel 未初始化</div>
      </div>
    );
  }

  const {
    answers,
    handleBlankClick,
    isReview,
    correctAnswers,
    selfEvaluation,
  } = viewModel;

  return (
    <FillBlankQuestionContent
      question={question}
      answers={answers}
      activeBlankIndex={viewModel.activeBlankIndex}
      onBlankClick={handleBlankClick}
      correctAnswers={correctAnswers}
      isReview={isReview}
      blankRefs={blankRefs}
      selfEvaluation={selfEvaluation}
    />
  );
};
