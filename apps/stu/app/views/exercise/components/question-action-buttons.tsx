"use client";

import { StudyType } from "@/app/models/exercise";
import Button from "@repo/ui/components/press-button";
import React from "react";
import type { QuestionState } from "../../../../types/app/exercise";

// 按钮配置接口
export interface QuestionActionButtonsProps {
  // 题目状态
  questionState: QuestionState;

  // 答案状态
  isAnswerComplete: boolean;
  isSubmitting: boolean;
  isProgressBarAnimating: boolean;

  // 操作函数
  onSubmit: () => void;
  onContinue: () => void;
  onUncertainClick: () => void;
  onGiveUpClick: () => void;

  // 转场状态
  isPlayingTransitions: boolean;

  // 🆕 自评相关状态
  shouldShowSelfEvaluation?: boolean; // 是否需要显示自评

  // 练习类型
  studyType: StudyType;

  // 🆕 下一题状态
  hasNextQuestion?: boolean; // 是否有下一题
}

export const QuestionActionButtons: React.FC<QuestionActionButtonsProps> = ({
  questionState,
  isAnswerComplete,
  isSubmitting,
  onSubmit,
  onContinue,
  onUncertainClick,
  onGiveUpClick,
  isPlayingTransitions,
  shouldShowSelfEvaluation = true,
  studyType,
  hasNextQuestion = true,
  isProgressBarAnimating,
}) => {
  // 判断是否显示"放弃作答"按钮
  const shouldShowGiveUpButton = questionState === "uncertain";

  // 获取继续按钮的文案
  const getContinueButtonText = () => {
    // 如果没有下一题且不是AI课类型，显示"查看报告"
    if (!hasNextQuestion && studyType !== StudyType.AI_COURSE) {
      return "查看报告";
    }
    return "继续";
  };
  // 已提交或放弃状态：只显示继续按钮
  if (questionState === "submitted" || questionState === "giving_up") {
    return (
      <Button
        color="study-theme"
        studyType={studyType}
        onClick={onContinue}
        disabled={isPlayingTransitions || isProgressBarAnimating}
      >
        {getContinueButtonText()}
      </Button>
    );
  }

  // 等待自评状态：根据是否需要自评显示不同按钮
  if (questionState === "awaiting_self_evaluation") {
    if (!shouldShowSelfEvaluation) {
      // 空答案或放弃作答，直接显示继续按钮
      return (
        <Button
          color="study-theme"
          studyType={studyType}
          onClick={onContinue}
          disabled={isPlayingTransitions || isProgressBarAnimating}
        >
          {getContinueButtonText()}
        </Button>
      );
    } else {
      // 有有效答案，显示提交自评按钮
      return (
        <Button
          color="study-theme"
          studyType={studyType}
          onClick={onSubmit}
          disabled={!isAnswerComplete || isSubmitting}
          loading={isSubmitting}
        >
          提交自评
        </Button>
      );
    }
  }

  // 答题状态：显示相关按钮
  return (
    <>
      {/* 不确定/放弃作答按钮 */}
      {shouldShowGiveUpButton ? (
        <Button
          color="study-theme"
          studyType={studyType}
          secondary
          onClick={onGiveUpClick}
        >
          放弃作答
        </Button>
      ) : (
        <Button color="white" onClick={onUncertainClick}>
          不确定
        </Button>
      )}

      {/* 提交按钮 */}
      <Button
        color="study-theme"
        onClick={onSubmit}
        studyType={studyType}
        disabled={!isAnswerComplete || isSubmitting || isProgressBarAnimating}
        loading={isSubmitting}
      >
        提交
      </Button>
    </>
  );
};
