"use client";

import React from "react";
import { DialogView } from "@/app/components/dialog/default-dialog";
import { StudyType } from "@/app/models/exercise";

/**
 * 弹窗类型枚举
 */
export type ConfirmDialogType =
  | "multipleChoiceConfirm" // 多选题只选一个确认
  | "subjectiveEmpty" // 主观题空白确认
  | "subjectivePartEmpty" // 主观题部分空白确认
  | "subjectivePhotoEmpty" // 主观题图片空白确认
  | "exitConfirm"; // 退出确认

/**
 * 弹窗配置接口
 */
export interface ConfirmDialogConfig {
  isVisible: boolean;
  type?: ConfirmDialogType;
  title?: string;
  subtitle?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  onClose?: () => void;
  isLoading?: boolean;
  studyType?: StudyType;
}

/**
 * 统一确认弹窗组件
 *
 * 基于 DialogView 实现，支持所有类型的确认弹窗：
 * - 多选题确认
 * - 主观题提交确认
 * - 退出确认
 * - 其他需要用户确认的情况
 */
export const ExerciseConfirmDialog: React.FC<{
  config: ConfirmDialogConfig;
}> = ({ config }) => {
  if (!config.isVisible) {
    return null;
  }

  // 根据类型设置默认配置
  const getDefaultConfig = (type?: ConfirmDialogType) => {
    switch (type) {
      case "multipleChoiceConfirm":
        return {
          title: "多选题答案有多个",
          subtitle: undefined,
          message: undefined,
          confirmText: "我再想想",
          cancelText: "继续提交",
        };
      case "exitConfirm":
        return {
          title: "确认退出练习吗？",
          subtitle: "答题进度已保存",
          message: undefined,
          confirmText: "退出",
          cancelText: "继续练习",
        };
      case "subjectiveEmpty":
      case "subjectivePartEmpty":
      case "subjectivePhotoEmpty":
        return {
          title: config.title || "确认提交",
          subtitle: undefined,
          message: config.message,
          confirmText: "我再想想",
          cancelText: "继续提交",
        };
      default:
        return {
          title: "确认操作",
          subtitle: undefined,
          message: undefined,
          confirmText: "确定",
          cancelText: "取消",
        };
    }
  };

  const defaultConfig = getDefaultConfig(config.type);
  const finalConfig = {
    title: config.title || defaultConfig.title,
    subtitle: config.subtitle || defaultConfig.subtitle,
    message: config.message || defaultConfig.message,
    confirmText: config.confirmText || defaultConfig.confirmText,
    cancelText: config.cancelText || defaultConfig.cancelText,
  };

  return (
    <DialogView
      title={finalConfig.title}
      subtitle={finalConfig.subtitle}
      open={config.isVisible}
      onClose={config.onClose || config.onCancel}
      buttons={[
        {
          text: finalConfig.confirmText,
          onClick: config.onConfirm || (() => {}),
          color: "white",
          studyType: config.studyType,
          disabled: config.isLoading,
        },
        {
          text: finalConfig.cancelText,
          onClick: config.onCancel || (() => {}),
          color: "study-theme",
          studyType: config.studyType,
          disabled: config.isLoading,
        },
      ]}
    >
      {finalConfig.message}
    </DialogView>
  );
};
