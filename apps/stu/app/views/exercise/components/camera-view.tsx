import CameraIconSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/camera-icon.svg";
import CloseIconSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/close.svg";
import IconLoadingSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/icon-loading.svg";
import React, { useState } from "react";

export interface BlankAnswer {
  id: number;
  value: string;
}

export interface ImageFile {
  id: number;
  file: File;
  preview: string;
  status?: "pending" | "success" | "error" | "audit-failed";
}

export type InputMode = "keyboard" | "camera";

const ImagePreviewModal: React.FC<{
  images: string[];
  current: number;
  onClose: () => void;
  onPrev: () => void;
  onNext: () => void;
}> = ({ images, current, onClose, onPrev, onNext }) => (
  <div
    style={{
      position: "fixed",
      top: 0,
      left: 0,
      width: "100vw",
      height: "100vh",
      background: "rgba(0,0,0,0.9)",
      zIndex: 9999,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      flexDirection: "column",
    }}
    onClick={onClose}
  >
    <div style={{ position: "relative", width: "100vw", height: "100vh" }}>
      <img
        src={images[current]}
        alt="预览"
        style={{
          height: "100vh",
          display: "block",
          margin: "0 auto",
        }}
        onClick={onClose}
      />
    </div>
  </div>
);

const CameraInput: React.FC<{
  imageFiles: ImageFile[];
  onRemoveImage: (id: number) => void;
  onRetryUpload: (id: number) => void;
  onTriggerNativeUpload: () => void;
}> = ({ imageFiles, onRemoveImage, onRetryUpload, onTriggerNativeUpload }) => {
  const [previewIndex, setPreviewIndex] = useState<number | null>(null);

  return (
    <div className="mt-3 flex flex-col">
      <div className="flex flex-wrap">
        {imageFiles.map((img, idx) => (
          <div key={img.id} className="relative mr-3">
            <img
              src={img.preview}
              alt="预览图"
              className="h-[100px] w-[100px] cursor-pointer rounded-lg border object-cover"
              style={{
                opacity: img.status === "pending" ? 0.5 : 1,
                borderColor:
                  img.status === "error" || img.status === "audit-failed"
                    ? "var(--color-red-1)"
                    : "var(--color-divider-1)",
                background: "var(--color-bg-white)",
              }}
              onClick={() => setPreviewIndex(idx)}
            />
            {/* loading 状态 */}
            {img.status === "pending" && (
              <div
                className="absolute inset-0 flex items-center justify-center rounded-lg"
                style={{ background: "var(--color-bg-white)", opacity: 0.7 }}
              >
                <span className="h-6 w-6 animate-spin rounded-full">
                  <IconLoadingSvg className="h-6 w-6" />
                </span>
              </div>
            )}
            {/* 上传失败或审核失败 */}
            {(img.status === "error" || img.status === "audit-failed") && (
              <div
                className="absolute inset-0 flex cursor-pointer flex-col items-center justify-center rounded-lg"
                style={{
                  background: "rgba(43,45,51,0.7)",
                  color: "white",
                }}
                onClick={() => onRetryUpload(img.id)}
              >
                {img.status === "audit-failed" ? (
                  <>
                    <p className="mb-1 text-xs">审核失败</p>
                    <p className="text-xs">重新上传</p>
                  </>
                ) : (
                  <>
                    <p className="mb-1 text-xs">上传失败</p>
                    <p className="text-xs">点击重试</p>
                  </>
                )}
              </div>
            )}
            {/* 删除按钮 */}
            <button
              className="absolute right-1 top-1 rounded-full"
              onClick={() => onRemoveImage(img.id)}
            >
              <CloseIconSvg
                className="h-6 w-6"
                style={{ color: "var(--color-gray-text)" }}
              />
            </button>
          </div>
        ))}
        {/* 上传按钮 */}
        {imageFiles.length < 3 && (
          <div
            className="border-1 flex h-[100px] w-[100px] cursor-pointer flex-col items-center justify-center rounded-lg border-solid bg-white"
            onClick={onTriggerNativeUpload}
          >
            <CameraIconSvg
              className="mb-1 h-8 w-8"
              style={{ color: "var(--color-gray-text)" }}
            />
            <div
              className="text-center"
              style={{ color: "var(--color-gray-text)" }}
            >
              <p className="font-resource-han-rounded text-xs font-normal leading-none">
                拍照上传
              </p>
              <p
                className="font-resource-han-rounded mt-1 text-[11px] font-normal leading-3"
                style={{ color: "var(--color-text-5)" }}
              >
                最多可以传 3 张
              </p>
            </div>
          </div>
        )}
      </div>
      {previewIndex !== null && (
        <ImagePreviewModal
          images={imageFiles.map((f) => f.preview)}
          current={previewIndex}
          onClose={() => setPreviewIndex(null)}
          onPrev={() => setPreviewIndex((i) => (i! > 0 ? i! - 1 : i!))}
          onNext={() =>
            setPreviewIndex((i) => (i! < imageFiles.length - 1 ? i! + 1 : i!))
          }
        />
      )}
    </div>
  );
};

export default CameraInput;
