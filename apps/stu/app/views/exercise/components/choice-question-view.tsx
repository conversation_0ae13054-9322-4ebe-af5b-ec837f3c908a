"use client";

import { QUESTION_TYPE } from "@repo/core/enums";
import { ChoiceOptionsList } from "@repo/core/exercise/components";
import { NextQuestionInfo, QuestionState } from "@repo/core/exercise/model";
import { useMemo } from "react";
import { useQuestionContext } from "../../../contexts/question-context";
import { useChoiceQuestionViewModel } from "../../../viewmodels/exercise/choice-question-viewmodel";

interface Props {
  questionState?: QuestionState;
  question?: NextQuestionInfo; // 可选，优先使用 Context 中的数据
}

export function ChoiceQuestionView({
  question: propQuestion,
  questionState = "answering",
}: Props) {
  // 🎯 优先从统一Context获取题目数据和正确答案，fallback到props
  const { currentQuestion, lastSubmitResult } = useQuestionContext();
  const question = currentQuestion || propQuestion;

  // 使用选择题专用ViewModel管理选择逻辑
  const choiceViewModel = useChoiceQuestionViewModel(question);

  // 支持选择题和判断题类型
  if (
    question?.questionType !== QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE &&
    question?.questionType !== QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE
  ) {
    throw new Error("ChoiceQuestionView 只支持单选题、多选题和判断题");
  }

  // 构建选项数据 - 使用 useMemo 缓存，避免无限重新渲染
  const options = useMemo(() => {
    if (!question) return [];
    return (
      question.questionContent.questionOptionList?.map((option) => ({
        id: option.optionKey || "",
        key: option.optionKey || "",
        content: option.optionVal || "",
        isCorrect: false, // 前端不需要知道正确答案
      })) || []
    );
  }, [question]);

  // 判断是否显示结果（提交后、放弃作答后、或等待自评状态）
  const showResult = useMemo(() => {
    const result =
      questionState === "submitted" ||
      questionState === "giving_up" ||
      questionState === "awaiting_self_evaluation";
    return result;
  }, [questionState]);

  // 计算实际传递给组件的选项状态
  const displaySelectedOptions = useMemo(() => {
    // 🔥 修复：首次答错时应该显示清空后的选择状态
    if (questionState === "first_attempt_incorrect") {
      return choiceViewModel.choiceAnswers; // 显示清空后的状态（应该是空数组）
    }
    if (questionState === "submitted") {
      return choiceViewModel.choiceAnswers; // 提交后显示用户的选择
    }
    // uncertain 和 giving_up 状态下，用户应该能看到并修改自己的选择
    return choiceViewModel.choiceAnswers;
  }, [questionState, choiceViewModel.choiceAnswers]);

  // 转换题目类型为组件需要的格式 - 使用 useMemo 缓存
  const questionTypeForComponent = useMemo(():
    | "single_choice"
    | "multiple_choice"
    | "true_false" => {
    if (!question) return "single_choice";
    if (question.questionType === QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE)
      return "multiple_choice";
    return "single_choice";
  }, [question]);

  return (
    <div className="choice-question-view">
      <ChoiceOptionsList
        questionId={question?.questionId}
        options={options}
        selectedOptions={displaySelectedOptions}
        onSelectOption={choiceViewModel.handleOptionClick}
        correctAnswer={question?.questionAnswer || undefined}
        showResult={showResult}
        questionType={questionTypeForComponent}
        answerStat={lastSubmitResult?.answerStats?.[0]?.answerStat || []}
      />
    </div>
  );
}
