import { FillBlankViewModel } from "@/app/viewmodels/exercise/fill-blank-question-viewmodel";
import { cn } from "@repo/ui/lib/utils";
import React, { useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";

export type BlankAnswer = {
  id: number;
  value: string;
};

export type InputMode = "keyboard" | "camera";

const KeyboardInput: React.FC<{
  viewModel: FillBlankViewModel;
  type: "fill-blank" | "solution" | "english-fill-blank";
}> = ({ viewModel }) => {
  const {
    answers,
    activeBlankIndex,
    handleTextChange,
    isReview,
    openInputDialog,
    closeInputDialog,
    showInputDialog,
    inputDialogValue,
    setInputDialogValue,
  } = viewModel;
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const maxRows = 3;
  const lineHeight = 24;
  const maxHeight = maxRows * lineHeight;
  const [isDialogVisible, setIsDialogVisible] = useState(false); // 控制蒙层动画
  const isConfirmingRef = useRef(false); // 标记是否正在点击确定按钮

  // 控制蒙层显示动画
  useEffect(() => {
    if (showInputDialog) {
      // 立即显示蒙层，避免闪烁
      setIsDialogVisible(true);
    } else {
      // 延迟隐藏蒙层，确保动画完成
      const timer = setTimeout(() => {
        setIsDialogVisible(false);
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [showInputDialog]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputDialogValue(e.target.value);
    viewModel.textareaAutoResize(textareaRef.current);
  };

  // 将键盘输入内容覆盖到作答区
  const updateAnswerFromInput = () => {
    handleTextChange({
      target: { value: inputDialogValue },
    } as React.ChangeEvent<HTMLTextAreaElement>);
  };

  // 处理输入框失焦事件，避免与确定按钮冲突
  const handleInputBlur = () => {
    // 延迟执行，让确定按钮的点击事件先执行
    setTimeout(() => {
      if (!isConfirmingRef.current) {
        // 失焦时将键盘内容覆盖到作答区
        updateAnswerFromInput();
        closeInputDialog();
      }
      isConfirmingRef.current = false; // 重置标记
    }, 100);
  };

  // 处理确定按钮点击
  const handleConfirmClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    isConfirmingRef.current = true; // 标记正在确认
    console.log("inputDialogValue", inputDialogValue);
    // 点击确定时将键盘内容覆盖到作答区
    updateAnswerFromInput();
    closeInputDialog();
  };

  // 处理对话框显示时的初始化
  useEffect(() => {
    if (showInputDialog && textareaRef.current) {
      viewModel.textareaAutoResize(textareaRef.current);
    }
  }, [showInputDialog, viewModel]);

  return (
    <>
      <div className="flex h-full flex-col">
        {/* 输入区域 */}
        {/* 解析状态 */}
        {!isReview && (
          <div className="mb-5 mt-3 h-full flex-1">
            <textarea
              className="box-border h-full w-full resize-none rounded-xl border border-[rgba(31,35,43,0.12)] bg-[#FFFFFF] p-6 text-base"
              placeholder={`点击输入答案`}
              value={answers[activeBlankIndex]?.value || ""}
              onChange={handleTextChange}
              onFocus={() =>
                openInputDialog(
                  answers[activeBlankIndex]?.value || "", // 回显当前作答区的内容
                  activeBlankIndex
                )
              }
            />
          </div>
        )}
      </div>
      {/* 底部弹窗和蒙层 - 使用 Portal 渲染到 body */}
      {isDialogVisible &&
        createPortal(
          <>
            <div
              className={cn(
                "fixed inset-0 z-40 bg-[rgba(0,0,0,0.4)] transition-opacity duration-200",
                showInputDialog ? "opacity-100" : "opacity-0"
              )}
              onClick={() => {
                // 点击蒙层关闭时将键盘内容覆盖到作答区
                updateAnswerFromInput();
                closeInputDialog();
              }}
            />
            <div
              className={cn(
                "fixed bottom-0 left-0 right-0 z-50 flex h-[13.75rem] w-full items-end transition-transform duration-200",
                showInputDialog ? "translate-y-0" : "translate-y-full"
              )}
            >
              <div className="flex w-full items-end rounded-t-2xl bg-white p-4 shadow-lg">
                <textarea
                  ref={textareaRef}
                  className="flex-1 resize-none rounded-lg border-none bg-[#F7F6F5] p-2 outline-0"
                  value={inputDialogValue}
                  onChange={handleInputChange}
                  onBlur={handleInputBlur}
                  onFocus={(e) => {
                    // 当获得焦点时，将光标移动到末尾
                    const length = e.target.value.length;
                    e.target.setSelectionRange(length, length);
                  }}
                  autoFocus
                  rows={1}
                  style={{
                    maxHeight,
                    minHeight: `${lineHeight}px`,
                    resize: "none",
                  }}
                />
                <button
                  className="ml-4 flex h-10 w-[6.125rem] cursor-pointer items-center justify-center rounded text-white"
                  onClick={handleConfirmClick}
                  style={{ backgroundColor: "var(--study-primary)" }}
                >
                  确定
                </button>
              </div>
            </div>
          </>,
          document.body
        )}
    </>
  );
};

export default KeyboardInput;
