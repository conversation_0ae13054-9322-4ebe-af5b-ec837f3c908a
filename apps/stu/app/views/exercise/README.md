# Exercise Views 层

## 📋 概述

Exercise Views 层是 MVVM 架构中的视图层，负责用户界面的渲染和用户交互的处理。该层专注于 UI 展示，不包含业务逻辑，通过调用统一的 Context 来处理用户操作。

## 🏗️ 架构设计

### 统一架构（2024年重构后）

```
View Layer (views/exercise/) ← 当前层
    ↓ 调用
Unified Context (contexts/question-context.tsx)
    ↓ 内含
ViewModel Layer (viewmodels/exercise/question-viewmodel.ts)
    ↓ 调用  
Model Layer (models/exercise/)
```

### 核心变更

✅ **架构简化**：
- 移除了 `exercise-viewmodel.ts`（已删除）
- 移除了 `exercise-context.tsx`（已删除）
- 移除了 `question-actions-viewmodel.ts`（已删除）
- 统一到 `question-context.tsx` 和 `question-viewmodel.ts`

✅ **调用简化**：
- 组件只需要调用 `useQuestionContext()` 即可获得所有功能
- Context内部集成了ViewModel的所有状态和方法
- 不再需要多个hooks的复杂组合

### 核心原则

- **纯 UI 渲染**：只负责界面展示，不包含业务逻辑
- **状态驱动**：基于统一Context提供的状态进行渲染
- **事件委托**：将用户操作委托给Context内的ViewModel处理
- **组件化**：高度组件化，提升复用性

## 📁 文件结构

```
views/exercise/
├── README.md                           # 当前文档
├── exercise-view.tsx                   # 练习统一视图组件
├── question-view.tsx                   # 题目视图组件
└── components/                         # 子组件目录
    ├── choice-question-view.tsx        # 选择题视图
    ├── fill-blank-answer-area-view.tsx # 填空题答题区
    ├── fill-blank-question-content-view.tsx # 填空题内容区
    └── camera-view.tsx                 # 摄像头视图
```

## 🔧 核心组件

### 1. ExerciseView
**职责**：练习功能的统一入口组件

**核心功能**：
- 支持两种模式：课中练习（直接模式）和巩固练习（URL模式）
- 统一的配置管理和Context包装
- 智能模式检测
- 全局错误和加载状态处理
- **内置退出逻辑**：根据学习类型差异化处理退出流程

**退出逻辑说明**：
- **AI课程**：直接触发 `onBack` 回调，无二次确认
- **其他练习类型**：显示二次确认弹窗，调用ExitViewModel保存进度，再触发 `onBack` 回调
- **架构优化**：ExitViewModel独立无耦合，View层直接调用，不经过Context
- **性能优势**：减少不必要的Context传递和重渲染
- **错误处理**：即使退出会话失败，也确保能正常触发退出回调

**架构优化记录 (2025-01)**：
- ✅ **第一次重构**：将退出逻辑从View层移到ViewModel
- ✅ **第二次优化**：发现ExitViewModel与Context无状态耦合，简化为直接调用
- 🎯 **核心原则**：无耦合即可直调，有耦合走Context

**组件接口**：
```typescript
interface ExerciseViewProps {
  // 可选：直接传入题目数据（课中练习模式）
  questionData?: NextQuestionInfo & { studySessionId?: number };
  studyType?: StudyType;
  
  // 回调函数
  onComplete?: (nextQuestionInfo?: NextQuestionInfo) => void;
  onBack?: () => void;  // 退出时触发，ExerciseView会先处理退出逻辑再调用此回调
}
```

**使用示例**：
```typescript
// 课中练习（直接模式）
<ExerciseView 
  studyType={StudyType.AI_COURSE} 
  questionData={questionInfo} 
  onComplete={handleComplete} 
/>

// 巩固练习（URL模式） 
<ExerciseView 
  studyType={StudyType.REINFORCEMENT_EXERCISE} 
  onComplete={handleComplete} 
  onBack={handleBack} 
/>
```

### 2. QuestionView
**职责**：单个题目的展示和交互

**核心功能**：
- 题目内容渲染
- 答题区域展示
- 操作按钮管理
- 向后兼容的props接口

**组件接口**：
```typescript
interface QuestionViewProps {
  question?: Question;    // 可选，支持向后兼容
  onContinue?: () => void;
  isSubmitting?: boolean;
  explanation?: string;
}
```

**状态管理（简化后）**：
```typescript
export const QuestionView: React.FC<QuestionViewProps> = ({
  question,
  onContinue,
  isSubmitting,
  explanation,
}) => {
  // 🎯 只需要一个Context调用即可获得所有功能
  const { 
    currentQuestion, 
    questionState, 
    handleSubmitAnswer 
  } = useQuestionContext();

  // 使用props传入的题目数据（向后兼容）或Context中的数据
  const displayQuestion = question || currentQuestion;
  
  // ... 其余渲染逻辑
};
```

### 3. ChoiceQuestionView
**职责**：选择题的专用视图组件

**核心功能**：
- 选项列表渲染
- 选择状态管理
- 单选/多选逻辑
- 与统一Context协作

**状态管理（简化后）**：
```typescript
export function ChoiceQuestionView() {
  // 🎯 从统一Context获取题目数据
  const { currentQuestion } = useQuestionContext();
  
  // 使用选择题专用ViewModel管理选择逻辑
  const choiceViewModel = useChoiceQuestionViewModel(currentQuestion);

  // ... 渲染逻辑
}
```

## 🎨 UI 设计规范

### 语义化 Class 名

所有DOM元素都包含语义化class名，便于调试和查找：

```tsx
// ✅ 推荐：语义化class名
<div className="question_view_container">
  <div className="question_content_section">
    <div className="question_stem_area">
      {/* 题目内容 */}
    </div>
    <div className="answer_area_section">
      {/* 答题区域 */}
    </div>
  </div>
  <div className="action_buttons_section">
    <Button className="submit_button">提交</Button>
  </div>
</div>
```

### 布局结构
```
┌─────────────────────────────────────┐
│ Question Content Section            │
│ ┌─────────────┬─────────────────────┐ │
│ │ Question    │ Answer Area         │ │
│ │ Stem Area   │ Section             │ │
│ │ (50%)       │ (50%)               │ │
│ └─────────────┴─────────────────────┘ │
├─────────────────────────────────────┤
│ Action Buttons Section              │
│ [不确定] [放弃]        [提交/继续] │
└─────────────────────────────────────┘
```

## 🔄 状态驱动渲染

### 统一状态管理

```typescript
// 之前：需要多个hooks
const config = useQuestionContext();
const sessionActions = useExerciseSessionActions();
const questionState = useQuestionState();
const questionActions = useQuestionActionsViewModel();

// 现在：只需要一个
const { 
  currentQuestion, 
  questionState, 
  handleSubmitAnswer,
  handleGiveUp,
  handleMarkUncertain,
  isSubmitting,
  canSubmit,
  showExplanation 
} = useQuestionContext();
```

### 题目状态映射
```typescript
const getButtonState = (questionState: QuestionState) => {
  switch (questionState) {
    case "answering":
      return {
        primaryButton: "提交",
        disabled: !canSubmit
      };
    case "answered":
      return {
        primaryButton: "继续",
        disabled: false
      };
    default:
      return {
        primaryButton: "提交",
        disabled: true
      };
  }
};
```

## 📊 组件复用指南

### 选择题组件复用
- `ChoiceQuestionView`：标准选择题视图
- `useChoiceQuestionViewModel`：选择题逻辑复用

### 填空题组件复用
- `FillBlankAnswerAreaView`：填空题答题区
- `FillBlankQuestionContentView`：填空题内容区
- `useFillBlankViewModel`：填空题逻辑复用

### 通用组件复用
- `QuestionView`：题目视图基础组件
- `Button`：统一的按钮组件（支持多种样式）

## 🐛 调试指南

### 语义化Class查找
所有组件都包含语义化class名，便于在开发者工具中快速定位：

```bash
# 在开发者工具中查找
.question_view_container          # 题目视图容器
.choice_question_container        # 选择题容器
.choice_option_item              # 选择题选项
.fill_blank_answer_area_container # 填空题答题区
.action_buttons_section          # 操作按钮区域
```

### Context状态检查
使用React DevTools检查QuestionContextProvider的状态：

```
QuestionContextProvider
├── currentQuestion: Question
├── questionState: QuestionState  
├── isSubmitting: boolean
├── canSubmit: boolean
└── handleSubmitAnswer: Function
```

## 🚀 性能优化

### 已实施的优化
- **减少重渲染**：使用useMemo和useCallback优化
- **代码分割**：按需加载组件
- **状态合并**：减少Context Provider层级
- **架构简化**：从复杂的多层调用简化为单一Context

### 架构优化成果
- **代码减少**：删除3个冗余文件，减少约500行代码
- **复杂度降低**：从两层ViewModel简化为单层统一ViewModel
- **调用简化**：组件只需要一个Context调用即可获得所有功能
- **维护性提升**：单一数据源，统一错误处理

## 📖 相关文档

- [ViewModels 层文档](../../viewmodels/exercise/README.md)
- [Models 层文档](../../models/exercise/README.md)
- [Components 层文档](../../components/README.md)
- [UI 设计规范](../../../../../docs/design/ui-guidelines.md)

## 组件概览

### 1. ExerciseView
**位置**: `exercise-view.tsx`
**用途**: 练习功能的统一入口组件
**特点**: 
- 支持两种模式：课中练习（直接模式）和巩固练习（URL模式）
- 统一的配置管理和Context包装
- 智能模式检测
- 全局错误和加载状态处理

**使用场景**: 
- 课中练习
- 巩固练习

### 2. ExerciseWidgetView ⭐️ 新增
**位置**: `exercise-widget-view.tsx`
**用途**: 嵌入到课程组件中的练习视图
**特点**:
- 直接使用从 `/api/v1/study_session/next_question` 获取的数据
- 不依赖完整的会话管理系统
- 轻量级实现，专门为AI课模式设计
- 自动处理练习完成后的跳转

**使用场景**:
- AI课中的练习组件
- 课程序列中的练习部分

## 使用示例

### ExerciseView (练习统一入口组件)

```typescript
import { ExerciseView } from '@/app/views/exercise/exercise-view';
import { StudyType } from '@/app/models/exercise';

function ExerciseRoute() {
  return (
    <ExerciseView
      studyType={StudyType.REINFORCEMENT_EXERCISE}
      onComplete={handleComplete}
      onBack={handleBack}
    />
  );
}
```

### ExerciseWidgetView (课程组件)

```typescript
import { ExerciseWidgetView } from '@/app/views/exercise/exercise-widget-view';

function CourseWidgetLoader({ data }: { data: CourseWidget<"exercise"> }) {
  const handleComplete = () => {
    // 练习完成后的处理逻辑
    console.log('练习完成');
  };

  return (
    <ExerciseWidgetView
      content={data}
      active={true}
      knowledgeId={789}
      widgetIndex={data.index}
      onComplete={handleComplete}
    />
  );
}
```

## 技术架构

### ExerciseView 架构
```
ExerciseView
├── ExerciseSessionProvider (Context)
│   └── useExerciseSessionViewModel (完整状态管理)
└── ExerciseViewContent
    ├── QuestionProvider
    └── QuestionView
```

### ExerciseWidgetView 架构
```
ExerciseWidgetView (轻量级)
├── 直接状态管理 (useState)
├── useSubmitStudyAnswer (仅提交答案)
└── QuestionProvider
    └── QuestionView
```

## 数据流对比

### ExerciseView 数据流
1. 调用 `/api/v1/study_session/enter` 创建会话
2. 调用 `/api/v1/study_session/next_question` 获取题目
3. 调用 `/api/v1/study_session/submit_answer` 提交答案
4. 重复步骤2-3直到练习完成

### ExerciseWidgetView 数据流
1. **接收** 已从 `/api/v1/study_session/next_question` 获取的数据
2. 解析数据并转换为Question格式
3. 调用 `/api/v1/study_session/submit_answer` 提交答案
4. 通知父组件练习完成

## API数据格式

ExerciseWidgetView接收的数据格式 (从next_question API获取):

```typescript
interface GetNextQuestionData {
  studySessionId: number | null;
  questionId: string | null;
  questionIndex: number | null;
  questionContent: string | null;
  questionType: number | null;
  questionDifficulty: number | null;
  options: Array<{
    optionId: string;
    optionContent: string;
  }> | null;
  progressInfo: {
    currentProgress: number;
    completedQuestions: number;
  };
  hasNextQuestion: boolean;
  questionExplanation?: string | null;
  questionAnswer?: {
    answerOptionList: Array<{
      optionKey?: string;
      optionVal?: string;
    }>;
  };
}
```

## 最近更新

- **2025-01-23**: 🆕 **新增ExerciseWidgetView组件**
  - ✅ 支持直接使用next_question API数据
  - ✅ 轻量级实现，专门为课程组件设计
  - ✅ 自动解析JSON数据并转换为Question格式
  - ✅ 支持练习完成回调
  - ✅ 集成到课程组件加载器中