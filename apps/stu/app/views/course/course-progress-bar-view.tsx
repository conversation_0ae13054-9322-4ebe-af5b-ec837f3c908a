import { useCourseViewContext } from "@/app/views/course/course-view-context";
import SidebarClose from "@/public/icons/sidebar-close.svg";
import { useSignal, useSignalEffect } from "@preact-signals/safe-react";
import { CourseSummary } from "@repo/core/course/course-summary";
import { AnimationDefinition, motion } from "motion/react";
import { ComponentProps, FC, useCallback, useMemo } from "react";

export const CourseProgressBarView: FC<ComponentProps<"div">> = () => {
  const { isProgressBarOpen, widgetSummarySequence, currentIndex, goto } =
    useCourseViewContext();

  const targetIndex = useSignal<number | null>(null);
  const summaries = useMemo(() => {
    const result: ((typeof widgetSummarySequence)[number] & {
      idx: number;
      arrIdx: number[];
    })[] = [];
    let last: (typeof widgetSummarySequence)[number] & {
      idx: number;
      arrIdx: number[];
    };
    widgetSummarySequence.forEach((summary, index) => {
      if (summary.type === "interactive") {
        last?.arrIdx.push(index);
      } else {
        last = { ...summary, idx: index, arrIdx: [index] };
        result.push(last);
      }
    });
    return result;
  }, [widgetSummarySequence]);

  const handleSummarySelected = useCallback(
    (idx: number) => {
      targetIndex.value = idx;
    },
    [targetIndex]
  );

  const handleSideBarAnimationComplete = useCallback(
    (definition: AnimationDefinition) => {
      if (definition === "close" && targetIndex.value !== null) {
        goto(targetIndex.value);
        targetIndex.value = null;
      }
    },
    [targetIndex, goto]
  );

  useSignalEffect(() => {
    if (isProgressBarOpen.value) {
      document.body.addEventListener("click", () => {
        isProgressBarOpen.value = false;
      });
    } else {
      document.body.removeEventListener("click", () => {
        isProgressBarOpen.value = false;
      });
    }
  });

  return (
    <motion.div
      key="course-progress-bar"
      initial={"close"}
      variants={{
        open: { x: 0 },
        close: { x: "150%" },
      }}
      animate={isProgressBarOpen.value ? "open" : "close"}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className="z-100 fixed right-0 top-0 flex h-screen w-64 transform-gpu flex-row items-center rounded-l-3xl border bg-white shadow-[-12px_12px_80px_0px_rgba(64,43,26,0.05)] backdrop-blur-[50px]"
      onAnimationComplete={handleSideBarAnimationComplete}
    >
      <SidebarClose
        className="h-[50px] w-[30px] -translate-x-1/2"
        onClick={() => (isProgressBarOpen.value = false)}
      />

      <div className="h-[calc(100%-var(--spacing)*10)] w-full overflow-y-auto pr-4">
        <div className="flex h-auto flex-col">
          {summaries.map((summary, index) => (
            <CourseSummary
              onSelect={() => handleSummarySelected(summary.idx)}
              key={summary.idx}
              title={summary.name}
              status={summary.status}
              isCurrent={summary.arrIdx.includes(currentIndex.value)}
              isLast={index === summaries.length - 1}
              index={summary.idx}
              type={summary.type}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
};
