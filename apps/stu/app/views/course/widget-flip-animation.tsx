import { FlipDirection } from "@/types/app/course";
import {
  ReadonlySignal,
  useComputed,
  useSignal,
} from "@preact-signals/safe-react";
import { motion } from "framer-motion";
import { FC } from "react";

export const WidgetFlipAnimation: FC<{
  show: ReadonlySignal<boolean>;
  direction: ReadonlySignal<FlipDirection>;
  onAnimationComplete?: () => void;
}> = ({ show, direction, onAnimationComplete }) => {
  const isReady = useSignal(false);
  const variants = {
    show: (dir: FlipDirection) => {
      return {
        y: dir === "next" ? "calc(var(--spacing)*-32)" : "0",
        transition: {
          duration: 0.8,
        },
      };
    },
    ready: (dir: FlipDirection) => {
      return {
        y: dir === "next" ? "100%" : "calc(-100% - var(--spacing)*32)",
        transition: {
          duration: 0.01,
        },
      };
    },
    hide: (dir: FlipDirection) => ({
      y: dir === "next" ? "100%" : "calc(-100% - var(--spacing)*32)",
      transition: {
        duration: 0,
      },
    }),
  };

  const animate = useComputed(() => {
    if (show.value) {
      return isReady.value ? "show" : "ready";
    }
    return "hide";
  });
  return (
    <motion.div
      custom={direction.value}
      initial="hide"
      variants={variants}
      animate={animate.value}
      className="z-100 pointer-events-none absolute left-0 top-0 h-full w-full"
      onAnimationComplete={(latest) => {
        if (latest === "hide") {
          isReady.value = false;
        }
        if (latest === "ready") {
          isReady.value = true;
        }
        if (latest === "show") {
          onAnimationComplete?.();
        }
      }}
    >
      {direction.value === "next" && (
        <div
          data-name="next"
          className="bg-linear-to-t h-32 w-full from-[#FAF8F6] via-[#FAF8F6] via-20% to-[#FAF8F6]/0"
        />
      )}
      <div className="h-full w-full bg-[#FAF8F6]" />
      {direction.value === "prev" && (
        <div
          data-name="prev"
          className="bg-linear-to-b h-32 w-full from-[#FAF8F6] via-[#FAF8F6] via-20% to-[#FAF8F6]/0"
        />
      )}
    </motion.div>
  );
};
