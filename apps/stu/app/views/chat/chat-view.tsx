"use client";
import IcClose2 from "@/public/icons/ic_close2.svg";
import { useCallback, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import MessageList from "./message-list-view";
import SendMessage from "./send-message-view";
// import { useTypewriter } from "@/app/hooks/use-typewriter";
import { Conversation } from "@/app/models/chat-model";
import { useChatViewModel } from "@/app/viewmodels/chat/chat-viewmodel";
import TwinkleIcon from "@/public/icons/twinkle.svg";
import type { FC } from "react";
import { GuideViewContextType } from "../guide/guide-view-context";
import GuessQA from "./guess-qa-view";

type ChatViewModelType = ReturnType<typeof useChatViewModel>;

interface ChatContentProps {
  messageList: Conversation[];
  setMessageList: (list: Conversation[]) => void;
  suggestedQuestions: { askText: string }[];
  initialMessage: string;
  initialInputStatus: "text" | "voice";
  onSendMessage: (message: string, inputStatus: "text" | "voice") => void;
}

// 主组件
const Chat: FC<{
  viewModel: ChatViewModelType;
  questionId?: string;
  guideContext?: GuideViewContextType;
}> = ({ viewModel, guideContext }) => {
  // console.log("questionId", questionId);
  const {
    isOpen,
    messageList,
    setMessageList,
    suggestedQuestions,
    initialMessage,
    initialInputStatus,
    handleSendMessage,
    handleUpdateMessage,
    handleClose,
    currentComponentType,
    remainingRounds,
  } = viewModel;
  if (!isOpen) return null;

  return createPortal(
    <ChatWindow
      messageList={messageList}
      setMessageList={setMessageList as (list: Conversation[]) => void}
      suggestedQuestions={suggestedQuestions}
      initialMessage={initialMessage}
      initialInputStatus={initialInputStatus}
      onClose={handleClose}
      onSendMessage={handleSendMessage}
      onUpdateMessage={handleUpdateMessage}
      currentComponentType={currentComponentType}
      guideContext={guideContext}
      remainingRounds={remainingRounds}
    />,
    document.body
  );
};

export default Chat;

// 子组件
interface ChatWindowProps {
  messageList: Conversation[];
  setMessageList: (list: Conversation[]) => void;
  suggestedQuestions: { askText: string }[];
  initialMessage: string;
  initialInputStatus: "text" | "voice";
  onClose: () => void;
  onSendMessage: (message: string, inputStatus: "text" | "voice") => void;
  onUpdateMessage: (message: string) => void;
  currentComponentType: string;
  guideContext?: GuideViewContextType;
  remainingRounds: number;
}

const ChatWindow = ({
  messageList,
  setMessageList,
  suggestedQuestions,
  initialMessage,
  initialInputStatus,
  onClose,
  onSendMessage,
  onUpdateMessage,
  currentComponentType,
  guideContext,
  remainingRounds,
}: ChatWindowProps) => {
  return (
    <div
      className="z-61 fixed inset-0 h-full bg-[rgba(0,0,0,0.5)]"
      onClick={(e) => {
        e.stopPropagation();
        onClose();
      }}
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="z-61 fixed left-1/2 top-1/2 h-[63vh] min-h-[70vh] min-w-[600px] -translate-x-1/2 -translate-y-1/2 overflow-hidden rounded-2xl bg-[#F7F6F5] pt-0.5"
      >
        <div className="bg-linear-0 h-18 absolute left-0 right-0 top-0 z-20 flex items-center justify-center from-[#F7F6F500] from-0% to-[#F7F6F5] to-100%">
          {remainingRounds <= 10 && (
            <div className="min-w-30 border-divider-2 flex h-7 items-center gap-0.5 rounded-full border bg-[#F7F6F5] px-2.5">
              <TwinkleIcon className="size-3" />
              <span className="text-text-2 text-[10px]">
                {remainingRounds <= 0
                  ? "小鹿电量已耗尽，明天再继续讨论吧"
                  : `今日剩余对话额度：${remainingRounds}轮`}
              </span>
            </div>
          )}
        </div>
        <CloseButton onClose={onClose} guideContext={guideContext} />
        <ChatContent
          messageList={messageList}
          setMessageList={setMessageList}
          suggestedQuestions={suggestedQuestions}
          initialMessage={initialMessage}
          initialInputStatus={initialInputStatus}
          onSendMessage={onSendMessage}
          onUpdateMessage={onUpdateMessage}
          currentComponentType={currentComponentType}
          disabledSend={remainingRounds <= 0}
        />
      </div>
    </div>
  );
};

const CloseButton = ({
  onClose,
}: {
  onClose: () => void;
  guideContext?: GuideViewContextType;
}) => {
  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);
  return (
    <button
      className="absolute right-4 top-4 z-20 flex h-10 w-10 cursor-pointer items-center justify-center rounded-full border border-[rgba(31,35,43,0.12)] bg-[#f9f6f4] text-sm text-[#000]"
      onClick={handleClose}
    >
      <IcClose2 />
    </button>
  );
};

interface ChatContentProps {
  messageList: Conversation[];
  setMessageList: (list: Conversation[]) => void;
  suggestedQuestions: { askText: string }[];
  initialMessage: string;
  initialInputStatus: "text" | "voice";
  onSendMessage: (message: string, inputStatus: "text" | "voice") => void;
  onUpdateMessage: (message: string) => void;
  currentComponentType: string;
  disabledSend: boolean;
}

const ChatContent = ({
  messageList,
  setMessageList,
  suggestedQuestions,
  initialMessage,
  initialInputStatus,
  onSendMessage,
  onUpdateMessage,
  disabledSend,
}: ChatContentProps) => {
  const listRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = useCallback(() => {
    const element = listRef.current;
    if (!element) return;
    element.scrollTo({
      top: 0,
    });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messageList.length, scrollToBottom]);

  return (
    <div className="flex h-full flex-col">
      <div
        ref={listRef}
        className="flex h-full flex-1 flex-col-reverse overflow-y-auto overflow-x-hidden pt-5"
      >
        {messageList.length === 1 &&
          suggestedQuestions.length > 0 &&
          !disabledSend && (
            <GuessQA
              suggestedQuestions={suggestedQuestions}
              onSendMessage={onUpdateMessage}
            />
          )}
        <MessageList
          conversations={messageList}
          setMessageList={setMessageList}
          onTextUpdate={scrollToBottom}
        />
      </div>
      <SendMessage
        initialMessage={initialMessage}
        initialInputStatus={initialInputStatus}
        onSendMessage={onSendMessage}
        disabled={disabledSend}
      />
    </div>
  );
};
