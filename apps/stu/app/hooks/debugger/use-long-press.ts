/**
 * 长按检测Hook - 完全独立的长按功能
 * 🔥 移除时直接删除此文件即可
 */
"use client";

import { useRef, useCallback } from "react";

interface UseLongPressOptions {
  onLongPress: () => void;
  onClick?: () => void;
  duration?: number; // 长按时长，默认1500ms
}

/**
 * 长按检测Hook
 * 返回事件处理器，可绑定到任何DOM元素
 */
export const useLongPress = ({ 
  onLongPress, 
  onClick, 
  duration = 1500 
}: UseLongPressOptions) => {
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  const isLongPress = useRef(false);

  const clearTimer = useCallback(() => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  }, []);

  const handleStart = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    isLongPress.current = false;
    longPressTimer.current = setTimeout(() => {
      isLongPress.current = true;
      onLongPress();
    }, duration);
  }, [onLongPress, duration]);

  const handleEnd = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    clearTimer();
    
    // 如果是长按，阻止事件冒泡避免触发子元素的点击
    if (isLongPress.current) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    // 如果不是长按且有点击回调，则执行点击并阻止冒泡
    if (!isLongPress.current && onClick) {
      e.preventDefault();
      e.stopPropagation();
      onClick();
    }
    
    // 如果没有onClick且不是长按，让事件正常冒泡到子元素
  }, [clearTimer, onClick]);

  const handleCancel = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    clearTimer();
    isLongPress.current = false;
  }, [clearTimer]);

  return {
    // 鼠标事件
    onMouseDown: handleStart,
    onMouseUp: handleEnd,
    onMouseLeave: handleCancel,
    
    // 触摸事件
    onTouchStart: handleStart,
    onTouchEnd: handleEnd,
    onTouchCancel: handleCancel,
  };
}; 