/**
 * VConsole调试工具 - 完全隔离的调试逻辑
 * 🔥 移除时直接删除此文件和providers.tsx中的调用即可
 */
"use client";

import { useEffect, useRef } from "react";

// 全局VConsole实例引用
let vConsoleInstance: any = null;

// 强制隐藏VConsole按钮
const forceHideVConsole = () => {
  try {
    // 通过CSS隐藏VConsole的入口按钮
    const vcBtn = document.getElementById('__vconsole');
    if (vcBtn) {
      vcBtn.style.display = 'none';
    }
    // 也隐藏面板
    const vcPanel = document.querySelector('.vc-panel');
    if (vcPanel) {
      (vcPanel as HTMLElement).style.display = 'none';
    }
  } catch (error) {
    // 忽略错误
  }
};

// 初始化VConsole
const initVConsole = async () => {
  if (vConsoleInstance) return;

  try {
    const VConsole = (await import("vconsole")).default;
    vConsoleInstance = new VConsole({
      theme: 'dark',
      defaultPlugins: ['system', 'network', 'element', 'storage'],
      maxLogNumber: 5000,
    });

    // 立即隐藏
    vConsoleInstance.hide();
    forceHideVConsole();

    // 延迟100ms再次隐藏，确保DOM完全加载
    setTimeout(forceHideVConsole, 100);

    console.log("[VConsole] 初始化完成，默认隐藏");
  } catch (error) {
    console.error("[VConsole] 初始化失败:", error);
  }
};

// 🔥 在应用启动时就初始化VConsole
if (typeof window !== 'undefined') {
  initVConsole();
}

/**
 * 导出独立的toggle方法供直接使用
 */
export const toggleVConsole = () => {
  if (!vConsoleInstance) {
    console.warn("[VConsole] 实例未初始化");
    return;
  }

  try {
    if (vConsoleInstance.isShow) {
      vConsoleInstance.hide();
      forceHideVConsole();
    } else {
      vConsoleInstance.show();
      const vcBtn = document.getElementById('__vconsole');
      const vcPanel = document.querySelector('.vc-panel');
      if (vcBtn) vcBtn.style.display = 'block';
      if (vcPanel) (vcPanel as HTMLElement).style.display = 'block';
    }
    console.log("[VConsole] 面板已切换");
  } catch (error) {
    console.warn("[VConsole] 控制失败:", error);
  }
};

/**
 * VConsole调试工具Hook
 * 默认隐藏但保留日志记录功能
 */
export const useVConsole = () => {
  const isInitialized = useRef(false);

  useEffect(() => {
    // 防止重复初始化
    if (isInitialized.current) return;
    isInitialized.current = true;

    // 确保VConsole已初始化
    initVConsole();

    // 清理函数
    return () => {
      if (vConsoleInstance) {
        vConsoleInstance.destroy();
        vConsoleInstance = null;
      }
    };
  }, []);
}; 