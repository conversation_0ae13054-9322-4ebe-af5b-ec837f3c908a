import { StudyType } from '@repo/core/enums';
import { QuestionState, StudentAnswer, UserAnswerData } from '@repo/core/exercise/model/types';

export type { QuestionState, UserAnswerData };

import { SubmitAnswerResponse } from '@repo/core/exercise/model';

// 答案结果常量类型

export const ANSWER_RESULT = {
  UNANSWERED: 0,        // 🔧 新增：未作答
  CORRECT: 1,           // 正确
  INCORRECT: 2,         // 错误
  PARTIAL_CORRECT: 3,   // 部分正确
} as const;

export type AnswerResultType = typeof ANSWER_RESULT[keyof typeof ANSWER_RESULT];



export type ExerciseUrlParams = {
  knowledgeId: number; // 知识点ID，必须
  phaseId: number; // 学段ID，必须
  subjectId: number; // 科目ID，必须
  textbookId: number; // 教材ID，必须
} & (
    // 巩固练习参数
    {
      studyType: StudyType.REINFORCEMENT_EXERCISE;
      studySessionId: number;
      questionSetId?: number;
    } |
    // 拓展练习参数
    {
      studyType: StudyType.EXPAND_EXERCISE;
      studySessionId: number;
      questionSetId?: number;
    } |
    // AI课参数
    {
      studyType: StudyType.AI_COURSE;
      lessonId: number;
      widgetIndex: number;
    }
  )


// ViewModel 状态接口
export interface QuestionViewModelState {
  questionState: QuestionState;
  submitCount: number;
  streakCount: number;
  lastSubmitResult: SubmitAnswerResponse | null;
  isInWrongQuestionBank: boolean;
  studentAnswer?: StudentAnswer;
}

// 初始状态
export const initialQuestionViewModelState: QuestionViewModelState = {
  questionState: "answering",
  submitCount: 0,
  streakCount: 0,
  lastSubmitResult: null,
  isInWrongQuestionBank: false,
  studentAnswer: {
    answerContents: [],
    answerDuration: 0,
    answerResult: 0,
    answerType: 0,
    evaluationType: 0,
    questionId: "",
  },
};

// 计时器控制接口
export interface TimerControlConfig {
  isActive: boolean;
  onTimeUpdate: (time: number) => void;
  shouldReset: string; // 题目ID变化时重置（已转换为字符串）
}