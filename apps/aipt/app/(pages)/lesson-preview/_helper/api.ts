import { get as courseCenterGet } from "../../../../lib/course-center-fetcher";
import { get } from "../../../../lib/fetcher";

const transformWidget = (item: any) => {
  return {
    index: item.widgetIndex,
    name: item.widgetName,
    type: item.widgetType,
    status: item.widgetType,
    hidden: item.hidden,
  };
};

// 获取当前可课的所有组件
export const getWidgets = async (
  knowledgeId: number,
  type: "draft" | "official" = "draft"
) => {
  let widgets = [];
  if (type === "draft") {
    const res: any = await get(`/api/v1/lesson_widget/preview/progress`, {
      query: { lessonId: String(knowledgeId) },
    });
    widgets = res.lessonWidgets.map((item: any) => transformWidget(item));
  } else {
    const res: any = await courseCenterGet(`/api/v1/lesson/preview/progress`, {
      query: { lessonId: String(knowledgeId) },
    });
    widgets = res.widgetList.map((item: any) => transformWidget(item));
  }
  return widgets;
};


const getExercise = async (lessonId: number, widgetIndex: number) => {
  const res: any = await get(`/api/v1/lesson_widget/question/get`, {
    query: { lessonId: String(lessonId), widgetIndex: String(widgetIndex) },
  });
  // 草稿场景下分组题会是多维数组，需要拍平
  let questionList = res.questionList.flat().filter((item: any) => item);
  questionList = questionList.map((item: any) => ({
    questionInfo: {
      ...item,
      // questionContent: item.questionContent.questionStem,
    },
    // questionInfo: transformQuestionData2(item),
    // studentAnswer: item.studentAnswer,
    // answerStats: item.answerStats,
  }));
  // res.data = JSON.parse(res.data);
  //   setWidgetData({
  //     questionList,
  //     widgetType: "exercise",
  //   });
  return questionList;
};
const getExerciseWithOfficial = async (lessonId: number, widgetIndex: number) => {
  const res: any = await courseCenterGet(`/api/v1/lesson/preview/question/list`, {
    query: { lessonId: String(lessonId), widgetIndex: String(widgetIndex) },
  });
  // 8-22 确认official场景下不会有多维数组场景
  let questionList = res.list.filter((item: any) => item);
  questionList = questionList.map((item: any) => ({
    questionInfo: {
      ...item,
    },
  }));
  return questionList;
};

export const getWidget = async (
  type: "draft" | "official",
  lessonId: number,
  widgetIndex: number
) => {
  const query = { lessonId: String(lessonId), widgetIndex: String(widgetIndex) };
  let res: any;
  if (type === "draft") {
    res = await get(`/api/v1/lesson_widget/preview/info`, { query });
    if (res.widgetType === "exercise") {
      res.data = await getExercise(lessonId, widgetIndex);
    } else {
      res.data = JSON.parse(res.data);
    }
  } else {
    res = await courseCenterGet(`/api/v1/lesson/preview/widget/info`, {
      query,
    });
    if (res.widgetType === "exercise") {
      res.data = await getExerciseWithOfficial(lessonId, widgetIndex);
    }
  }

  return res;
};
