import { CourseSummary } from "@repo/core/course/course-summary";
import { CourseWidgetSummary } from "@repo/core/types/data/course";
import { cn } from "@repo/ui/lib/utils";
import { ComponentProps, FC } from "react";
import { useCourseViewContext } from "./course-view-context";

export const CourseProgressBar: FC<ComponentProps<"div">> = ({
  className = "",
}) => {
  const { widgetSummarySequence, activeIndex, goto } = useCourseViewContext();
  if (!widgetSummarySequence) return null;

  const genDefaultTitle = (summary: CourseWidgetSummary) => {
    if (summary.type === "interactive") {
      return "互动组件";
    }
    return summary.name || "暂无标题";
  };

  return (
    <div
      className={cn(
        "h-full min-w-[200px] overflow-y-auto py-5 pr-4",
        className
      )}
    >
      <div className="flex h-auto flex-col">
        {widgetSummarySequence.map((summary, idx) => (
          <div
            key={idx}
            style={{ display: summary.hidden === 1 ? "none" : "block" }}
          >
            <CourseSummary
              onSelect={() => goto(idx)}
              key={idx}
              title={genDefaultTitle(summary)}
              status={summary.status}
              isCurrent={idx === activeIndex}
              isLast={idx === widgetSummarySequence.length - 1}
              index={idx}
              type={summary.type}
            />
          </div>
        ))}
      </div>
    </div>
  );
};
