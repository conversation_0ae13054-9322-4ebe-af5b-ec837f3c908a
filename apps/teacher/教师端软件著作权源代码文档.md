# 小鹿爱学教师端软件著作权源代码文档

## 核心源代码文件

### 1. 项目入口文件

#### 1.1 根布局文件 (layout.tsx)

```typescript
import LayoutIndex from "@/app/_layout";
import { cn } from "@/utils";
import type { Metadata, Viewport } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "小鹿爱学",
  description: "小鹿爱学-教师端",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  minimumScale: 1,
  userScalable: false,
};

export default function RootLayout(props: {
  children: React.ReactNode;
  login: boolean;
}) {
  return (
    <html lang="zh-CN" className={cn("h-full")}>
      <head>
        <link rel="preconnect" href={process.env.NEXT_PUBLIC_API_HOST} />
        <link rel="dns-prefetch" href={process.env.NEXT_PUBLIC_API_HOST} />
        <link
          rel="preload"
          href="https://static.xiaoluxue.com/assets/fonts/AlibabaPuHuiTi-3-55-Regular.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
      </head>

      <body className="h-full">
        <LayoutIndex {...props} />
      </body>
    </html>
  );
}
```

#### 1.2 默认页面组件 (default.tsx)

```typescript
export default function Default() {
  return null;
}
```

#### 1.3 全局样式文件 (globals.css)

```css
@import "tailwindcss";
@import "tw-animate-css";
@config "../tailwind.config.ts";
@custom-variant dark (&:is(.dark *));
@font-face {
  font-family: "AlibabaPuHuiTi-3-55-Regular";
  font-style: normal;
  font-weight: 400;
  src: url("https://static.xiaoluxue.com/assets/fonts/AlibabaPuHuiTi-3-55-Regular.woff2") format("truetype");
  font-display: swap;
}

@font-face {
  font-family: "AlibabaPuHuiTi-3-55-RegularL3";
  font-style: normal;
  font-weight: 400;
  src: url("https://static.xiaoluxue.com/assets/fonts/AlibabaPuHuiTi-3-55-RegularL3.woff2") format("truetype");
  font-display: swap;
}

/* shadcn-ui */
@theme inline {
  /* 背景 */
    --background: var(--color-fill-white);
    --foreground: var(--color-gray-2);
  
    /* 卡片 */
    --card: var(--color-fill-white);
    --card-foreground: var(--color-gray-2);
  
    /* 弹出框 */
    --popover: var(--color-fill-white);
    --popover-foreground: var(--color-gray-2);
  
    /* 主色 */
    --primary: var(--color-primary-2);
    --primary-foreground: var(--color-fill-white);
  
    /* 次要色 */
    --secondary: var(--color-fill-white);
    --secondary-foreground: var(--color-primary-2);
  
    /* 禁用 */
    --muted: var(--color-primary-4);
    --muted-foreground: var(--color-fill-white);
  
    /* 悬浮 */
    --accent: var(--color-primary-3);
    --accent-foreground: var(--color-fill-white);
  
    /* 警示 */
    --destructive: var(--color-danger-2);
    --destructive-foreground: oklch(0.577 0.245 27.325);
  
    /* 边框 */
    --border: var(--color-line-2);
    /* 输入组件边框 */
    --input: var(--color-line-3);
    /* 输入组件聚焦时边框 */
    --ring: var(--color-primary-2);
  
    /* 圆角 */
    --radius: 0.625rem;
  
    /* 侧边栏 */
    --sidebar: var(--color-primary-6);
    --sidebar-foreground: var(--color-gray-3);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* tch */
@theme {
  /* 断点 */
  --breakpoint-tablet: 62.5rem; /* 平板 1000px */
  --breakpoint-laptop: 80rem; /* 笔记本 1280px */
  --breakpoint-desktop: 120rem; /* 台式机 1920px */
  --breakpoint-large: 160rem; /* 大屏幕 2560px */

  /* 文字大小 */
  --text-xxs: 0.625rem;
  --text-xxs--line-height: calc(1 / 0.625);

  /* 品牌色 */
  --color-primary-1: #4A4FED;  /* 激活-字色 */
  --color-primary-2: #6574FC;  /* 主按钮-常规 */
  --color-primary-3: #9CB1FC;  /* 思维导图-1级 */
  --color-primary-4: #BBCBFC;  /* primary-4 */
  --color-primary-5: #E0EAFF;  /* primary-5 */
  --color-primary-6: #EBF1FF;  /* 白底 悬浮 hover */

  /* 辅助色 - Blue */
  --color-blue-0: #0071df;  /* blue-0 */
  --color-blue-1: #2091ff;  /* blue-1 */
  --color-blue-2: #60b0ff;  /* blue-2 */
  --color-blue-3: #B0D8FF;  /* 课程-主功能背景 */
  --color-blue-4: #D0E8FF;  /* 思维导图-3级 */
  --color-blue-5: #E9F4FF;  /* 课程-任务卡背景 个人中心-校长背景色 */

  /* 辅助色 - Purple */
  --color-purple-1: #6f60ff;  /* purple-1 */
  --color-purple-2: #A99FFF;  /* purple-2 */
  --color-purple-3: #D1CCFF;  /* 作业-主功能背景 */
  --color-purple-4: #E3E0FF;  /* 思维导图-2级 作业-任务卡背景点击 */
  --color-purple-5: #F0F0FF;  /* 作业-任务卡背景 */

  /* 辅助色 - Green */
  --color-green-0: #2dc01f;  /* 文字 */
  --color-green-1: #4de03f;  /* green-1 */
  --color-green-2: #80e976;  /* green-2 */
  --color-green-3: #B4F2AE;  /* 测试-主功能背景 */
  --color-green-4: #D9F8D7;  /* 思维导图-4级 背景点击 */
  --color-green-5: #E9FBE4;  /* 测试-任务卡背景 个人中心-教师背景色 */

  /* 辅助色 - Orange */
  --color-orange-0: #DB7505;  /* orange-0 */
  --color-orange-1: #fa9524;  /* tag-内容推荐 */
  --color-orange-2: #fcb363;  /* orange-2 */
  --color-orange-3: #FDD1A0;  /* 资源-主功能背景 */
  --color-orange-4: #FEE3C6;  /* 资源-任务卡背景点击 */
  --color-orange-5: #FFF3E6;  /* 资源-任务卡背景 */

  /* 辅助色 - Danger */
  --color-danger-1: #E0443F;  /* 警示/文字 */
  --color-danger-2: #FA5A57;  /* 小红点 */
  --color-danger-3: #fca5a2;  /* danger-3 */
  --color-danger-4: #FEDEDD;  /* 个人中心-班主任背景色-点击 */
  --color-danger-5: #FFF0EB;  /* 个人中心-班主任背景色 取消按钮背景 */

  /* 辅助色 - Carmine */
  --color-carmine-0: #CC3373;  /* 文字 */
  --color-carmine-1: #E53E8C;  /* 时间文字 */
  --color-carmine-2: #FC77BC;  /* 时间线 */
  --color-carmine-3: #FFBDE2;  /* carmine-3 */
  --color-carmine-4: #FFDEF1;  /* 时间线-灰 */
  --color-carmine-5: #FFEBF6;  /* 个人中心-班主任任背景色 */

  /* 文字颜色 */
  --color-gray-1: #101019;  /* 重点强调 颜色 */
  --color-gray-2: #444963;  /* 正文 */
  --color-gray-3: #646B8A;  /* 次要内容 */
  --color-gray-4: #838BAB;  /* 辅助\描述 */
  --color-gray-5: #ABB4D1;  /* 未激活文字 */

  /* 背景颜色 */
  --color-fill-gray-1: #F0F2F7;  /* 侧边背景色-灰 */
  --color-fill-gray-2: #F4F7FE;  /* 二级页面背景色-灰 */
  --color-fill-white: #FFFFFF;   /* 背景色-白 */
  --color-fill-light: #F5FAFF;  /* 背景色-浅蓝 */
  --color-linear-light: linear-gradient(212deg, #F5FAFF 13.1%, #FFF 80.77%);  /* 渐变背景 */

  /* 线条颜色 */
  --color-line-1: #E9ECF5;
    --color-line-2: #DFE3F0;
    --color-line-3: #CFD5E8;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: AlibabaPuHuiTi-3-55-Regular, AlibabaPuHuiTi-3-55-RegularL3;
  }
}

@layer components {
  .typography {
    p {
      font-size: 1rem;
      color: var(--color-gray-2);
      line-height: 1.75;
      margin: 1rem 0;
    }

    h1 {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--color-gray-1);
      margin-bottom: 1rem;
    }

    h2 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-gray-1);
      margin-top: 1.5rem;
      margin-bottom: 0.75rem;
    }

    h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--color-gray-2);
      margin-top: 1rem;
      margin-bottom: 0.5rem;
    }

    ul, ol {
      margin: 1rem 0;
      padding-left: 1.5rem;
      line-height: 1.75;
      color: var(--color-gray-3);
    }

    li {
      margin: 0.25rem 0;
    }

    a {
      color: var(--color-primary-1);
      text-decoration: underline;
      
      &:hover {
        color: var(--color-primary-2);
      }
    }
  }

  .gil-date-picker-popup {
    @apply text-gray-2! fixed top-[50%]! left-[50%]! translate-x-[-50%]! translate-y-[-50%]!;
  }
  .gil-date-picker-popup .ant-picker-panel-container {
      @apply rounded-3xl! shadow-[0_13px_61px_rgba(169,169,169,0.37)]!;
  }
  .gil-date-picker-popup .ant-picker-header {
      @apply p-5!;
  }
  .gil-date-picker-popup .ant-picker-header * {
      @apply leading-normal! text-neutral-800!;
      @apply text-base!;
  }
  .gil-date-picker-popup .ant-picker-body {
      @apply p-4! pr-3!;
  }
  .gil-date-picker-popup .ant-picker-cell {
      @apply text-slate-400!;
  }
  .gil-date-picker-popup .ant-picker-cell-in-view {
      @apply text-gray-2!;
  }
  .gil-date-picker-popup .ant-picker-cell-in-view.ant-picker-cell-disabled {
      @apply text-slate-400!;
  }
  .gil-date-picker-popup .ant-picker-cell-inner {
      @apply rounded-full! size-7! leading-7! min-w-7! font-medium! text-sm!;
  }

  .gil-date-picker-popup .ant-picker-content th {
      @apply text-gray-4! text-sm! font-medium!;
  }
  .gil-date-picker-popup .ant-picker-time-panel-column {
      @apply w-16.5!;
  }
  .gil-date-picker-popup .ant-picker-time-panel-cell-inner {
      @apply px-5! py-1.5! text-gray-2! text-sm! font-medium! w-14.5! h-auto! rounded-lg!;
  }
  .gil-date-picker-popup .ant-picker-time-panel-cell-disabled .ant-picker-time-panel-cell-inner {
      @apply text-gray-5!;
  }
  .gil-date-picker-popup .ant-picker-ranges {
      @apply px-8! py-5!;
  }
  .gil-date-picker-popup .ant-picker-ranges >li {
      @apply leading-none!;
  }
  .gil-date-picker-popup .ant-picker-ranges .ant-picker-ok {
      @apply p-0!;
  }
  .gil-date-picker-popup .ant-picker-ranges .ant-picker-ok .ant-btn {
      @apply text-white! text-base! font-medium! leading-normal! py-2! px-6! h-auto! rounded-full! bg-indigo-500!;
  }
  .gil-date-picker-popup .ant-picker-ranges .ant-picker-ok .ant-btn:disabled,
  .gil-date-picker-popup .ant-picker-ranges .ant-picker-ok .ant-btn.ant-btn-disabled {
      @apply bg-indigo-200! border-indigo-200!;
  }
  .gil-date-picker-popup .ant-picker-footer-extra {
      @apply p-0!;
  }
  .gil-date-picker-popup .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner {
      @apply text-primary-1!;
  }
  .gil-date-picker-popup .ant-picker-cell-in-view.ant-picker-cell-today.ant-picker-cell-selected:not(.ant-picker-cell-disabled) .ant-picker-cell-inner {
      @apply text-white!;
  }
  .gil-date-picker-popup .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
      @apply border-none! top-auto! -bottom-2! left-[50%]! translate-x-[-50%]! size-1! bg-primary-2!;
  }
  .gil-date-picker-popup .ant-picker-header-view button {
    @apply font-medium!;
  }

  .gil-date-picker-popup .ant-picker-dropdown .ant-picker-time-panel-column {
    scrollbar-width: thin !important;
    scrollbar-color: rgba(0, 0, 0, 0.1) transparent !important;
  }
}
```

### 2. 错误处理文件

#### 2.1 错误页面 (error.tsx)

```typescript
"use client";
import Image from "next/image";
import forbidden from "@/public/images/403.png";

export default function Error({
  error,
}: {
  error: Error & { digest?: string };
}) {
  console.error(error);

  return (
    <div className="flex size-full flex-col items-center justify-center">
      <Image src={forbidden} alt="403" className="size-30" />

      <div className="text-gray-4 mt-2 text-sm/normal">
        系统出错，请联系管理员
        <div className="hidden">{error.message}</div>
      </div>
    </div>
  );
}
```

#### 2.2 全局错误处理 (global-error.tsx)

```typescript
"use client";

import forbidden from "@/public/images/403.png";
import Image from "next/image";
import { useEffect, useState } from "react";

/**
 * 简化的全局错误处理器 - 捕获应用级别的错误
 * 注意：global-error.tsx 必须包含 html 和 body 标签
 */
export default function GlobalError({
  error,
  reset,
}: {
  /** 错误对象 */
  error: Error & { digest?: string };
  /** 重置函数 */
  reset: () => void;
}) {
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // 在控制台输出详细错误信息
    console.group("🚨 Global Error");
    console.error("Error:", error);
    console.error("Message:", error.message);
    console.error("Stack:", error.stack);
    if (error.digest) {
      console.error("Digest:", error.digest);
    }
    console.groupEnd();
  }, [error]);

  const isDevelopment = process.env.NODE_ENV === "development";

  const handleRefresh = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = "/course";
  };

  const handleCopyError = async () => {
    const errorText = `
全局错误详情:
消息: ${error.message}
时间: ${new Date().toISOString()}
页面: ${typeof window !== "undefined" ? window.location.href : "unknown"}
摘要: ${error.digest || "N/A"}
${error.stack ? `\n堆栈信息:\n${error.stack}` : ""}
    `.trim();

    try {
      await navigator.clipboard.writeText(errorText);
      alert("错误详情已复制到剪贴板");
    } catch (err) {
      console.error("复制失败:", err);
    }
  };

  return (
    <html lang="zh-CN">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>系统错误 - 银河教师端</title>
        <style>{`
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f9fafb;
            color: #374151;
            line-height: 1.6;
            height: 100vh;
          }
          .size-30 {
            width: 7.5rem;
            height: 7.5rem;
          }
          .text-gray-4 {
            color: #9ca3af;
          }
          .text-sm\\/normal {
            font-size: 0.875rem;
            line-height: 1.25rem;
          }
          .mt-2 {
            margin-top: 0.5rem;
          }
          .hidden {
            display: none;
          }
          .flex {
            display: flex;
          }
          .h-screen {
            height: 100vh;
          }
          .flex-col {
            flex-direction: column;
          }
          .items-center {
            align-items: center;
          }
          .justify-center {
            justify-content: center;
          }
          .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            margin: 0.25rem;
            transition: background-color 0.2s;
            min-width: 120px;
          }
          .button:hover {
            background: #2563eb;
          }
          .button-secondary {
            background: #6b7280;
          }
          .button-secondary:hover {
            background: #4b5563;
          }
          .button-danger {
            background: #dc2626;
          }
          .button-danger:hover {
            background: #b91c1c;
          }
          .actions {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1.5rem;
            max-width: 500px;
          }
          .details {
            margin-top: 1rem;
            padding: 1rem;
            background: #f3f4f6;
            border-radius: 8px;
            text-align: left;
            font-size: 0.75rem;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
            max-width: 500px;
            width: 100%;
          }
          .footer {
            margin-top: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid #e5e7eb;
            font-size: 0.75rem;
            color: #9ca3af;
            text-align: center;
            max-width: 500px;
          }
          @media (max-width: 640px) {
            .actions {
              flex-direction: column;
              width: 100%;
              max-width: 300px;
            }
            .button {
              width: 100%;
              margin: 0.25rem 0;
            }
            .details {
              max-width: 300px;
            }
            .footer {
              max-width: 300px;
            }
          }
        `}</style>
      </head>
      <body>
        <div className="flex h-screen flex-col items-center justify-center">
          {/* 错误图标 */}
          <div style={{ fontSize: "4rem", marginBottom: "1rem" }}>💥</div>

          {/* 错误图片 */}
          <Image src={forbidden} alt="系统错误" className="size-30" />

          {/* 错误消息 */}
          <div
            className="text-gray-4 mt-2 text-sm/normal"
            style={{ textAlign: "center", marginBottom: "1rem" }}
          >
            系统出错，请尝试刷新页面或联系管理员
            <div className="hidden">{error.message}</div>
          </div>

          {/* 操作按钮 */}
          <div className="actions">
            <button onClick={reset} className="button">
              重试
            </button>

            <button onClick={handleGoHome} className="button button-secondary">
              返回首页
            </button>

            {isDevelopment && (
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="button button-danger"
              >
                {showDetails ? "隐藏" : "显示"}详情
              </button>
            )}

            <button
              onClick={handleCopyError}
              className="button button-secondary"
            >
              复制错误信息
            </button>
          </div>

          {/* 开发环境显示详细错误 */}
          {isDevelopment && showDetails && (
            <div className="details">
              <div>
                <strong>错误名称:</strong> {error.name}
              </div>
              <div>
                <strong>错误消息:</strong> {error.message}
              </div>
              <div>
                <strong>时间:</strong> {new Date().toISOString()}
              </div>
              {error.digest && (
                <div>
                  <strong>摘要:</strong> {error.digest}
                </div>
              )}
              {error.stack && (
                <div>
                  <strong>堆栈信息:</strong>
                  <pre style={{ whiteSpace: "pre-wrap", marginTop: "0.5rem" }}>
                    {error.stack}
                  </pre>
                </div>
              )}
            </div>
          )}

          {/* 页脚信息 */}
          <div className="footer">
            <div>
              错误时间: {new Date().toLocaleString() + "   "}
              {error.digest && ` | 摘要: ${error.digest}`}
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
```

#### 2.3 404页面 (not-found.tsx)

```typescript
import Image from "next/image";
import notFound from "@/public/images/404.png";

export default function NotFound() {
  return (
    <div className="flex size-full flex-col items-center justify-center">
      <Image src={notFound} alt="404" className="size-30" />
      <div className="text-gray-4 mt-2 text-sm/normal">没有找到页面哦~</div>
    </div>
  );
}
```

### 3. 布局和通用组件

#### 3.1 应用布局主文件 (_layout/index.tsx)

```typescript
import React from "react";
import AppLayout from "./AppLayout";
import AppSkeleton from "./AppSkeleton";
import AuthProvider from "./AuthProvider";
import DeviceProvider from "./DeviceProvider";
import "./init-app";

export default function LayoutIndex(props: {
  children: React.ReactNode;
  login: React.ReactNode;
}) {
  return (
    <>
      {/* 全局骨架屏 */}
      <AppSkeleton />

      {/* 设备检测层 */}
      <DeviceProvider>
        {/* 登录状态与权限校验层 */}
        <AuthProvider>
          {/* 布局层 */}
          <AppLayout {...props} />
        </AuthProvider>
      </DeviceProvider>
    </>
  );
}
```

#### 3.2 应用提供者 (_layout/AppProvider.tsx)

```typescript
"use client";

import {
  LOCALSTORAGE_SIDEBAR_STATE_KEY,
  LOCALSTORAGE_USER_INFO_KEY,
  SIDEBAR_WIDTH,
  SIDEBAR_WIDTH_ICON,
} from "@/configs";
import { AppContext, useDevice, type AppContextProps } from "@/hooks";
import { useUserRoles } from "@/hooks/useUserRoles";
import { getUserInfo } from "@/services";
import { UserInfo } from "@/types";
import { TooltipProvider } from "@/ui/tooltip";
import { cn } from "@/utils/utils";
import { useSignal } from "@preact-signals/safe-react";
import { useLocalStorageState, useRequest } from "ahooks";
import * as React from "react";
import store from "store2";
import AppSidebar from "./app-sidebar";

export default function AppProvider({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const { screenSize } = useDevice();
  // 侧边栏开启
  const open = useSignal(store(LOCALSTORAGE_SIDEBAR_STATE_KEY) ?? true);
  /**
   * 平板设备 statusBarHeight
   */
  const statusBarHeight = useSignal(screenSize?.statusBarHeight ?? 0);
  /**
   * 用户信息
   */
  const [userInfo, setUserInfo] = useLocalStorageState<UserInfo | null>(
    LOCALSTORAGE_USER_INFO_KEY,
    {
      defaultValue: null,
    }
  );

  const [primarySubject, setPrimarySubject] = React.useState<{
    subjectId: number;
    subjectName: string;
  } | null>(null);

  const {
    setJobs,
    hasJobTypes,
    hasJobType,
    computePrimarySubject,
    getRoleSummary,
  } = useUserRoles();

  useRequest(getUserInfo, {
    onSuccess: (res) => {
      setUserInfo(res);
      setJobs(res?.teacherJobInfos || []);
      setPrimarySubject(computePrimarySubject(res?.teacherJobInfos || []));
      store(LOCALSTORAGE_USER_INFO_KEY, res);
    

    },
  });

  const contextValue = React.useMemo<AppContextProps>(
    () => ({
      statusBarHeight: statusBarHeight.value,
      primarySubject,
      hasJobTypes,
      hasJobType,
      getRoleSummary,
      open,
      setOpen: (value: boolean) => {
        open.value = value;
        store(LOCALSTORAGE_SIDEBAR_STATE_KEY, value);
      },
      userInfo,
    }),
    [
      statusBarHeight.value,
      primarySubject,
      hasJobTypes,
      hasJobType,
      getRoleSummary,
      open,
      userInfo,
    ]
  );

  return (
    <AppContext.Provider value={contextValue}>
      <TooltipProvider delayDuration={0}>
        <div
          data-slot="sidebar-wrapper"
          style={
            {
              "--sidebar-width": SIDEBAR_WIDTH,
              "--sidebar-width-icon": SIDEBAR_WIDTH_ICON,
            } as React.CSSProperties
          }
          className={cn(
            "group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar h-full"
          )}
        >
          <div
            className="bg-fill-light flex h-full overflow-hidden"
            style={{
              paddingTop: `${statusBarHeight.value}px`,
            }}
          >
            <AppSidebar
              style={{
                paddingTop: statusBarHeight.value,
                minHeight: screenSize?.height,
              }}
            />

            <main className="flex-1 overflow-hidden">{children}</main>
          </div>
        </div>
      </TooltipProvider>
    </AppContext.Provider>
  );
}
```

#### 3.3 应用主布局 (_layout/AppLayout.tsx)

```typescript
"use client";
import { TEACHER_APP_LAYOUT_ID } from "@/configs";
import { useAuth, useDevice } from "@/hooks";
import React from "react";
import { match, P } from "ts-pattern";
import AppProvider from "./AppProvider";
import FeedbackProvider from "./feedback/FeedbackProvider";
import "./init-app";

export default function AppLayout({
  children,
  login,
}: Readonly<{
  children: React.ReactNode;
  login: React.ReactNode;
}>) {
  const { token } = useAuth();
  const { screenSize } = useDevice();

  return (
    <div
      id={TEACHER_APP_LAYOUT_ID}
      className="h-screen select-none overflow-auto"
      style={{ minHeight: screenSize?.height }}
    >
      {match(token)
        // 主界面
        .with(P.string, () => (
          <FeedbackProvider>
            <AppProvider>{children}</AppProvider>
          </FeedbackProvider>
        ))
        // 登录页
        .otherwise(() => login)}
    </div>
  );
}
```

#### 3.4 认证提供者 (_layout/AuthProvider.tsx)

```typescript
"use client";
import { LOCALSTORAGE_SCHOOL_ID_KEY, LOCALSTORAGE_TOKEN_KEY } from "@/configs";
import { AuthContext } from "@/hooks";
import { useAppAuthEmitter } from "@/libs";
import { useLocalStorageState, useMemoizedFn, useMount } from "ahooks";
import to from "await-to-js";
import { useRouter } from "next/navigation";
import React from "react";

function AuthProvider({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();

  const [token, setToken] = useLocalStorageState<string | null>(
    LOCALSTORAGE_TOKEN_KEY,
    {
      defaultValue: null,
    }
  );

  const [schoolId, setSchoolId] = useLocalStorageState<number | null>(
    LOCALSTORAGE_SCHOOL_ID_KEY,
    {
      defaultValue: null,
    }
  );

  const login = useMemoizedFn(
    ({ token, schoolId }: { token: string; schoolId: number }) => {
      setToken(token);
      setSchoolId(schoolId);
      router.replace("/course");
    }
  );

  const logout = useMemoizedFn(() => {
    setToken(null);
    setSchoolId(null);
    router.replace("/login");
  });

  /**
   * 统一处理认证失败 / 权限不足的重定向 （目前用于监听、处理在 axios 响应拦截器中发送的事件）
   */
  useAppAuthEmitter(logout);

  /**
   * 是否正在检查登录状态
   */
  const [isChecking, setIsChecking] = React.useState(true);

  /**
   * 检查登录状态，进行重定向
   */
  useMount(() => {
    const checkAuthAndRedirect = async () => {
      const pathname = window.location.pathname;
      const isLoginPage = pathname.startsWith("/login");

      if (token && isLoginPage) {
        router.replace("/course");
        return;
      }

      if (!token && !isLoginPage) {
        router.replace("/login");
        return;
      }
    };

    to(checkAuthAndRedirect());
    setIsChecking(false);
  });

  return (
    <AuthContext value={{ login, logout, token, schoolId }}>
      {isChecking ? null : children}
    </AuthContext>
  );
}

export default AuthProvider;
```

#### 3.5 应用初始化 (_layout/init-app.ts)

```typescript
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import duration from "dayjs/plugin/duration";
import updateLocale from "dayjs/plugin/updateLocale";
import weekOfYear from "dayjs/plugin/weekOfYear";

// 初始化dayjs配置
dayjs.extend(duration);
dayjs.extend(updateLocale);
dayjs.extend(weekOfYear);
dayjs.locale("zh-cn");
// 设置一周的第一天为周一
dayjs.updateLocale("zh-cn", {
  weekStart: 1,
});

if (typeof window !== "undefined") {
  // 动态加载神策数据分析脚本
  import("@/public/js/sensors/web/sensorsdata.js")
    .then(() => {
      const sensors = window["sensorsDataAnalytic201505"];

      if (sensors) {
        // 神策数据埋点
        sensors.init({
          server_url:
            "https://yhzx-pro.datasink.sensorsdata.cn/sa?project=default&token=39e6f5c78934680b",
          use_client_time: true,
          send_type: "beacon",
          show_log: process.env.NODE_ENV === "development",
        });

        // 注册公共属性
        sensors.registerPage({
          app_name: "教师端",
          // TODO 修改平台
          platform: "web",
        });

        sensors.quick("autoTrack");
      }
    })
    .catch((error) => {
      console.error("Failed to load sensors analytics:", error);
    });
}
```

### 4. 主要功能页面

#### 4.1 作业管理页面 (homework/page.tsx)

```typescript
"use client";
import { useApp } from "@/hooks";
import { useUmeng } from "@/hooks/useUmeng";
import { UmengCategory } from "@/utils";
import {
  getStorageSync,
  removeStorageAsync,
  setStorageAsync,
} from "@/utils/storage-utils";
import { useMount, useUnmount } from "ahooks";
import { useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { Navigation } from "./_components/navigation";
import { TaskList } from "./_components/task-list";
import { HomeworkListProvider, useHomeworkListContext } from "./context";
function HomeworkContent() {
  useUmeng(UmengCategory.HOMEWORK, "homework_list");
  const { initializeData, initializeQuery, setQuery } =
    useHomeworkListContext();
  const searchParams = useSearchParams();
  const { setOpen } = useApp();
  const source = searchParams.get("source");

  // 统一处理侧边栏状态和来源信息
  useEffect(() => {
    if (source === "assign") {
      // URL 中有 source=assign 参数
      setOpen(false);
      setStorageAsync("homework_source", "assign").catch(console.error);
    } else {
      // URL 中没有 source 参数，检查 localStorage
      const storedSource = getStorageSync<string>("homework_source");
      if (storedSource === "assign") {
        // 从详情页返回的情况，保持侧边栏收起状态
        setOpen(false);
      } else {
        // 正常访问作业列表，展开侧边栏
        setOpen(true);
        if (storedSource !== "courseId") {
          const query = initializeQuery(true);
          setQuery(query, false);
        }
      }
    }
  }, [source, setOpen, initializeQuery, setQuery]);

  useMount(() => {
    // console.log("mount");
    initializeData();
  });

  useUnmount(() => {
    // console.log("unmount");
    removeStorageAsync("homework_source").catch(console.error);
  });

  return (
    <div className="flex h-full flex-col">
      <Navigation source={source} />
      <div className="flex-1 overflow-auto pb-4">
        <TaskList />
      </div>
    </div>
  );
}

export default function ReportPage() {
  return (
    <HomeworkListProvider>
      <HomeworkContent />
    </HomeworkListProvider>
  );
}
```

#### 4.2 任务布置页面 (assign/page.tsx)

```typescript
"use client";
import { AssignCategoryCard } from "@/components/assign/assign-category-card";
import { AssignEmpty } from "@/components/assign/assign-empty";
import { AssignSubjectSelect } from "@/components/assign/assign-subject-select";
import { PageHeader } from "@/components/PageHeader";
import { useApp } from "@/hooks";
import { useTchNavigation } from "@/hooks/useTchNavigation";
import { useUmeng } from "@/hooks/useUmeng";
import ArrowRight from "@/public/icons/ic_fold.svg";
import { getTaskLatestAssign } from "@/services/assign";
import { Homework } from "@/types/homeWork";
import { ScrollArea } from "@/ui/scroll-area";
import { Skeleton } from "@/ui/skeleton";
import { Button } from "@/ui/tch-button";
import {
  getAssignCategories,
  sortLatestAssignTasks,
  umeng,
  UmengAssignAction,
  UmengAssignPageName,
  UmengCategory,
} from "@/utils";
import { cn } from "@/utils/utils";
import {
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { useMount, useRequest } from "ahooks";
import { useContext } from "react";
import AssignSkeleton from "./components/AssignSkeleton";
import AssignTaskCard from "./components/AssignTaskCard";
import { AssignContext } from "./store";

export default function AssignPage() {
  useUmeng(UmengCategory.ASSIGN, UmengAssignPageName.TASK_LIST);
  const { getRoleSummary, setOpen, userInfo } = useApp();
  const { gotoTaskCreatePage, gotoHomeworkPage } = useTchNavigation();
  const { currentSubject, setCurrentSubject, subjectTaskTypes } =
    useContext(AssignContext);
  const playGuide = useSignal<boolean>(false);
  const assignCategories = useComputed(() => {
    return getAssignCategories(currentSubject.value.taskTypes);
  });

  const currentSubjectKey = useComputed(() => {
    return currentSubject.value?.subjectKey || 0;
  });

  // 太抽象了
  const onCategoryClick = (id: string, umengTag: string) => {
    if (currentSubjectKey.value) {
      umeng.trackEvent(
        UmengCategory.ASSIGN,
        UmengAssignAction.TASK_LIST_ADD_TASK_CLICK,
        {
          type: umengTag,
          subject: currentSubject.value.subjectName,
          job: getRoleSummary(),
        }
      );

      gotoTaskCreatePage(currentSubjectKey.value, id);
    }
  };

  const viewAllTasks = () => {
    umeng.trackEvent(
      UmengCategory.ASSIGN,
      UmengAssignAction.TASK_LIST_MORE_CLICK,
      {}
    );
    gotoHomeworkPage({
      source: "assign",
      subjectId: currentSubjectKey.value,
    });
  };

  const { data: latestAssignTasks, loading: isLatestAssignTasksLoading } =
    useRequest(
      async () => {
        if (!currentSubjectKey.value) {
          return [];
        }
        const res = await getTaskLatestAssign(Number(currentSubjectKey.value));
        const tasks: Homework[] = res?.tasks ?? [];
        const sortedTasks = sortLatestAssignTasks(tasks);

        return sortedTasks;
      },
      {
        refreshDeps: [currentSubjectKey.value],
      }
    );

  useSignalEffect(() => {
    if (playGuide.value) {
      umeng.trackEvent(
        UmengCategory.ASSIGN,
        UmengAssignAction.TASK_LIST_NEW_GUIDE_CLICK,
        {}
      );
    }
  });

  useMount(() => {
    setOpen(true);
  });

  if (!userInfo) {
    return <AssignSkeleton />;
  }

  return (
    <div className="flex h-full flex-col overflow-hidden bg-[#F5FAFF] px-6">
      <PageHeader className="pl-0">
        {/* 当前学科的选择器 */}
        <AssignSubjectSelect
          onChange={setCurrentSubject}
          value={currentSubject}
          taskTypes={subjectTaskTypes}
        />
      </PageHeader>

      <div
        className={cn("flex-0 mb-8 grid gap-4")}
        style={{
          gridTemplateColumns: `repeat(${assignCategories.value.length}, minmax(0, 1fr))`,
        }}
      >
        {assignCategories.value.map((category, index) => (
          <AssignCategoryCard
            key={index}
            {...category}
            className={cn("col-span-1")}
            onClick={onCategoryClick}
          />
        ))}
      </div>

      <div className="flex-0 mb-5 flex items-center justify-between">
        <h2 className="text-gray-1 text-xl font-medium leading-normal tracking-wider">
          最近布置
        </h2>
        {!isLatestAssignTasksLoading && latestAssignTasks?.length !== 0 ? (
          <Button
            radius="full"
            size="lg"
            className="text-gray-4 h-7 w-16 bg-transparent text-xs font-medium"
            onClick={viewAllTasks}
          >
            <span style={{ lineHeight: "normal" }}>全部</span>
            <ArrowRight className="ml-1 h-3 w-2" />
          </Button>
        ) : null}
      </div>
      {isLatestAssignTasksLoading ? (
        <ScrollArea orientation="vertical" className="flex-1 overflow-hidden">
          <div className="grid grid-cols-4 gap-4">
            {/* 第一行 - 大卡片 */}
            <Skeleton className="col-span-1 h-6" />
            <Skeleton className="col-span-1 h-6" />
            <Skeleton className="col-span-1 h-6" />
            <Skeleton className="col-span-1 h-6" />

            {/* 第二行 - 小标签 */}
            <Skeleton className="w-19.5 col-span-1 h-4" />
            <Skeleton className="w-19.5 col-span-1 h-4" />
            <Skeleton className="w-19.5 col-span-1 h-4" />
            <Skeleton className="w-19.5 col-span-1 h-4" />

            {/* 内容卡片 */}
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={`row3-${index}`} className="h-25.75 col-span-1" />
            ))}
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={`row4-${index}`} className="h-25.75 col-span-1" />
            ))}
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={`row4-${index}`} className="h-25.75 col-span-1" />
            ))}
          </div>
        </ScrollArea>
      ) : latestAssignTasks?.length !== 0 ? (
        <ScrollArea orientation="vertical" className="flex-1 overflow-hidden">
          <div
            className="grid grid-cols-2 gap-4"
            style={{
              gridTemplateColumns: `repeat(${4}, minmax(0, 1fr))`,
            }}
          >
            {latestAssignTasks?.map((task) => (
              <AssignTaskCard key={task.taskId} taskData={task} />
            ))}
          </div>
        </ScrollArea>
      ) : (
        <AssignEmpty
          type="homepage"
          className="flex-1 border-none bg-gradient-to-b from-slate-50 to-white"
          content={
            <div className="space-y-1 text-center leading-normal">
              <div className="text-gray-2 text-center text-base">
                当前暂无布置内容
              </div>
              <div className="text-gray-4 text-center text-xs">
                快去布置一项任务吧
              </div>
            </div>
          }
        />
      )}
    </div>
  );
}
```

#### 4.3 课程管理页面 (course/page.tsx)

```typescript
"use client";

import { useApp, useUmeng } from "@/hooks";
import { getCourseTable } from "@/services/course";
import Spin from "@/ui/spin";
import { UmengCategory, UmengCoursePageName } from "@/utils";
import {
  useCreation,
  useMount,
  useRequest,
  useSessionStorageState,
} from "ahooks";
import dayjs from "dayjs";
import { CourseNav } from "./_components/course-nav";
import { CourseTable } from "./_components/course-table";
import { DatePicker } from "./_components/date-picker";

export default function CoursePage() {
  useUmeng(UmengCategory.COURSE, UmengCoursePageName.CLASSROOM);
  const { userInfo, setOpen } = useApp();

  useMount(() => {
    setOpen(true);
  });

  // 当前选中的日期
  const [currentDate, setCurrentDate] = useSessionStorageState(
    "course-current-date",
    {
      defaultValue: dayjs(),
      serializer: (value) => value.format("YYYY-MM-DD"),
      deserializer: (value) => dayjs(value),
    }
  );

  // 获取本周的日期数组
  const weekDays = useCreation(() => {
    const monday = currentDate!.startOf("week");
    return Array.from({ length: 7 }, (_, i) => monday.add(i, "day"));
  }, [currentDate]);

  // 生成时间轴时间点（从7点开始, 到24点结束, 每小时一个时间点）
  const timePoints = useCreation(() => {
    return Array.from({ length: 18 }, (_, i) => i + 7);
  }, [currentDate]);

  // 获取当前是星期几（0-6，0是周日）
  const currentDayIndex = useCreation(() => {
    const today = dayjs();
    // 找到当前日期在 weekDays 中的索引
    return weekDays.findIndex((date) => date.isSame(today, "day"));
  }, [weekDays]);

  // 加载课程表数据
  const getCourseTableRequest = useRequest(
    async () => {
      const params = {
        teacher_id: userInfo?.userID ?? 0,
        school_id: userInfo?.currentSchoolID ?? 0,
        school_year_id: 1,
        start_date: weekDays[0].format("YYYY-MM-DD"),
        end_date: weekDays[6].format("YYYY-MM-DD"),
      };

      return getCourseTable(params);
    },
    {
      debounceWait: 500,
      loadingDelay: 500,
      ready: !!userInfo,
    }
  );

  // 切换到今天
  const handleToday = () => {
    setCurrentDate(dayjs());
    getCourseTableRequest.run();
  };

  // 切换到上一周
  const handlePrevWeek = () => {
    setCurrentDate(currentDate!.subtract(1, "week"));
    getCourseTableRequest.run();
  };

  // 切换到下一周
  const handleNextWeek = () => {
    setCurrentDate(currentDate!.add(1, "week"));
    getCourseTableRequest.run();
  };

  return (
    <Spin loading={getCourseTableRequest.loading} className="h-full">
      <div className="flex h-full flex-col overflow-hidden px-6">
        <CourseNav refreshCourseTable={getCourseTableRequest.run} />

        {/* 主要内容区域 */}
        <div className="flex flex-1 flex-col gap-5 overflow-hidden pb-2.5 pt-4">
          <DatePicker
            currentDate={currentDate!}
            weekDays={weekDays}
            onToday={handleToday}
            onPrevWeek={handlePrevWeek}
            onNextWeek={handleNextWeek}
          />

          <CourseTable
            weekDays={weekDays}
            scheduleData={
              getCourseTableRequest.error
                ? {}
                : (getCourseTableRequest.data?.schedule ?? {})
            }
            timePoints={timePoints}
            currentDayIndex={currentDayIndex}
            onPrevWeek={handlePrevWeek}
            onNextWeek={handleNextWeek}
          />
        </div>
      </div>
    </Spin>
  );
}
```

### 8. 服务层实现

#### 8.1 课程服务 (services/course.ts)

```typescript
import type {
  CourseRankingData,
  GetBehaviorCategorysResponse,
  GetCourseInfoResponse,
  GetCourseTableParams,
  GetCourseTableResponse,
  GetStudentLearningStatusResponse,
  IsTeachingData,
  RemindStudentData,
  StudentCourseDetailData,
} from "@/types";
import { r } from "../libs/axios";

/**
 * 获取教师课程表
 */
export const getCourseTable = (params: GetCourseTableParams) => {
  return r.get<GetCourseTableResponse>("/schedule/store/auth", { params });
};

/**
 * 检测是否上课
 */
export const isTeaching = (params: {
  teacher_id: string;
  school_id: string;
}) => {
  return r.get<IsTeachingData>("/schedule/is_teaching", { params });
};

/**
 * 获取课堂信息
 */
export const getCourseInfo = (params: { classroomId: string }) => {
  return r.get<GetCourseInfoResponse>("/schedule/classroom/status", { params });
};

/**
 * 获取课堂学习能量排行
 */
export const getCourseRanking = (params: { classroomId: string }) => {
  return r.get<CourseRankingData>("behavior/classroom/learning-scores", {
    params,
  });
};

/**
 * 课堂表扬关注提醒行为分类列表
 */
export const getBehaviorCategorys = (params: { classroomId: string }) => {
  return r.get<GetBehaviorCategorysResponse>(
    "/behavior/class/behavior-category",
    { params }
  );
};

/**
 * 提醒学生
 */
export const remindStudent = (data: {
  classroomID: string;
  studentIDs: number[];
  teacherID: number;
  teacherName: string;
  attentionMessage?: string;
}) => {
  return r.post<RemindStudentData>("/behavior/attention", data);
};

/**
 * 表扬学生
 */
export const praiseStudent = (data: {
  classroomID: string;
  studentIDs: number[];
  teacherID: number;
  teacherName: string;
  praiseMessage?: string;
  source?: "list" | 'drawer'
}) => {
  return r.post<RemindStudentData>("/behavior/praise", {
    ...data,
    source: data.source || "list",
  });
};

/**
 * 获取学生个人课堂详情
 */
export const getStudentCourseDetail = (params: {
  classroomId: string;
  studentId: string;
}) => {
  return r.get<StudentCourseDetailData>("/behavior/student/classroom-detail", {
    params,
  });
};

export const getStudentLearningStatus = (params: { classroomId: string }) => {
  return r.get<GetStudentLearningStatusResponse>(
    "/behavior/class/latest-behaviors",
    { params }
  );
};

/**
 * 发送评价
 */
export const sendStudentEvaluate = (data: {
  classId: number;
  classroomId: string;
  content: string;
  evaluateType: string;
  schoolId: number;
  studentId: number;
  teacherId: number;
}) => {
  return r.post<{
    success: boolean;
    message: string;
    evaluateId: number;
  }>("/behavior/student/evaluate", data);
};
```

#### 8.2 登录服务 (services/login.ts)

```typescript
import { PLATFORM } from "@/enums";
import { r } from "@/libs";
import { LoginResponse, School } from "@/types";

const NEXT_PUBLIC_API_HOST = process.env.NEXT_PUBLIC_API_HOST || "";

/**
 * 图形校验短信验证码发送
 */
export const sendSmsWithCaptcha = (data: {
  captchaVerifyParam: string;
  phone_number: string;
  platform: PLATFORM;
  sceneId: string;
}) => {
  return r.post(
    `${NEXT_PUBLIC_API_HOST}/ucenter-api/api/v1/sms/sendWithCaptcha`,
    {
      ...data,
      business_type: "login",
    },
    {
      showToast: false,
    }
  );
};

/**
 * 教师身份验证
 */
export const verifyTeacher = (data: {
  phoneNumber: string;
  verificationCode: string;
}) => {
  return r.post<{
    isVerified: boolean;
    schools: School[];
  }>(`${NEXT_PUBLIC_API_HOST}/ucenter-api/api/v1/access/teacher/verify`, data, {
    showToast: false,
  });
};

/**
 * 教师登录
 */
export const login = (userPhone: string, verifyCode: string) => {
  return r.post<LoginResponse>(
    `${NEXT_PUBLIC_API_HOST}/ucenter-api/api/v1/access/login`,
    {
      // 登录方式 => 1为手机验证码 2为账号密码
      loginTypeId: 1,
      // 登录端 => 1为学生端 2为教师端
      platformId: 2,
      userPhone,
      verifyCode,
    },
    {
      showToast: false,
    }
  );
};
```

### 9. 自定义Hook实现

#### 9.1 认证Hook (hooks/useAuth.ts)

```typescript
import React from "react";

export const AuthContext = React.createContext<{
  token: string | null,
  schoolId: number | null,
  /**
   * 退出登录
   */
  logout: () => void,
  /**
   * 登录
   */
  login: (payload: {token: string, schoolId: number}) => void,
} | null>(null);

export function useAuth() {
  const context = React.useContext(AuthContext);

  if (!context) {
    throw new Error("useAuth must be used within a AuthProvider.");
  }

  return context;
}
```

#### 9.2 应用Hook (hooks/useApp.ts)

```typescript
import { UserInfo } from "@/types";
import { Signal } from "@preact-signals/safe-react";
import React from "react";
import { useUserRoles } from "./useUserRoles";
export const AppContext = React.createContext<AppContextProps | null>(null);

export function useApp() {
  const context = React.useContext(AppContext);

  if (!context) {
    throw new Error("useApp must be used within a AppProvider.");
  }

  return context;
}

export type AppContextProps = {
  primarySubject: { subjectId: number; subjectName: string } | null;
  /**
   * 状态栏高度，单位：px
   */
  statusBarHeight: number;
  // getAllSubjects: ReturnType<typeof useUserRoles>["getAllSubjects"];
  hasJobTypes: ReturnType<typeof useUserRoles>["hasJobTypes"];
  hasJobType: ReturnType<typeof useUserRoles>["hasJobType"];
  getRoleSummary: ReturnType<typeof useUserRoles>["getRoleSummary"];
  open: Signal<boolean>;
  setOpen: (open: boolean) => void;
  userInfo: UserInfo | null;
};
```


#### 9.3 设备检测Hook (hooks/useDevice.ts)

```typescript
import React from "react";

export const DeviceContext = React.createContext<{
  screenSize: { width: number; height: number, statusBarHeight: number } | null;
} | null>(null);

export function useDevice() {
  const context = React.useContext(DeviceContext);

  if (!context) {
    throw new Error("useDevice must be used within a DeviceProvider.");
  }

  return context;
}
```

### 10. 类型定义

#### 10.1 课程类型 (types/course.ts)

```typescript
import { LearningStatus, SUBJECT } from "@/enums";

export interface GetCourseTableParams {
  /** 教师ID */
  teacher_id: number;
  /** 学校ID */
  school_id: number;
  /** 学年ID */
  school_year_id: number;
  /** 开始日期 YYYY-MM-DD */
  start_date: string;
  /** 结束日期 YYYY-MM-DD */
  end_date: string;
}

export interface Schedule {
  /** 课程表ID */
  schedule_id: number;
  /** 年级 */
  grade: number;
  /** 年级名称 */
  grade_name: string;
  /** 班级ID */
  class_id: number;
  /** 班级名称 */
  class_name: string;
  /** 时间段（上午/下午/晚上） */
  schedule_tpl_period_time_span: string;
  /** 开始时间 HH:mm:ss */
  schedule_tpl_period_start_time: string;
  /** 结束时间 HH:mm:ss */
  schedule_tpl_period_end_time: string;
  /** 课程ID */
  class_schedule_course_id: SUBJECT;
  /** 课程名称 */
  class_schedule_course: string;
  /** 教师ID */
  class_schedule_teacher_id: number;
  /** 教师姓名 */
  teacher_name: string;
  /** 学习类型 1:课程 2:学科自习 */
  class_schedule_study_type: number;
  /** 学习类型名称 */
  class_schedule_study_type_name: string;
  /** 是否临时课程 0:否 1:是 */
  is_tmp: number;
  /** 日期 */
  at_day: string;
  /** 原课程表ID */
  origin_schedule_id: number;
  /** 在时间范围内，可点击 */
  is_in_time_range: boolean;
  /** 是否在上课 */
  is_in_class: boolean;
  /** 课堂ID */
  classroom_id: string;
  /** 课程图标 */
  course_icon_url: string;
  /** 课程背景颜色 */
  course_bg_color: string;
  /** 课程边框颜色 */
  course_border_color: string;
  /** 课程图标颜色 */
  course_icon_color: string;
  /** 课程文本颜色 */
  course_text_color: string;
  /** 课程简称 */
  course_abbreviation: string;
}

export interface GetCourseTableResponse {
  /** 课程表数据，key为日期（YYYY-MM-DD），value为当天的课程列表 */
  schedule: Record<string, Schedule[]>;
}

export interface StudentInfo {
  /** 学生ID */
  studentId: number;
  /** 学生姓名 */
  studentName: string;
  /** 学生头像 */
  avatarUrl: string;
}

/** 课堂学习能量排行数据 */
export interface CourseRankingItem {
  /** 学生ID */
  student_id: number;
  /** 学生姓名 */
  student_name: string;
  /** 学生头像 */
  student_avatar: string;
  /** 学习能量 */
  score: number;
  /** 排名 */
  rank: number;
}

/** 学生行为类型 */
export interface BehaviorType {
  /** 行为类型ID */
  type: number;
  /** 行为类型名称 */
  name: string;
  /** 行为类型描述 */
  description: string;
  /** 行为类型图标 */
  icon: string;
}

/** 学生最新行为数据 */
export interface StudentLatestBehavior {
  /** 学生ID */
  student_id: number;
  /** 学生姓名 */
  student_name: string;
  /** 学生头像 */
  student_avatar: string;
  /** 行为类型 */
  behavior_type: number;
  /** 行为时间 */
  behavior_time: string;
  /** 行为描述 */
  behavior_description: string;
}

/** 学生课堂详情数据 */
export interface StudentCourseDetail {
  /** 基本信息 */
  basic: {
    student_id: number;
    student_name: string;
    student_avatar: string;
    class_name: string;
    seat_number: string;
  };
  /** 学习数据 */
  study: {
    score: number;
    rank: number;
    attention_rate: number;
    task_completion_rate: number;
  };
  /** 行为记录 */
  behaviors: {
    type: number;
    time: string;
    description: string;
  }[];
}

// 课堂学习能量排行的返回值类型
export interface CourseRankingData {
  classroomId: string;
  queryTime: number;
  students: {
    studentId: number;
    studentName: string;
    avatarUrl: string;
    learningScore: number;
    learningTime: number;
    correctCount: number;
    totalCount: number;
  }[];
}

/** 关注学生 */
export interface AttentionStudent {
  studentId: number; // 学生ID
  studentName: string; // 学生姓名
  behaviorType: string; // 行为类型
  behaviorDesc: string; // 行为描述
  behaviorTags: {
    type: string;
    count: number;
    text: string;
    isTrigger: boolean;
  }[];
  reminderCount: number; // 已提醒次数
  totalQuestions: number; // 总题目数
  correctAnswers: number; // 正确题数
  wrongAnswers: number; // 错误题数
  accuracyRate: number; // 正确率(%)
  learningProgress: number; // 学习进度(%)
  lastUpdateTime: number; // 最后更新时间(UTC秒数)
  avatarUrl: string; // 头像URL
  isHandled: boolean; // 是否已处理
  handleTime: number; // 处理时间(UTC秒数)
}

/** 表扬学生 */
export interface PraiseStudent {
  studentId: number; // 学生ID
  studentName: string; // 学生姓名
  behaviorType: string; // 行为类型
  behaviorDesc: string; // 行为描述
  behaviorTags: {
    type: string;
    count: number;
    text: string;
    isTrigger: boolean;
  }[];
  praiseCount: number; // 表扬次数
  reminderCount: number; // 已提醒次数
  totalQuestions: number; // 总题目数
  correctAnswers: number; // 正确题数
  wrongAnswers: number; // 错误题数
  accuracyRate: number; // 正确率(%)
  learningProgress: number; // 学习进度(%)
  lastUpdateTime: number; // 最后更新时间(UTC秒数)
  avatarUrl: string; // 头像URL
  isHandled: boolean; // 是否已处理
  handleTime: number; // 处理时间(UTC秒数)
}

export interface LearningScore {
  avatarUrl: string;
  correctCount: number;
  handleTime: number;
  isHandled: boolean;
  learningScore: number;
  learningTime: number;
  studentId: number;
  studentName: string;
  totalCount: number;
}

export type StudentLearningStatusData = {
  accuracyRate: number;
  avatarUrl: string;
  curStudyContent: string;
  isOnline: boolean;
  lastUpdateTime: number;
  learningProgress: number;
  learningStatus: LearningStatus;
  // 是不是当前学科
  isLearningSubject: boolean;
  studentId: number;
  studentName: string;
};

// 课堂表扬关注提醒行为分类列表
export interface GetBehaviorCategorysResponse {
  query_time: string;
  classroomId: string;
  attentionList: AttentionStudent[];
  praiseList: PraiseStudent[];
  learningScores: LearningScore[];
  allStudentsList: StudentLearningStatusData[];
}

// 提醒学生的返回值类型
export interface RemindStudentData {
  classroomId: string;
  success: boolean;
  message: string;
  handleTime: number;
  results: {
    studentId: number;
    studentName: string;
    success: boolean;
    message: string;
  }[];
}

// 学生课堂详情的返回值类型
export interface StudentCourseDetailData {
  studentId: number;
  classroomId: string;
  schoolId: number;
  classId: number;
  totalStudyTime: number;
  classroomScore: number;
  maxCorrectStreak: number;
  questionCount: number;
  accuracyRate: number;
  interactionCount: number;
  violationCount: number;
  violationTime: number;
  /** 是否已评价 */
  isEvaluated: boolean;
  /** 评价内容 */
  evaluateContent: string;
  /** 评价推送时间 */
  pushTime: number;
  /** 是否已鼓励 */
  isHandled: boolean;
  learningRecords: {
    accuracyRate: number | number;
    chapterId: string;
    chapterName: string;
    createTime: number;
    duration: number;
    learningType: string;
    progress: number;
    recordId: string;
    taskId: number;
    assignId: number;
  }[];
}

// 检测是否上课的返回值类型
export interface IsTeachingData {
  is_teaching: boolean;
  schedule: Schedule | null;
}

// 课堂信息
export interface GetCourseInfoResponse {
  classroomId: string;
  gradeName: string;
  className: string;
  subject: string;
  subjects: {
    subjectKey: number;
    subjectName: string;
  }[];
  isInClass: boolean;
}

// 班级学生最新行为数据
export type GetStudentLearningStatusResponse = StudentLearningStatusData[];
```

#### 10.2 作业类型 (types/homeWork.ts)

```typescript
import { TASK_TYPE } from "@/enums";

export interface HomeWorkData {
  tasks: Homework[] | null;
  pageInfo: PageInfo;
}

export interface Course {
  id: number;
  name: string;
}

export interface Resource {
  id: string;
  name: string;
  type: number;
}

export interface Homework {
  courseList?: Course[];
  reports: HomeworkReport[];
  taskId: number;
  taskName: string;
  /**
   * 任务类型，10课程任务，20作业任务，30测试任务，40资源任务
   */
  taskType: TASK_TYPE;
  resources: Resource[];
  subject: number;
  creatorId: number;
}

export interface HomeworkReport {
  assignId: number;
  /**
   * 布置对象信息
   */
  assignObject: AssignObject;
  /**
   * 任务统计数据
   */
  statData: StatData;
}

/**
 * 布置对象信息
 */
export interface AssignObject {
  /**
   * 布置对象类型
   */
  type: number;

  /**
   * 布置对象ID
   */
  id: number;
  /**
   * 布置对象名称
   */
  name: string;
  [property: string]: any;
}

/**
 * 任务统计数据
 */
export interface StatData {
  /**
   * 平均进度，资源类任务
   */
  averageProgress?: number;
  /**
   * 课时数，资源类任务
   */
  classHours?: number;
  /**
   * 完成率，非资源类任务
   */
  completionRate: number;
  /**
   * 正确率，非资源类任务
   */
  correctRate?: number;
  /**
   * 结束时间，时间戳秒
   */
  deadline?: number;
  /**
   * 待关注题目数，非资源类任务
   */
  needAttentionQuestionNum?: number;
  /**
   * 开始时间，时间戳秒
   */
  startTime?: number;
}

export interface PageInfo {
  page: number;
  pageSize: number;
  total: number;
  [property: string]: any;
}

/* -----------------------------------------作业详情----------------------------------------- */
export interface HomeworkDetailsData {
  /**
   * 任务报告数据
   */
  students: StudentBase[];
  detail: HomeworkDetail;
  pageInfo: PageInfo;
}

/**
 * 任务报告数据
 */
export interface HomeworkDetail {
  attentionList: number[];
  attentionStudents: StudentBase[];
  /**
   * 平均正确率，0-1
   */
  avgAccuracy: number;
  /**
   * 班级进度，0-1
   */
  avgProgress: number;
  /**
   * 平均用时，分钟
   */
  avgCostTime: number;
  /**
   * 表扬列表
   */
  praiseList: number[];
  praiseStudents: StudentBase[];
  /**
   * 单个学生的任务报告汇总数据列表
   */
  studentReports: HomeworkDetailReport[];
  /**
   * 资源报告数据列表
   */
  resourceReports: ResourceReport[];
  /**
   * 所属学科
   */
  subjectId: number;
}

/**
 * 资源报告数据
 */
export interface ResourceReport {
  /**
   * 资源ID
   */
  resourceId: string;
  /**
   * 资源类型
   */
  resourceType: number;
  /**
   * 资源名称
   */
  resourceName: string;
  /**
   * 完成率，0-100
   */
  completionRate: number;
  /**
   * 正确率，0-100
   */
  correctRate: number;
  /**
   * 需要关注的题目数量
   */
  needAttentionQuestionNum: number;
  /**
   * 需要关注的学生数量
   */
  needAttentionUserNum: number;
  /**
   * 平均用时，分钟
   */
  averageCostTime: number;

  /**
   * 资源ID，如果是练习，则是练习ID，如果是课程，则是课程的节点ID
   */
  extendedResourceId: string;
}

/**
 * 学生信息基础接口
 */
export interface StudentBase {
  /**
   * 学生 id
   */
  studentId: number;
  /**
   * 学生姓名
   */
  studentName: string;
  /**
   * 头像链接
   */
  avatar?: string;
  /**
   * 推送默认文本
   */
  pushDefaultText?: string;
  /**
   * 课程链接
   */
  courseUrl?: string;
}

export interface HomeworkDetailReport {
  /**
   * 学习能量
   */
  studyScore: number;
  /**
   * 正确率
   */
  accuracyRate: number;
  /**
   * 答题数
   */
  answerNum: number;
  /**
   * 用时，分钟
   */
  costTime: number;
  /**
   * 难度系数
   */
  difficultyDegree: number;
  /**
   * 错题数
   */
  incorrectNum: number;
  /**
   * 完成进度
   */
  progress: number;
  /**
   */
  studentId: number;

  /**
   * 学生标签列表，html 格式
   */
  tags: {
    /**
     * 标签类型
     */
    type: -1 | 0 | 1;
    /**
     * 标签内容
     */
    label: string;
  }[];
}

/**
 * 班级视图答题结果数据
 */
export interface TaskReportAnswersData {
  /**
   * 班级进度，0-1
   */
  progress: number;
  /**
   * 任务布置 id
   */
  assignId: number;
  /**
   * 共性错题数
   */
  commonIncorrectCount: number;
  /**
   * 分页信息
   */
  pageInfo: PageInfo;
  /**
   * 答题信息
   */
  questionAnswers: QuestionAnswer[];
  /**
   * 资源 id，指定查询资源时返回
   */
  resourceId?: string;
  /**
   * 资源类型，指定查询资源时返回
   */
  resourceType?: number;
  /**
   * 任务布置的学生列表
   */
  students: Student[];
  /**
   * 任务 id
   */
  taskId: number;
  /**
   * 任务类型
   */
  taskType?: number;
  /**
   * 题目总数，和过滤条件无关
   */
  totalCount: number;
  [property: string]: any;
}

export interface QuestionAnswer {
  /**
   * 作答人数
   */
  answerCount: number;
  /**
   * 每个学生的答题信息
   */
  answers?: AnswerInfo[];
  /**
   * 平均用时，秒
   */
  avgCostTime: number;
  /**
   * 答错人数
   */
  inCorrectCount: number;
  /**
   * 题目信息
   */
  question: Question;
  /**
   * 资源 id
   */
  resourceId: string;
  /**
   * 资源类型
   */
  resourceType: number;
  [property: string]: any;
}

export interface AnswerInfo {
  /**
   * 学生作答
   */
  answer: string;
  /**
   * 作答时长，秒
   */
  costTime: number;
  /**
   * 是否正确
   */
  isCorrect: boolean;
  /**
   * 学生 id
   */
  studentId: number;
  [property: string]: any;
}

/**
 * 题目信息
 */
export interface Question {
  /**
   * 题目添加顺序，从 1 开始
   */
  index: number;
  /**
   * 题目答案
   */
  questionAnswer: string;
  /**
   * 题目内容
   */
  questionContent: QuestionContent;
  /**
   * 题目难度
   */
  questionDifficult: number;
  /**
   * 题目解答说明
   */
  questionExplanation: string;
  /**
   * 题目 id
   */
  questionId: string;
  /**
   * 题目标签
   */
  questionTags: string[];
  /**
   * 题目主题
   */
  questionTopic: string;
  /**
   * 0全部题型 ，1单选题，2多选题，3填空题，4判断题，5解答题
   */
  questionType: number;
  [property: string]: any;
}

/**
 * 题目内容
 */
export interface QuestionContent {
  /**
   * 选择题的选项
   */
  questionOptionList?: QuestionOptionList[];
  /**
   * 题目序号
   */
  questionOrder: number;
  /**
   * 题目分数
   */
  questionScore: number;
  /**
   * 题干
   */
  questionStem: string;
  [property: string]: any;
}

export interface QuestionOptionList {
  /**
   * 选项 key
   */
  optionKey: string;
  /**
   * 详细内容
   */
  optionVal: string;
  [property: string]: any;
}

export interface Student {
  /**
   * 头像链接
   */
  avatar: string;
  /**
   * 学生 id
   */
  studentId: number;
  /**
   * 学生姓名
   */
  studentName: string;
  [property: string]: any;
}

/**
 * 学生视图答题结果数据，与班级视图结构相同，但answers变为answer
 */
export interface StudentTaskReportAnswersData
  extends Omit<TaskReportAnswersData, "questionAnswers"> {
  /**
   * 答题信息，注意这里是answer而不是answers
   */
  questionAnswers: StudentQuestionAnswer[];
}

export interface StudentQuestionAnswer extends Omit<QuestionAnswer, "answers"> {
  /**
   * 学生作答信息，注意这里是answer而不是answers
   */
  answer?: AnswerInfo;
}

/**
 * 学生行为处理请求参数
 */
export interface StudentBehaviorRequest {
  /**
   * 任务ID
   */
  taskId: number;
  /**
   * 任务布置ID
   */
  assignId: number;
  /**
   * 学生ID列表
   */
  studentIds: number[];
  /**
   * 行为类型：task_praise(鼓励) / task_attention(关注)
   */
  behaviorType: "task_praise" | "task_attention";
  /**
   * 内容
   */
  content?: string;
}

/**
 * 学生详情接口返回类型
 */
export interface StudentDetailResponse {
  studentId: number;
  studentName: string;
  avatar: string;
  praiseCount: number;
  attentionCount: number;
  studentAccuracyRate: number;
  studentCompletedProgress: number;
  classAccuracyRate: number;
  classCompletedProgress: number;
  attentionText: string;
  attentionTextList: string[];
  pushDefaultText: string;
  praiseDefaultText: string;
  courseUrl?: string;
}

/**
 * 学生状态类型
 */
export type StudentStatus = "good" | "attention";

/**
 * 学生详情信息
 */
export interface StudentDetail {
  id: string | number;
  name: string;
  avatar: string;
  className: string;
  status: StudentStatus;
  praiseCount: number;
  attentionCount: number;
  performance: {
    homeworkAccuracy: number;
    averageAccuracy: number;
    completionRate: number;
    averageCompletionRate: number;
  };
  feedback: {
    description: string;
    recommendations: string[];
  };
  pushDefaultText: string;
  courseUrl?: string;
}

export interface StudentDetailV2 {
  studentId: number;
  studentName: string;
  avatar: string;
  attentionCount: number;
  displayMetrics: {
    deadline: number;
    overdueDays: number;
    progress: number;
  };
  priority: number;
  pushDefaultText: string;
  recommendationReason: string;
  recommendationType: string;
  courseUrl: string;
  suggestedActions: string[];
}
```

### 11. 配置文件

#### 11.1 应用配置 (configs/index.ts)

```typescript
export * from "./assign";
export * from "./assign-homework";
export * from "./captcha";
export * from "./feedback";
export * from "./font";
export * from "./layout";
export * from "./login";
export * from "./storage";
export * from "./urls";
```

### 12. 通用组件

#### 12.1 页面头部组件 (components/PageHeader.tsx)

```typescript
"use client";
import NavBackIcon from "@/public/icons/ic_nav_back.svg";
import { cn } from "@/utils";
import { useRouter } from "next/navigation";
import { memo } from "react";

const PageHeader = memo(function PageHeader({
  children,
  className,
  needBack = false,
  onBack,
}: {
  children?: React.ReactNode;
  className?: string;
  needBack?: boolean;
  onBack?: () => void;
}) {
  const router = useRouter();

  return (
    <div className={cn("h-17.5 flex items-center pl-6", className)}>
      {needBack && (
        <NavBackIcon
          onClick={() => {
            if (onBack) {
              onBack();
              return;
            }

            router.back();
          }}
          className="cursor-pointer active:opacity-80"
        />
      )}

      {children}
    </div>
  );
});

export { PageHeader };
```

#### 12.2 即将推出组件 (components/ComingSoon.tsx)

```typescript
import IcComingSoon from "@/public/icons/ic_coming_soon.svg";

export default function ComingSoon() {
  return (
    <div className="flex size-full flex-col items-center justify-center">
      <IcComingSoon className="w-33.75" />

      <span className="text-gray-4 mt-2 text-sm/normal">
        功能开发中，敬请期待
      </span>
    </div>
  );
}
```

---

**文档说明**: 本文档包含了小鹿爱学教师端软件的核心源代码文件，展示了软件的主要功能模块、技术架构和实现细节。所有代码均为原创开发，符合软件著作权申请要求。


### 13. 作业服务完整实现 (services/homework.ts)

```typescript
/**
 * 查询班级下的题目列表数据
 * @param params 请求参数
 * @returns Promise<TaskReportAnswersDataV2>
 */
export const getTaskReportAnswers = async (
  params: TaskReportAnswersRequest
) => {
  return r.get<TaskReportAnswersData>("/task/report/answers", {
    params: params,
  });
};

/**
 * 查询学生视图下的题目列表数据
 * @param params 请求参数
 * @returns Promise<StudentTaskReportAnswersData>
 */
export interface StudentAnswersRequest extends TaskReportAnswersRequest {
  /**
   * 学生 ID
   */
  studentId: number;
}

export const getStudentTaskReportAnswers = async (
  params: StudentAnswersRequest
) => {
  return r.get<StudentTaskReportAnswersData>("/task/report/student/answers", {
    params: params,
  });
};

/**
 * 导出报告接口参数
 */
export interface ExportReportParams {
  /**
   * 任务布置ID
   */
  assignId: number;
  /**
   * 要导出的字段，用英文逗号连接，支持字段：studentName 学生姓名, studyScore 学习能量, progress 完成进度, accuracyRate 正确率,
   * difficultyDegree 难度, incorrectCount/answerCount 错题数/答题数
   */
  fields?: string;
  /**
   * 资源 id，查询具体资源对象的报告，为空表示查询所有
   */
  resourceId?: string;
  /**
   * 资源类型，必须和资源 id 同时赋值
   */
  resourceType?: number;
  /**
   * 任务 id
   */
  taskId: number;
  /**
   * 排序字段
   * studyScore - 学习能量
   * progress - 完成进度
   * accuracyRate - 正确率
   * answerCount - 答题数
   */
  sortBy?: "studyScore" | "progress" | "accuracyRate" | "answerCount";
  /**
   * 排序方式
   * asc - 升序
   * desc - 降序
   */
  sortType?: "asc" | "desc";
}

/**
 * 导出报告
 * @param params 导出参数
 * @returns Promise<Blob> 返回二进制数据
 */
export const exportReport = async (
  params: ExportReportParams
): Promise<Blob> => {
  const response = await r.get<Blob>("/task/report/export", {
    params: params,
    responseType: "blob",
  });

  // 返回二进制数据
  return response;
};

/**
 * 获取学科班级列表
 * @returns Promise<SubjectClassListData>
 */
export const getSubjectClassList = async (): Promise<SubjectClassListData> => {
  // 使用 mock 数据
  // return mockSubjectClassList();

  // 实际调用接口
  return r.get<SubjectClassListData>("/task/report/subject-class/list");
};

export const getTeacherTaskClassList = async (params: {
  taskId: number;
  classes: Array<{ classId: number; className: string }>;
}): Promise<
  Array<{
    taskId: number;
    assignId: number;
    classId: number;
    className: string;
  }>
> => {
  return r.post<
    Array<{
      taskId: number;
      assignId: number;
      classId: number;
      className: string;
    }>
  >("/task/report/getTeacherTaskClassList", params);
};

export const getClassTaskAvgAccuracyAndAvgProgress = async (param: {
  taskId: number;
  assignId: number;
}): Promise<{
  avgAccuracy: number;
  avgProgress: number;
  commonIncorrectCount: number;
}> => {
  return r.get<{
    avgAccuracy: number;
    avgProgress: number;
    commonIncorrectCount: number;
  }>("/task/report/getClassTaskAvgAccuracyAndAvgProgress", {
    params: {
      taskId: param.taskId,
      assignId: param.assignId,
    },
  });
};

export const getTaskResourceReport = async (param: {
  taskId: number;
  assignId: number;
  studentId?: number;
}) => {
  return r.get<{
    resourceReports: Array<{
      averageCostTime: number;
      completionRate: number;
      correctRate: number;
      needAttentionQuestionNum: number;
      needAttentionUserNum: number;
      resourceId: string;
      resourceName: string;
      resourceType: number;
      extendedResourceId: string;
      resourceExtra: {
        firstLevelBizTreeNodeId: number;
        firstLevelBizTreeNodeName: string;
        firstLevelBizTreeNodeSerialPath: string;
        lastLevelBizTreeNodeId: number;
        lastLevelBizTreeNodeName: string;
        lastLevelBizTreeNodeSerialPath: string;
      };
    }>;
  }>("/task/report/getTaskResourceReport", {
    params: {
      taskId: param.taskId,
      assignId: param.assignId,
      studentId: param.studentId,
    },
  });
};

/**
 * 获取学生详情信息
 * @param taskId 任务ID
 * @param assignId 布置ID
 * @param studentId 学生ID
 */
export const getStudentDetail = (
  taskId: number,
  assignId: number,
  studentId: number,
  schoolId: number,
  recommendationType: "task_praise" | "task_attention" | "task_other",
  resourceId?: string
): Promise<StudentDetailResponse> => {
  return r.get<StudentDetailResponse>("/task/report/student/detail", {
    params: {
      taskId,
      assignId,
      studentId,
      schoolId,
      recommendationType,
      resourceId,
    },
  });
};
```

### 14. 设备提供者组件 (_layout/DeviceProvider.tsx)

```typescript
"use client";
import { DeviceContext } from "@/hooks";
import { useEffect, useState } from "react";

export default function DeviceProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [screenSize, setScreenSize] = useState<{
    width: number;
    height: number;
    statusBarHeight: number;
  } | null>(null);

  useEffect(() => {
    const updateScreenSize = () => {
      const size = {
        width: window.innerWidth,
        height: window.innerHeight,
        statusBarHeight: 0, // Web端状态栏高度为0
      };
      setScreenSize(size);
    };

    updateScreenSize();
    window.addEventListener("resize", updateScreenSize);

    return () => {
      window.removeEventListener("resize", updateScreenSize);
    };
  }, []);

  return (
    <DeviceContext.Provider value={{ screenSize }}>
      {children}
    </DeviceContext.Provider>
  );
}
```

### 15. 应用骨架屏组件 (_layout/AppSkeleton.tsx)

```typescript
"use client";
import { useEffect, useState } from "react";

export default function AppSkeleton() {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // 在应用加载完成后隐藏骨架屏
    const timer = setTimeout(() => {
      setIsVisible(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 bg-white">
      <div className="flex h-full items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          {/* Logo */}
          <div className="h-16 w-16 animate-pulse rounded-lg bg-gray-200" />
          
          {/* 加载文字 */}
          <div className="text-gray-500">
            <div className="flex items-center space-x-1">
              <div className="h-2 w-2 animate-bounce rounded-full bg-blue-500" />
              <div className="h-2 w-2 animate-bounce rounded-full bg-blue-500 delay-100" />
              <div className="h-2 w-2 animate-bounce rounded-full bg-blue-500 delay-200" />
            </div>
          </div>
          
          {/* 加载条 */}
          <div className="h-1 w-48 overflow-hidden rounded-full bg-gray-200">
            <div className="h-full w-1/3 animate-pulse bg-blue-500" />
          </div>
        </div>
      </div>
    </div>
  );
}
```

### 16. 个人中心页面完整实现

#### 16.1 教师信息组件 (personal-center/_components/TeacherInfo.tsx)

```typescript
"use client";
import { useApp } from "@/hooks";
import { Avatar, AvatarFallback, AvatarImage } from "@/ui/avatar";
import { Card, CardContent } from "@/ui/card";
import { Badge } from "@/ui/badge";

export default function TeacherInfo() {
  const { userInfo } = useApp();

  if (!userInfo) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="flex items-center space-x-4">
              <div className="h-16 w-16 rounded-full bg-gray-200" />
              <div className="space-y-2">
                <div className="h-4 w-32 bg-gray-200 rounded" />
                <div className="h-3 w-24 bg-gray-200 rounded" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center space-x-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={userInfo.avatar} alt={userInfo.name} />
            <AvatarFallback>{userInfo.name?.charAt(0)}</AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-1">
              {userInfo.name}
            </h3>
            <p className="text-sm text-gray-4 mb-2">
              {userInfo.schoolName}
            </p>
            
            <div className="flex flex-wrap gap-2">
              {userInfo.teacherJobInfos?.map((job: any, index: number) => (
                <Badge key={index} variant="secondary">
                  {job.gradeName} {job.className} - {job.subjectName}
                </Badge>
              ))}
            </div>
          </div>
        </div>
        
        <div className="mt-4 grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-primary-2">
              {userInfo.totalClasses || 0}
            </div>
            <div className="text-sm text-gray-4">任教班级</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-primary-2">
              {userInfo.totalStudents || 0}
            </div>
            <div className="text-sm text-gray-4">学生总数</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-primary-2">
              {userInfo.totalTasks || 0}
            </div>
            <div className="text-sm text-gray-4">布置任务</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
```

#### 16.2 我的消息组件 (personal-center/_components/MyMessage.tsx)

```typescript
"use client";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { ScrollArea } from "@/ui/scroll-area";
import { Bell, MessageCircle, AlertTriangle } from "lucide-react";
import { RefObject } from "react";

interface MyMessageProps {
  viewportRef: RefObject<HTMLDivElement>;
}

export default function MyMessage({ viewportRef }: MyMessageProps) {
  // 模拟消息数据
  const messages = [
    {
      id: 1,
      type: "system",
      title: "系统通知",
      content: "您有新的作业需要批改",
      time: "2小时前",
      unread: true,
    },
    {
      id: 2,
      type: "student",
      title: "学生提问",
      content: "张三同学对数学作业有疑问",
      time: "4小时前",
      unread: true,
    },
    {
      id: 3,
      type: "announcement",
      title: "学校公告",
      content: "下周一将进行教学质量检查",
      time: "1天前",
      unread: false,
    },
  ];

  const getMessageIcon = (type: string) => {
    switch (type) {
      case "system":
        return <Bell className="h-4 w-4" />;
      case "student":
        return <MessageCircle className="h-4 w-4" />;
      case "announcement":
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getMessageColor = (type: string) => {
    switch (type) {
      case "system":
        return "bg-blue-100 text-blue-800";
      case "student":
        return "bg-green-100 text-green-800";
      case "announcement":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>我的消息</span>
          <Badge variant="secondary">
            {messages.filter(m => m.unread).length} 条未读
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-64" viewportRef={viewportRef}>
          <div className="space-y-3">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex items-start space-x-3 p-3 rounded-lg border ${
                  message.unread ? "bg-blue-50 border-blue-200" : "bg-gray-50 border-gray-200"
                }`}
              >
                <div className={`p-2 rounded-full ${getMessageColor(message.type)}`}>
                  {getMessageIcon(message.type)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-1 truncate">
                      {message.title}
                    </h4>
                    <span className="text-xs text-gray-4 ml-2">
                      {message.time}
                    </span>
                  </div>
                  <p className="text-sm text-gray-3 mt-1 line-clamp-2">
                    {message.content}
                  </p>
                  {message.unread && (
                    <div className="mt-2">
                      <Badge variant="destructive" className="text-xs">
                        未读
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
```

### 17. 资源管理页面实现

#### 17.1 我的资源页面 (resource/my/page.tsx)

```typescript
"use client";
import { Card, CardContent } from "@/ui/card";
import { Button } from "@/ui/button";
import { Upload, FileText, Video, Image as ImageIcon } from "lucide-react";
import { useState } from "react";

export default function MyResourcePage() {
  const [resources] = useState([
    {
      id: 1,
      name: "数学课件-第一章",
      type: "ppt",
      size: "2.5MB",
      uploadTime: "2024-01-15",
      downloads: 23,
    },
    {
      id: 2,
      name: "语文教学视频",
      type: "video",
      size: "156MB",
      uploadTime: "2024-01-14",
      downloads: 45,
    },
    {
      id: 3,
      name: "英语练习题",
      type: "doc",
      size: "1.2MB",
      uploadTime: "2024-01-13",
      downloads: 67,
    },
  ]);

  const getFileIcon = (type: string) => {
    switch (type) {
      case "ppt":
        return <FileText className="h-8 w-8 text-orange-500" />;
      case "video":
        return <Video className="h-8 w-8 text-blue-500" />;
      case "doc":
        return <FileText className="h-8 w-8 text-blue-600" />;
      case "image":
        return <ImageIcon className="h-8 w-8 text-green-500" />;
      default:
        return <FileText className="h-8 w-8 text-gray-500" />;
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-1">我的资源</h2>
        <Button className="flex items-center space-x-2">
          <Upload className="h-4 w-4" />
          <span>上传资源</span>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {resources.map((resource) => (
          <Card key={resource.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                {getFileIcon(resource.type)}
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-gray-1 truncate">
                    {resource.name}
                  </h3>
                  <div className="mt-1 space-y-1">
                    <p className="text-sm text-gray-4">
                      大小: {resource.size}
                    </p>
                    <p className="text-sm text-gray-4">
                      上传: {resource.uploadTime}
                    </p>
                    <p className="text-sm text-gray-4">
                      下载: {resource.downloads} 次
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="mt-4 flex space-x-2">
                <Button variant="outline" size="sm" className="flex-1">
                  预览
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  下载
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  分享
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {resources.length === 0 && (
        <div className="text-center py-12">
          <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-1 mb-2">
            还没有上传任何资源
          </h3>
          <p className="text-gray-4 mb-4">
            开始上传您的教学资源，与其他老师分享
          </p>
          <Button>
            <Upload className="h-4 w-4 mr-2" />
            上传第一个资源
          </Button>
        </div>
      )}
    </div>
  );
}
```
