import { r } from "@/libs/axios";
import { BaseTreeInfo, KnowledgeTreeList } from "@/types/knowledgeTree";

export type GetKnowledgeTreeListParams = {
  subjectId: number;
};

export type GetKnowledgeTreeDetailParams = {
  bizTreeId: number;
};

export const getKnowledgeTreeList = ({
  subjectId,
}: GetKnowledgeTreeListParams) => {
  return r.post<KnowledgeTreeList>("/task/knowledge-tree/list", {
    subject: subjectId,
  });
};

export const getKnowledgeTreeDetail = ({
  bizTreeId,
}: GetKnowledgeTreeDetailParams) => {
  return r.post<BaseTreeInfo>("/task/knowledge-tree/detail", {
    bizTreeId,
  });
};
