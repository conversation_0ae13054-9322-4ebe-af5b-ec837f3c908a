import {
  FilterConfig,
  ResourceListParams,
  ResourceListResponse,
} from "@/types/resource";
import type {
  RefreshTokenResponse,
  WebTokenInfo,
  WebTokenResponse,
} from "@repo/core/components/doc-preview/type";
import { r } from "../libs/axios";

/**
 * 获取资源列表
 */
export const getResourceList = (data: ResourceListParams) => {
  return r.post<ResourceListResponse>("/resource/teacher/resource/list", data);
};

/**
 * 获取筛选配置
 */
export const getFilterConfig = () => {
  return r.get<FilterConfig>("/resource/filter-config");
};

/**
 * 生成用于上传教学资源的令牌，支持多种文件类型包括Office文档、图片、视频、音频等。视频文件会返回VOD令牌信息。
 * @summary 获取资源上传令牌
 */
export const getUploadToken = (payload: {
  originFile: string;
  fileType: string;
}) => {
  return r.post<{
    /**
     * 对象键
     */
    objectKey: string;
    /**
     * 上传URL
     */
    uploadUrl: string;
    /**
     * VOD令牌信息（仅视频文件）
     */
    vodToken?: {
      /**
       * 访问密钥ID
       */
      AccessKeyID: string;
      /**
       * 当前时间
       */
      CurrentTime: string;
      /**
       * 过期时间
       */
      ExpiredTime: string;
      /**
       * 秘密访问密钥
       */
      SecretAccessKey: string;
      /**
       * 会话令牌
       */
      SessionToken: string;
    };
  }>("/resource/upload/token", payload);
};

/**
 * 获取学校年级和学科列表
 */
export const getGradeAndSubjectList = () => {
  return r.post<{
    phraseGrade: {
      id: number;
      name: string;
      grades: { id: number; name: string }[];
    }[];
    subjects: { id: number; name: string }[];
  }>("/resource/school/detail");
};

/**
 * 上传资源文件
 */
export const addResource = (payload: {
  /**
   * 文件大小（字节）
   */
  fileSizebytes: number;
  /**
   * 对象键
   */
  objectKey: string;
  /**
   * 文件原始名称
   */
  originalFileName: string;
  /**
   * 视频ID
   */
  vid?: string;
  /**
   * VOD URL
   */
  vodUrl?: string;
  /**
   * 视频时长
   */
  duration: number;
}) => {
  return r.post<{ resouceId: string; teacherResourceDbId: number }>(
    "/resource/teacher/addResource",
    payload
  );
};

/**
 * 更新资源文件
 */
export const updateResource = (payload: {
  generateChannel?: number;
  grade: number[];
  subject: number[];
  teacherResourceDbId: number;
  userFileName: string;
}) => {
  return r.post<{ success: boolean }>(
    "/resource/teacher/updateResource",
    payload
  );
};

/**
 * 删除资源
 */
export const deleteResource = (teacherResourceDbId: number) => {
  return r.post(`/resource/teacher/deleteResource`, {
    TeacherResourceID: teacherResourceDbId,
  });
};

/**
 * 收藏/取消收藏资源
 */
export const toggleResourceStar = (resourceId: string, starred: boolean) => {
  return r.post(`/resource/${resourceId}/star`, { starred });
};

export const getResourceDocumentToken = (resourceId: string) => {
  return r.post<WebTokenResponse>("/resource/getDocumentPreviewToken", {
    resourceId: resourceId,
  });
};

export const getResourceDocumentRefreshToken = (
  accessToken: string,
  refreshToken: string
) => {
  return r.post<RefreshTokenResponse>("/resource/refreshDocumentPreviewToken", {
    accessToken: accessToken,
    refreshToken: refreshToken,
  });
};

export const getResourceCommonPreview = (resourceId: string) => {
  return r.post<{
    originFileName: string;
    ossUrl: string;
    vodUrl: string;
    webOffice?: WebTokenInfo;
  }>("/resource/getPreViewResources", {
    resourceId: resourceId,
  });
};
