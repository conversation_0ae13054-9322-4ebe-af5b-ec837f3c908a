import { filetypeinfo } from "magic-bytes.js";
import mime from "mime";
import React from "react";
// 导入所有 SVG 图标
import ThreeGpSmallIcon from "@/public/files/3gp-small.svg";
import AacSmallIcon from "@/public/files/aac-small.svg";
import AviSmallIcon from "@/public/files/avi-small.svg";
import DocSmallIcon from "@/public/files/doc-small.svg";
import DocxSmallIcon from "@/public/files/docx-small.svg";
import FlvSmallIcon from "@/public/files/flv-small.svg";
import FolderSmallIcon from "@/public/files/folder-small.svg";
import JpegSmallIcon from "@/public/files/jpeg-small.svg";
import JpgSmallIcon from "@/public/files/jpg-small.svg";
import M4aSmallIcon from "@/public/files/m4a-small.svg";
import MkvSmallIcon from "@/public/files/mkv-small.svg";
import MovSmallIcon from "@/public/files/mov-small.svg";
import Mp3SmallIcon from "@/public/files/mp3-small.svg";
import Mp4SmallIcon from "@/public/files/mp4-small.svg";
import MpgSmallIcon from "@/public/files/mpg-small.svg";
import PdfSmallIcon from "@/public/files/pdf-small.svg";
import PngSmallIcon from "@/public/files/png-small.svg";
import PptSmallIcon from "@/public/files/ppt-small.svg";
import PptxSmallIcon from "@/public/files/pptx-small.svg";
import RmvbSmallIcon from "@/public/files/rmvb-small.svg";
import VobSmallIcon from "@/public/files/vob-small.svg";
import WmvSmallIcon from "@/public/files/wmv-small.svg";
import XlsSmallIcon from "@/public/files/xls-small.svg";
import XlsxSmallIcon from "@/public/files/xlsx-small.svg";

/**
 * 切割完整的文件名，获取文件名和扩展名
 * @param fullFileName  hello.txt => [hello, txt]
 */
export function getFileNameAndExtension(fullFileName: string) {
  const lastDotIndex = fullFileName.lastIndexOf(".");

  if (lastDotIndex === -1) {
    // 没有找到点，返回原文件名和空扩展名
    return [fullFileName, ""];
  }

  const name = fullFileName.substring(0, lastDotIndex);
  const extension = fullFileName.substring(lastDotIndex + 1);

  return [name, extension];
}

/**
 * 根据文件扩展名获取对应的文件图标组件
 * @param fileExtension 文件扩展名（如 "pdf", "doc", "jpg" 等）
 * @returns React 组件构造函数
 */
export function getFileIcon(
  fileExtension?: string
): React.ComponentType<React.SVGProps<SVGSVGElement>> {
  // 将扩展名转换为小写，去除可能的点号
  const ext = fileExtension?.toLowerCase().replace(/^\./, "") || "";

  // 根据文件扩展名返回对应的 SVG 组件
  switch (ext) {
    // 文档类型
    case "pdf":
      return PdfSmallIcon;
    case "doc":
      return DocSmallIcon;
    case "docx":
      return DocxSmallIcon;

    // 表格类型
    case "xls":
      return XlsSmallIcon;
    case "xlsx":
      return XlsxSmallIcon;

    // 演示文稿类型
    case "ppt":
      return PptSmallIcon;
    case "pptx":
      return PptxSmallIcon;

    // 图片类型
    case "jpg":
      return JpgSmallIcon;
    case "jpeg":
      return JpegSmallIcon;
    case "png":
    case "gif": // 使用 png 图标作为 gif 的替代
      return PngSmallIcon;

    // 音频类型
    case "mp3":
      return Mp3SmallIcon;
    case "aac":
      return AacSmallIcon;
    case "m4a":
      return M4aSmallIcon;

    // 视频类型
    case "mp4":
      return Mp4SmallIcon;
    case "avi":
      return AviSmallIcon;
    case "mov":
      return MovSmallIcon;
    case "mkv":
      return MkvSmallIcon;
    case "flv":
      return FlvSmallIcon;
    case "wmv":
      return WmvSmallIcon;
    case "mpg":
      return MpgSmallIcon;
    case "rmvb":
      return RmvbSmallIcon;
    case "vob":
      return VobSmallIcon;
    case "3gp":
      return ThreeGpSmallIcon;

    // 文件夹
    case "folder":
      return FolderSmallIcon;

    // 文本类型和默认情况
    case "txt":
    default:
      return DocSmallIcon;
  }
}

/**
 * 获取文件类型的显示名称
 * @param fileExtension 文件扩展名
 * @returns 文件类型的显示名称
 */
export function getFileTypeName(fileExtension?: string): string {
  if (!fileExtension) return "文件";

  const ext = fileExtension.toLowerCase().replace(/^\./, "");

  const typeNameMap: Record<string, string> = {
    pdf: "PDF文档",
    doc: "Word文档",
    docx: "Word文档",
    xls: "Excel表格",
    xlsx: "Excel表格",
    ppt: "PowerPoint演示",
    pptx: "PowerPoint演示",
    jpg: "图片",
    jpeg: "图片",
    png: "图片",
    gif: "图片",
    mp3: "音频",
    aac: "音频",
    m4a: "音频",
    mp4: "视频",
    avi: "视频",
    mov: "视频",
    mkv: "视频",
    flv: "视频",
    wmv: "视频",
    mpg: "视频",
    rmvb: "视频",
    vob: "视频",
    "3gp": "视频",
    txt: "文本文件",
    folder: "文件夹",
  };

  return typeNameMap[ext] || "文件";
}

/**
 * 检查文件类型是否为图片
 * @param fileExtension 文件扩展名
 * @returns 是否为图片类型
 */
export function isImageFile(fileExtension?: string): boolean {
  if (!fileExtension) return false;

  const ext = fileExtension.toLowerCase().replace(/^\./, "");
  const imageTypes = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"];

  return imageTypes.includes(ext);
}

/**
 * 检查文件类型是否为视频
 * @param fileExtension 文件扩展名
 * @returns 是否为视频类型
 */
export function isVideoFile(fileExtension?: string): boolean {
  if (!fileExtension) return false;

  const ext = fileExtension.toLowerCase().replace(/^\./, "");
  const videoTypes = [
    "mp4",
    "avi",
    "mov",
    "mkv",
    "flv",
    "wmv",
    "mpg",
    "rmvb",
    "vob",
    "3gp",
  ];

  return videoTypes.includes(ext);
}

/**
 * 检查文件类型是否为音频
 * @param fileExtension 文件扩展名
 * @returns 是否为音频类型
 */
export function isAudioFile(fileExtension?: string): boolean {
  if (!fileExtension) return false;

  const ext = fileExtension.toLowerCase().replace(/^\./, "");
  const audioTypes = ["mp3", "aac", "m4a", "wav", "flac", "ogg"];

  return audioTypes.includes(ext);
}

/**
 * 根据 MIME 类型直接获取对应的文件图标组件
 * @param mimeType MIME 类型（如 "image/jpeg", "application/pdf" 等）
 * @returns React 组件构造函数
 */
export function getFileIconByMimeType(
  mimeType?: string
): React.ComponentType<React.SVGProps<SVGSVGElement>> {
  const extension = mimeType
    ? (mime.getExtension(mimeType.toLowerCase()) ?? undefined)
    : undefined;

  return getFileIcon(extension);
}

export const validateFileWithMagicBytes = async (file: File) => {
  const buffer = await file.slice(0, 8192).arrayBuffer();
  const bytes = new Uint8Array(buffer);
  const info = filetypeinfo(bytes);

  return info.some((item) => item.mime === file.type);
};
