/**
 * Markdown 文件构建时读取和 SSG 工具
 *
 * 严格版本：专注于 Markdown 到 HTML 的转换，构建时失败策略
 *
 * 特性：
 * - 🚀 构建时处理，运行时零开销
 * - 📦 支持 `export const dynamic = "force-static"` 配置
 * - 🔄 使用 React cache 避免重复处理
 * - 🛡️ 严格的构建时验证，任何错误都会导致构建失败
 * - 🧹 HTML 内容安全化处理
 */
import { promises as fs } from "fs";
import { marked } from "marked";
import path from "path";
import { cache } from "react";
import sanitizeHtml from "sanitize-html";

/**
 * 支持的协议文件类型
 */
export type AgreementType = "user-agreement" | "privacy-policy" | string;

/**
 * 构建时读取和处理 Markdown 文件
 *
 * 严格模式：任何错误都会导致构建失败
 * 使用 React cache 确保相同的文件只处理一次
 *
 * @param name - 协议文件类型
 * @returns Promise<string> 处理后的 HTML 内容
 * @throws 如果文件不存在或处理失败，直接抛出错误导致构建失败
 */
export const readMarkdownForSSG = cache(
  async (name: AgreementType): Promise<string> => {
    console.log(`📖 开始读取协议文件: ${name}`);

    // 构建文件路径
    const filePath = path.join(process.cwd(), "public/md", `${name}.md`);

    // 读取 Markdown 文件（如果失败会直接抛出错误）
    const markdown = await fs.readFile(filePath, "utf-8");

    // 转换为 HTML（如果失败会直接抛出错误）
    const rawHtml = marked(markdown);

    // 清理和安全化 HTML（如果失败会直接抛出错误）
    const cleanHtml = sanitizeHtml(rawHtml as string);

    console.log(`✅ 协议文件处理完成: ${name} (${cleanHtml.length} 字符)`);

    return cleanHtml;
  }
);

/**
 * 生成 generateStaticParams 所需的参数数组
 * 
 * 这个函数返回所有支持的协议类型，用于 SSG 静态路径生成
 */
export function generateAgreementStaticParams(): Array<{ agreement: AgreementType }> {
  const agreements: AgreementType[] = ['user-agreement', 'privacy-policy'];
  
  return agreements.map(agreement => ({ agreement }));
}

/**
 * 验证协议类型是否有效
 * 
 * @param type - 要验证的类型字符串
 * @returns 类型守卫，确认是否为有效的 AgreementType
 */
export function isValidAgreementType(type: string): type is AgreementType {
  return ['user-agreement', 'privacy-policy'].includes(type);
}
