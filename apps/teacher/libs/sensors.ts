import Sensors from "@/public/js/sensors/web/sensorsdata.d";
import * as Sentry from "@sentry/nextjs";

class SensorsManager {
  private sensors: null | typeof Sensors = null;
  private trackQueue: Array<[string, object | undefined, unknown]> = [];
  private profileQueue: Array<[object & { userID: number }, unknown]> = [];

  async init(platform: string) {
    if (typeof window === "undefined" || this.sensors) {
      return;
    }

    // 动态导入神策 SDK
    const { default: sensors } = await import(
      "@/public/js/sensors/web/sensorsdata.es6"
    );

    // 初始化配置
    sensors.init({
      server_url:
        "https://yhzx-pro.datasink.sensorsdata.cn/sa?project=default&token=39e6f5c78934680b",
      use_client_time: true,
      send_type: "beacon",
      show_log: process.env.NODE_ENV === "development",
    });

    // 注册公共属性
    sensors.registerPage({
      app_name: "教师端",
      platform,
    });

    sensors.quick("autoTrack");

    this.sensors = sensors;

    this.flushEventQueue();
  }

  // 安全的事件追踪
  track(eventName: string, payload?: object, c?: unknown) {
    if (!this.sensors) {
      return this.trackQueue.push([eventName, payload, c]);
    }

    try {
      requestIdleCallback(() => {
        this.sensors!.track(eventName, payload, c);
      });
    } catch (error) {
      Sentry.captureException(error, {
        level: "warning",
      });
    }
  }

  // 设置用户属性
  setProfile(props: object & { userID: number }, c?: unknown) {
    if (!this.sensors) {
      return this.profileQueue.push([props, c]);
    }

    // 用户关联
    this.sensors.login(props.userID.toString());
    // 设置用户属性
    this.sensors.setProfile(props, c);
  }

  // 处理初始化前的事件队列
  private flushEventQueue() {
    if (!this.sensors) {
      return;
    }

    while (this.profileQueue.length) {
      const payload = this.profileQueue.shift()!;

      this.setProfile(...payload);
    }

    while (this.trackQueue.length) {
      const payload = this.trackQueue.shift()!;

      this.track(...payload);
    }
  }
}

export const sensorsManager = new SensorsManager();
