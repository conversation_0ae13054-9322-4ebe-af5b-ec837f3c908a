export interface KnowledgeTree {
  bizTreeId: number;
  bizTreeName: string;
  bizTreeType: number;
  bizTreeVersion: string;
  phase: number;
  subject: number;
}

export type KnowledgeTreeList = KnowledgeTree[];

export interface BaseTreeInfo {
  baseTreeDetail: BaseTreeDetail;
  baseTreeId: number;
  baseTreeName: string;
  baseTreeVersion: string;
  bizTreeDetail: BizTreeDetail;
  bizTreeId: number;
  bizTreeName: string;
  bizTreeVersion: string;
  phase: number;
  subject: number;
}

export interface BaseTreeDetail {
  baseTreeNodeChildren: BaseTreeDetail[];
  baseTreeNodeId: number;
  baseTreeNodeName: string;
}

export interface BizTreeDetail {
  baseTreeNodeIds: string[];
  baseTreeNodeNameMap?: { [key: string]: any };
  bizTreeId: number;
  bizTreeName: string;
  bizTreeNodeChildren: BizTreeDetail[];
  bizTreeNodeId: number;
  bizTreeNodeName: string;
}
