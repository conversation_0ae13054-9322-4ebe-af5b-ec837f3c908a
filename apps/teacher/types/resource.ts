/**
 * 资源类型
 */
export type ResourceTabType = "my" | "public";

/**
 * 资源项目接口
 */
export interface ResourceItem {
  createdAt?: string;
  creatorId?: number;
  creatorName?: string;
  creatorSchoolId?: number;
  creatorSchoolName?: string;
  fileExtension?: string;
  fileSize?: number;
  fileType?: string;
  generateChannel?: string;
  grade: string;

  gradeIds: number[];
  objectKey?: string;
  originalFilename?: string;
  phase?: string;
  relationType?: number;
  resourceId: string;
  resourceName?: string;
  resourceType?: string;
  subject: string;
  subjectIds: number[];
  teacherResourceDbId: number;
  teacherResourceId?: string;
  updatedAt: number; // 秒
  userFilename: string;
  vid?: string;
}

/**
 * 筛选条件接口
 */
export interface ResourceFilters {
  grade: string[];
  source: string[];
  subject: string[];
}

/**
 * 资源列表查询参数
 */
export interface ResourceListParams {
  fileType?: string;
  grade?: number[];
  phase?: number[];
  resourceName?: string;
  subject?: number[];
  page: number;
  pageSize: number;
}

/**
 * 资源列表响应
 */
export interface ResourceListResponse {
  resources: ResourceItem[];
  total: number;
}

/**
 * 筛选选项
 */
export interface FilterOption {
  label: string;
  value: string;
  count?: number;
}

/**
 * 筛选配置
 */
export interface FilterConfig {
  grade: FilterOption[];
  source: FilterOption[];
  subject: FilterOption[];
}
