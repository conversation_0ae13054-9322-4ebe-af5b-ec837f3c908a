"use client";

import { PageHeader } from "@/components/PageHeader";
import { useApp } from "@/hooks";
import { ScrollArea } from "@/ui/scroll-area";
import { cn } from "@/utils";
import { getAppInfo } from "@repo/lib/utils/device";
import { useMount } from "ahooks";
import { ChevronRight } from "lucide-react";
import Link from "next/link";

// 关于页面的菜单项配置
const aboutMenuItems = [
  {
    id: "user-agreement",
    title: "用户协议",
    href: "/about/user-agreement",
  },
  {
    id: "privacy-policy",
    title: "隐私政策",
    href: "/about/privacy-policy",
  },
  {
    id: "personal-info",
    title: "个人信息清单",
    href: "/about/personal-info",
  },
  {
    id: "sdk-list",
    title: "SDK列表",
    href: "/about/sdk-list",
  },
  {
    id: "permissions",
    title: "申请使用权限",
    href: "/about/permissions",
  },
  {
    id: "license",
    title: "资质证照公示",
    href: "/about/license",
  },
];

export default function AboutPage() {
  const versionName = getAppInfo()?.versionName;
  const { setOpen } = useApp();

  useMount(() => {
    setOpen(false);
  });

  return (
    <div className="flex h-full flex-col">
      {/* 页面头部 */}
      <PageHeader needBack className="text-gray-1 gap-2 text-xl font-medium">
        关于
      </PageHeader>

      <ScrollArea className="flex-1 overflow-hidden">
        <div className="mx-6 overflow-hidden rounded-lg bg-white shadow-sm">
          {aboutMenuItems.map((item, index) => (
            <Link
              key={item.id}
              href={item.href}
              prefetch={true}
              className={cn(
                "flex cursor-pointer items-center justify-between px-4 py-4",
                "transition-colors hover:bg-gray-50 active:bg-gray-100",
                "touch-manipulation", // 移动端优化
                index !== aboutMenuItems.length - 1 &&
                  "border-b border-gray-100"
              )}
            >
              <span className="select-none text-base text-gray-900">
                {item.title}
              </span>
              <ChevronRight className="h-5 w-5 flex-shrink-0 text-gray-400" />
            </Link>
          ))}
        </div>

        {/* 底部版本信息 */}
        <div className="flex items-center justify-between px-6 py-6 text-sm text-gray-400">
          <span>{versionName && `version：${versionName}`}</span>
          <span>
            ICP备案号：
            <span
              className="cursor-pointer text-blue-500"
              onClick={() => {
                window.open("https://beian.miit.gov.cn/#/home", "_blank");
              }}
            >
              粤ICP备2025455539号-1
            </span>
          </span>
        </div>
      </ScrollArea>
    </div>
  );
}
