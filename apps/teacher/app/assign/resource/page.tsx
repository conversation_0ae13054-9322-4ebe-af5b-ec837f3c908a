"use client";

import AssignResourcesLayout from "@/components/assign/AssignResourcesLayout";
import { LOCALSTORAGE_RESOURCE_CAR_KEY } from "@/configs/storage";
import { useTchNavigation } from "@/hooks/useTchNavigation";
import { ResourceItem } from "@/types/resource";
import useLocalStorageState from "ahooks/lib/useLocalStorageState";
import { useCallback } from "react";

export default function AssignResourcePage() {
  const [resourcesCar, setResourcesCar] = useLocalStorageState<
    ResourceItem[] | undefined
  >(LOCALSTORAGE_RESOURCE_CAR_KEY, {
    listenStorageChange: true,
  });

  const { gotoTchAssignPage, gotoBack } = useTchNavigation();

  const handleConfirmAssign = useCallback(() => {
    setResourcesCar([]);
    gotoTchAssignPage(true);
  }, [setResourcesCar, gotoTchAssignPage]);

  return (
    <AssignResourcesLayout
      onBack={gotoBack}
      resources={resourcesCar || []}
      onConfirmAssign={handleConfirmAssign}
    />
  );
}
