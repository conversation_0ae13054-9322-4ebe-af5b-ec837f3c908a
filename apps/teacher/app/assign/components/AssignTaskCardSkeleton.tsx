"use client";

import { ScrollArea } from "@/ui/scroll-area";
import { Skeleton } from "@/ui/skeleton";
export default function AssignTaskCardSkeleton() {
  return (
    <ScrollArea orientation="vertical" className="flex-1 overflow-hidden">
      <div className="grid grid-cols-4 gap-4">
        {/* 第一行 - 大卡片 */}
        <Skeleton className="col-span-1 h-6" />
        <Skeleton className="col-span-1 h-6" />
        <Skeleton className="col-span-1 h-6" />
        <Skeleton className="col-span-1 h-6" />

        {/* 第二行 - 小标签 */}
        <Skeleton className="w-19.5 col-span-1 h-4" />
        <Skeleton className="w-19.5 col-span-1 h-4" />
        <Skeleton className="w-19.5 col-span-1 h-4" />
        <Skeleton className="w-19.5 col-span-1 h-4" />

        {/* 内容卡片 */}
        {Array.from({ length: 4 }).map((_, index) => (
          <Skeleton key={`row3-${index}`} className="h-25.75 col-span-1" />
        ))}
        {Array.from({ length: 4 }).map((_, index) => (
          <Skeleton key={`row4-${index}`} className="h-25.75 col-span-1" />
        ))}
        {Array.from({ length: 4 }).map((_, index) => (
          <Skeleton key={`row4-${index}`} className="h-25.75 col-span-1" />
        ))}
      </div>
    </ScrollArea>
  );
}
