"use client";
import { AssignCategoryCard } from "@/components/assign/assign-category-card";
import { AssignEmpty } from "@/components/assign/assign-empty";
import { AssignSubjectSelect } from "@/components/assign/assign-subject-select";
import { PageHeader } from "@/components/PageHeader";
import { useApp } from "@/hooks";
import { useUmeng } from "@/hooks/useUmeng";
import ArrowRight from "@/public/icons/ic_fold.svg";
import { getTaskLatestAssign } from "@/services/assign";
import { Homework } from "@/types/homeWork";
import { ScrollArea } from "@/ui/scroll-area";

import TchLink from "@/components/TchLink";
import {
  getAssignCategories,
  sortLatestAssignTasks,
  umeng,
  UmengAssignAction,
  UmengAssignPageName,
  UmengCategory,
} from "@/utils";
import { cn } from "@/utils/utils";
import { useComputed } from "@preact-signals/safe-react";
import { useMount, useRequest } from "ahooks";
import { useContext } from "react";
import AssignSkeleton from "./components/AssignSkeleton";
import AssignTaskCard from "./components/AssignTaskCard";
import AssignTaskCardSkeleton from "./components/AssignTaskCardSkeleton";
import { AssignContext } from "./store";

export default function AssignPage() {
  useUmeng(UmengCategory.ASSIGN, UmengAssignPageName.TASK_LIST);
  const { getRoleSummary, setOpen, userInfo } = useApp();
  // const { gotoTaskCreatePage, gotoHomeworkPage } = useTchNavigation();
  const { currentSubject, setCurrentSubject, subjectTaskTypes } =
    useContext(AssignContext);
  const assignCategories = useComputed(() => {
    return getAssignCategories(currentSubject.value.taskTypes);
  });

  const onCategoryClick = (id: string, umengTag: string) => {
    umeng.trackEvent(
      UmengCategory.ASSIGN,
      UmengAssignAction.TASK_LIST_ADD_TASK_CLICK,
      {
        type: umengTag,
        subject: currentSubject.value.subjectName,
        job: getRoleSummary(),
      }
    );
  };

  const viewAllTasks = () => {
    umeng.trackEvent(
      UmengCategory.ASSIGN,
      UmengAssignAction.TASK_LIST_MORE_CLICK,
      {}
    );
    // gotoHomeworkPage({
    //   source: "assign",
    //   subjectId: currentSubject.value.subjectKey,
    // });
  };

  const { data: latestAssignTasks, loading: isLatestAssignTasksLoading } =
    useRequest(
      async () => {
        const res = await getTaskLatestAssign(currentSubject.value.subjectKey);
        const tasks: Homework[] = res?.tasks ?? [];
        const sortedTasks = sortLatestAssignTasks(tasks);

        return sortedTasks.filter((e) => Boolean(e));
      },
      {
        refreshDeps: [currentSubject.value.subjectKey],
      }
    );

  // 可能是个用户引导，但没实装过功能
  // const playGuide = useSignal<boolean>(false);
  // useSignalEffect(() => {
  //   if (playGuide.value) {
  //     umeng.trackEvent(
  //       UmengCategory.ASSIGN,
  //       UmengAssignAction.TASK_LIST_NEW_GUIDE_CLICK,
  //       {}
  //     );
  //   }
  // });

  useMount(() => {
    setOpen(true);
  });

  if (!userInfo) {
    return <AssignSkeleton />;
  }

  return (
    <div className="flex h-full flex-col overflow-hidden bg-[#F5FAFF] px-6">
      <PageHeader className="pl-0">
        {/* 当前学科的选择器 */}
        <AssignSubjectSelect
          onChange={setCurrentSubject}
          value={currentSubject}
          taskTypes={subjectTaskTypes}
        />
      </PageHeader>

      <div
        className={cn("flex-0 mb-8 grid gap-4")}
        style={{
          gridTemplateColumns: `repeat(${assignCategories.value.length}, minmax(0, 1fr))`,
        }}
      >
        {assignCategories.value.map((category) =>
          category.enable ? (
            <TchLink
              href={`/assign/${currentSubject.value.subjectKey}/${category.id}`}
              key={category.id}
              className="relative"
              loadingIconClassName="absolute top-1/2 left-1/2 h-1/2 w-1/2 -translate-x-1/2 -translate-y-1/2"
              onNavigate={() => onCategoryClick(category.id, category.umengTag)}
            >
              <AssignCategoryCard
                {...category}
                enable={category.enable}
                className={cn("col-span-1")}
              />
            </TchLink>
          ) : (
            <AssignCategoryCard
              {...category}
              key={category.id}
              enable={category.enable}
              className={cn("col-span-1")}
            />
          )
        )}
      </div>

      <div className="flex-0 mb-5 flex items-center justify-between">
        <h2 className="text-gray-1 text-xl font-medium leading-normal tracking-wider">
          最近布置
        </h2>
        {!isLatestAssignTasksLoading && latestAssignTasks?.length !== 0 ? (
          <TchLink
            href="/homework"
            className="text-gray-4 flex h-7 min-w-16 items-center justify-center rounded-full border border-slate-200 bg-transparent px-3 text-xs font-medium hover:bg-slate-50 active:bg-slate-200"
            onClick={viewAllTasks}
          >
            <span style={{ lineHeight: "normal" }}>全部</span>
            <ArrowRight className="ml-1 h-3 w-2" />
          </TchLink>
        ) : null}
      </div>
      {isLatestAssignTasksLoading ? (
        <AssignTaskCardSkeleton />
      ) : latestAssignTasks?.length !== 0 ? (
        <ScrollArea orientation="vertical" className="flex-1 overflow-hidden">
          <div
            className="grid grid-cols-2 gap-4"
            style={{
              gridTemplateColumns: `repeat(${4}, minmax(0, 1fr))`,
            }}
          >
            {latestAssignTasks?.map((task, index) =>
              task ? (
                <AssignTaskCard key={task.taskId} taskData={task} />
              ) : (
                <div key={`${index}-empty`} />
              )
            )}
          </div>
        </ScrollArea>
      ) : (
        <AssignEmpty
          type="homepage"
          className="flex-1 border-none bg-gradient-to-b from-slate-50 to-white"
          content={
            <div className="space-y-1 text-center leading-normal">
              <div className="text-gray-2 text-center text-base">
                当前暂无布置内容
              </div>
              <div className="text-gray-4 text-center text-xs">
                快去布置一项任务吧
              </div>
            </div>
          }
        />
      )}
    </div>
  );
}
