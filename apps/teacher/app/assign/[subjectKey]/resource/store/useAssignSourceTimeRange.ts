import { DateTimeChangePayload } from "@/components/assign/assign-date-picker";
import { TargetJobClass } from "@/types";
import { AssignCourseTimeRange } from "@/types/assign/course";
import { Signal, useSignal, useSignalEffect } from "@preact-signals/safe-react";
import { endOfDay } from "date-fns";
import { useCallback } from "react";

export function getRecommendEndTime(startTime: number) {
    return endOfDay(startTime).getTime();
}

export default function useAssignTimeRange(checkedTargetClassList: Signal<TargetJobClass[]>) {
    const assignTimeRanges = useSignal<AssignCourseTimeRange[]>([])

    // 按班级分时间
    const calcAssignTimeRange = useCallback(function () {
        const pendingAssignTimeRanges: AssignCourseTimeRange[] = [];
        const checkedClasses = checkedTargetClassList.value;

        for (const cls of checkedClasses) {
            pendingAssignTimeRanges.push({
                classInfo: cls,
                isImmediate: true,
                isRecommend: true,
                startTime: Date.now(),
                // PM: 推荐时间的策略：当天晚上23:59:59
                endTime: getRecommendEndTime(Date.now())
            })
        }

        assignTimeRanges.value = pendingAssignTimeRanges;

        return pendingAssignTimeRanges;
    }, [assignTimeRanges, checkedTargetClassList])

    useSignalEffect(() => {
        // NOTE：需要跟PM battle下这里了，都没了会怎么样，这是个交互Bug
        calcAssignTimeRange()
    })

    const updateAssignTimeRange = useCallback(function (payload: DateTimeChangePayload) {
        const { type, isStart, index, customTimestamp } = payload;

        const assignTimeRange = assignTimeRanges.value[index];
        if (!assignTimeRange) {
            return;
        }

        if (type === "immediate") {
            assignTimeRange.isImmediate = type === "immediate";
            assignTimeRange.startTime = Date.now();
        } else if (type === "recommend") {
            assignTimeRange.isRecommend = true;
            assignTimeRange.endTime = getRecommendEndTime(Date.now())
        } else if (type === "custom") {
            if (isStart) {
                assignTimeRange.isImmediate = false;
                assignTimeRange.startTime = customTimestamp;

                // 如果结束时间小于开始时间，则需要重新计算结束时间
                if (assignTimeRange.endTime <= customTimestamp) {
                    assignTimeRange.isRecommend = true;
                    assignTimeRange.endTime = getRecommendEndTime(Date.now())
                }
            } else {
                assignTimeRange.isRecommend = false;
                assignTimeRange.endTime = customTimestamp;
            }
        }

        assignTimeRanges.value[index] = assignTimeRange

        assignTimeRanges.value = [...assignTimeRanges.value];
    }, [assignTimeRanges])

    return {
        assignTimeRanges,
        calcAssignTimeRange,
        updateAssignTimeRange,
    }
}