import { SubjectKey } from "@/app/assign/type";
import {
  AssignResourceTypeEnum,
  AssignTaskTypeEnum,
  LOCALSTORAGE_RESOURCE_CAR_KEY,
} from "@/configs";
import useSubjectInfosByUserInfo from "@/hooks/useSubjectInfosByUserInfo";
import { useTchNavigation } from "@/hooks/useTchNavigation";
import { assignTask } from "@/services/assign";
import { AssignResourceParams } from "@/types/assign/course";
import { ResourceItem } from "@/types/resource";
import { toast } from "@/ui/toast";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { useLocalStorageState, useRequest } from "ahooks";
import { useParams } from "next/navigation";
import { useCallback } from "react";
import useCheckAssignTarget from "../../store/useCheckAssignTarget";
import useGetResourceStudentGroupsParams from "../hooks/useGetResourceStudentGroupsParams";
import useAssignSourceTimeRange from "./useAssignSourceTimeRange";

function genInitialAssignName(subjectName: string) {
  const date = new Date();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${month}月${day}日${subjectName}资源作业`;
}

export default function useAssignResourceForm() {
  const { subjectKey } = useParams();
  const subject = useComputed<SubjectKey>(() => Number(subjectKey));
  const { subjectInfos } = useSubjectInfosByUserInfo();

  const { checkedClasses, checkedClassStudentList, toggleClass } =
    useCheckAssignTarget(subject.value);

  // 任务留言
  const teacherComment = useSignal<string>("");
  const setTeacherComment = useCallback(
    (comment: string) => {
      teacherComment.value = comment;
    },
    [teacherComment]
  );

  const { gotoTchAssignPage } = useTchNavigation();

  // 任务名称
  const defaultAssignName = useComputed<string>(() => {
    return genInitialAssignName(
      subjectInfos.value.find((item) => item.subjectKey === Number(subjectKey))
        ?.subjectName || ""
    );
  });

  const assignName = useSignal<string>(defaultAssignName.value);
  const { assignTimeRanges, updateAssignTimeRange } =
    useAssignSourceTimeRange(checkedClasses);

  const setAssignName = useCallback(
    (name: string) => {
      assignName.value = name;
    },
    [assignName]
  );

  const { getResourceStudentGroupsParams } =
    useGetResourceStudentGroupsParams(assignTimeRanges);

  const isSyncResourceCar = useSignal(false);

  // TODO：需要优化的State老鼠屎：
  const [resourcesCar, setResourcesCar] = useLocalStorageState<
    ResourceItem[] | undefined
  >(LOCALSTORAGE_RESOURCE_CAR_KEY);

  const localResources = useSignal<ResourceItem[]>([]);

  const setIsSyncResourceCar = useCallback(
    (newVal: boolean) => {
      isSyncResourceCar.value = newVal;
      if (newVal) {
        localResources.value = resourcesCar || [];
      }
    },
    [isSyncResourceCar, localResources, resourcesCar]
  );
  const setResources = useCallback(
    (newResources: ResourceItem[]) => {
      if (isSyncResourceCar.value) {
        setResourcesCar(newResources);
      }
      localResources.value = newResources;
    },
    [isSyncResourceCar, localResources, setResourcesCar]
  );

  const resources = useComputed<ResourceItem[]>(() => {
    return localResources.value;
  });

  // 提交任务
  const { run: confirm, loading: confirmLoading } = useRequest(
    async () => {
      const params: AssignResourceParams = {
        subject: subject.value,
        taskType: AssignTaskTypeEnum.TASK_TYPE_RESOURCE,
        taskName: assignName.value,
        teacherComment: teacherComment.value,
        // TODO: check type
        resources: [
          {
            resourceId: resources.value[0].resourceId!,
            resourceType: AssignResourceTypeEnum.RESOURCE_RESOURCE,
            resourceSubIDs: resources.value.map((e) => e.resourceId!),
          },
        ],

        studentGroups: getResourceStudentGroupsParams(),
      };
      await assignTask(params);
      toast.success("布置成功");
      if (isSyncResourceCar.value) {
        setResourcesCar(undefined);
      }

      return new Promise((resolve) => {
        setTimeout(() => {
          gotoTchAssignPage(true);
          resolve(true);
        }, 2000);
      });
    },
    {
      manual: true,
      cacheKey: `assign-resource-confirm-${subject.value}`,
    }
  );

  return {
    subject,
    resources,
    setResources,
    isSyncResourceCar,
    setIsSyncResourceCar,
    checkedClasses,
    checkedClassStudentList,
    assignName,
    assignTimeRanges,
    toggleClass,
    setAssignName,
    updateAssignTimeRange,
    teacherComment,
    setTeacherComment,

    confirm,
    confirmLoading,
    resourcesCar,
  };
}
