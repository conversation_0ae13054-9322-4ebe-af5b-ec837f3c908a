import { useClassListBySubject } from "@/app/assign/store/useClassListBySubject";
import { createContext, useContext } from "react";
import useAssignResourceForm from "./useAssignResourceForm";
import { useAssignResourceStep } from "./useStep";

type AssignResourceContextType = ReturnType<typeof useAssignResourceStore>;

export function useAssignResourceStore() {
  const { currentAssignStep, goToSelectTarget, goToSetTime } =
    useAssignResourceStep();

  const {
    resources,
    subject,
    checkedClasses,
    checkedClassStudentList,
    confirm,
    toggleClass,
    updateAssignTimeRange,
    assignTimeRanges,
    assignName,
    setAssignName,
    confirmLoading,
    teacherComment,
    setTeacherComment,
    setResources,
    isSyncResourceCar,
    setIsSyncResourceCar,
    resourcesCar,
  } = useAssignResourceForm();

  const classList = useClassListBySubject(subject.value);

  return {
    classList,

    currentAssignStep,
    goToSelectTarget,
    goToSetTime,

    resources,
    setResources,
    subject,
    checkedClasses,
    checkedClassStudentList,
    assignName,
    assignTimeRanges,
    teacherComment,
    setTeacherComment,
    confirm,
    confirmLoading,
    toggleClass,
    updateAssignTimeRange,
    setAssignName,

    isSyncResourceCar,
    setIsSyncResourceCar,
    resourcesCar,
  };
}

export const AssignResourceContext =
  createContext<AssignResourceContextType | null>(null);

export const AssignResourceProvider =
  AssignResourceContext.Provider as React.Provider<AssignResourceContextType>;

export function useAssignResourceContext() {
  const state = useContext(AssignResourceContext);

  if (!state) {
    throw new Error("AssignResourceContext is not found");
  }

  return state;
}
