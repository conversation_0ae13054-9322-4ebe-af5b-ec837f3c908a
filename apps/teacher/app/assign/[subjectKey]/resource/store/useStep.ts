import { useSignal } from "@preact-signals/safe-react";
import { AssignResourceStep } from "../type";

export function useAssignResourceStep() {
  const currentAssignStep = useSignal<AssignResourceStep>("select-target");

  function setCurrentAssignStep(step: AssignResourceStep) {
    currentAssignStep.value = step;
  }

  function goToSelectTarget() {
    setCurrentAssignStep("select-target");
  }

  function goToSetTime() {
    setCurrentAssignStep("set-time");
  }

  return {
    currentAssignStep,
    goToSelectTarget,
    goToSetTime,
  };
}
