"use client";

import { AssignCancelAlert } from "@/components/assign/assign-cancel-alert";
import { AssignHeader } from "@/components/assign/assign-header";
import { AssignHeading } from "@/components/assign/assign-heading";
import { AssignPageContainer } from "@/components/assign/assign-page-container";
import { AssignTargets } from "@/components/assign/assign-targets";
import { useTchNavigation } from "@/hooks/useTchNavigation";
import { TargetJobClass } from "@/types";
import { AlertDialog } from "@/ui/alertDialog";
import { ScrollArea } from "@/ui/scroll-area";
import { useSignal } from "@preact-signals/safe-react";
import { useCallback } from "react";
import AssignResourceFooter from "./_components/AssignResourceFooter";
import AssignResourceSelect from "./_components/AssignResourceSelect";
import AssignResourceSetTime from "./_components/AssignResourceSetTime";
import { useAssignResourceContext } from "./store";

export default function AssignResourcePage() {
  const {
    currentAssignStep,
    goToSelectTarget,
    resources,
    classList,
    checkedClasses,
    toggleClass,
    checkedClassStudentList,
    setIsSyncResourceCar,
    resourcesCar,
  } = useAssignResourceContext();
  const { gotoBack } = useTchNavigation();

  const cancelAssignOpen = useSignal(false);

  const onBack = () => {
    if (currentAssignStep.peek() !== "select-target") {
      goToSelectTarget();
      return;
    }

    if (resources.value.length > 0) {
      cancelAssignOpen.value = true;
    } else {
      gotoBack();
    }
  };

  const isFirstTimeToToggleClass = useSignal(true);
  const showConfirmResourceCar = useSignal(false);

  const handleToggleClass = useCallback(
    (jobClass: TargetJobClass) => {
      if (isFirstTimeToToggleClass.value) {
        isFirstTimeToToggleClass.value = false;
        if (resourcesCar && resourcesCar?.length > 0) {
          showConfirmResourceCar.value = true;
        }
      }
      toggleClass(jobClass);
    },
    [
      isFirstTimeToToggleClass,
      resourcesCar,
      showConfirmResourceCar,
      toggleClass,
    ]
  );

  return (
    <AssignPageContainer>
      <AssignHeader
        className="flex-shrink-0 flex-grow-0"
        title="布置资源任务"
        onBack={onBack}
      />
      <div className="mb-17 flex h-full overflow-hidden">
        {currentAssignStep.value === "select-target" ? (
          <>
            <ScrollArea
              orientation="vertical"
              className="flex-1 overflow-hidden"
            >
              <div className="flex h-full w-full flex-1 flex-col pl-6 pr-4">
                {/* 1.选择布置对象 */}
                <div className="pt-2">
                  <AssignHeading content="1.选择布置对象" className="mb-4" />
                  <AssignTargets
                    classList={classList}
                    checkedClasses={checkedClasses}
                    checkedClassStudentList={checkedClassStudentList}
                    toggleClass={handleToggleClass}
                  />
                </div>

                {/* 2.选择任务内容 */}
                <div className="mt-8 pb-4">
                  <AssignHeading content="2.选择任务内容" />
                </div>

                <AssignResourceSelect />
              </div>
            </ScrollArea>
          </>
        ) : null}
        {currentAssignStep.value === "set-time" ? (
          <div className="flex h-full w-full flex-1 flex-col pl-6 pr-4">
            <AssignHeading content="3.任务设置" className="mb-4" />
            <AssignResourceSetTime />
          </div>
        ) : null}
        {/* Footer */}
        <AssignResourceFooter onBack={onBack} />
      </div>

      <AssignCancelAlert
        open={cancelAssignOpen.value}
        onCancel={() => {
          cancelAssignOpen.value = false;
        }}
        onOk={() => {
          cancelAssignOpen.value = false;
          gotoBack();
        }}
      />
      <AlertDialog
        open={showConfirmResourceCar.value}
        title="是否同步？"
        description="识别到“资源篮”中已有资源，是否为您同步到当前任务？"
        onCancel={() => {
          showConfirmResourceCar.value = false;
        }}
        onOk={() => {
          setIsSyncResourceCar(true);
          showConfirmResourceCar.value = false;
        }}
        variant="warning"
      />
    </AssignPageContainer>
  );
}
