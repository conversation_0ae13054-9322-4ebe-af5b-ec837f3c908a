import { AssignCard } from "@/components/assign/assign-card";
import { ResourceTable } from "@/components/resource/ResourceTable";
import { useAssignResourceContext } from "../../store";
import AssignResourceSkeleton from "./AssignResourceSkeleton";

export default function AssignResourceSelect() {
  const { checkedClasses, resources, setResources } =
    useAssignResourceContext();

  return (
    <AssignCard className="flex h-full w-full gap-2">
      {checkedClasses.value.length > 0 ? (
        <ResourceTable
          joinedResources={resources.value}
          setJoinedResources={setResources}
        />
      ) : (
        <AssignResourceSkeleton />
      )}
    </AssignCard>
  );
}
