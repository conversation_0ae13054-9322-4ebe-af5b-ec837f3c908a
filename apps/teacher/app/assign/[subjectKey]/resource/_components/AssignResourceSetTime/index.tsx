import { useApp } from "@/hooks/useApp";
import { AssignCourseTimeRange } from "@/types/assign/course";
import { ScrollArea } from "@/ui/scroll-area";
import { useAssignResourceContext } from "../../store";
import { getRecommendEndTime } from "../../store/useAssignSourceTimeRange";
import AssignSetTimePure from "./AssignSetTimePure";

export default function AssignResourceSetTime() {
  const { statusBarHeight } = useApp();

  const {
    assignName,
    assignTimeRanges,
    updateAssignTimeRange,
    setAssignName,
    teacherComment,
    setTeacherComment,
  } = useAssignResourceContext();

  const handleGetRecommendEndTimeNumber = (
    timeRange: AssignCourseTimeRange
  ) => {
    return getRecommendEndTime(timeRange.startTime);
  };

  return (
    <ScrollArea
      className="h-full w-full"
      style={{
        height: `calc(100vh - ${statusBarHeight}px - 6.875rem)`,
      }}
    >
      <AssignSetTimePure
        assignName={assignName.value}
        assignTimeRanges={assignTimeRanges.value}
        onDateTimeChange={updateAssignTimeRange}
        onAssignNameChange={setAssignName}
        getRecommendEndTime={handleGetRecommendEndTimeNumber}
        teacherComment={teacherComment.value}
        onTeacherCommentChange={setTeacherComment}
      />
    </ScrollArea>
  );
}
