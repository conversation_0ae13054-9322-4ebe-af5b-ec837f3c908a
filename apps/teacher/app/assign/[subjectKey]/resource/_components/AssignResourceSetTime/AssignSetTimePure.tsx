import { AssignCard } from "@/components/assign/assign-card";
import {
  AssignDatePicker,
  DateTimeChangePayload,
  isInvalidTimeRange,
} from "@/components/assign/assign-date-picker";
import { TchCounterInput } from "@/components/common/tch-counter-input";
import { AssignCourseTimeRange } from "@/types/assign/course";
import { Textarea } from "@/ui/textarea";
import { cn } from "@/utils/utils";
import { Fragment } from "react";

export type AssignSetTimePureProps = {
  assignName: string;
  assignTimeRanges: AssignCourseTimeRange[];
  onDateTimeChange: (timeRange: DateTimeChangePayload) => void;
  onAssignNameChange: (assignName: string) => void;
  getRecommendEndTime: (timeRange: AssignCourseTimeRange) => number;
  teacherComment: string;
  onTeacherCommentChange: (comment: string) => void;
};

export default function AssignSetTimePure({
  assignName,
  assignTimeRanges,
  onDateTimeChange,
  onAssignNameChange,
  getRecommendEndTime,
  teacherComment,
  onTeacherCommentChange,
}: AssignSetTimePureProps) {
  return (
    <AssignCard>
      <div className="mb-4 flex items-center gap-6">
        <span className="w-17 text-right text-sm leading-normal text-slate-600">
          <span className="text-danger-2 mr-1 align-middle">*</span>
          <span>任务名称</span>
        </span>
        <TchCounterInput
          className="w-136 h-9"
          value={assignName}
          maxLength={30}
          onChange={(e) => onAssignNameChange(e.target.value)}
        />
      </div>
      <div className="flex items-start gap-6">
        <span className="w-17 text-right text-sm leading-normal text-slate-600">
          <span className="text-danger-2 mr-1 align-middle">*</span>
          <span>任务时间</span>
        </span>

        <AssignCard className="bg-fill-gray-2 mb-4 flex-1 rounded-xl py-4 outline-none">
          <div className="grid grid-cols-[minmax(4.5rem,max-content)_1fr_1fr] items-start gap-x-3 py-2">
            <div className="col-span-1 pr-3"></div>
            <div className="text-gray-4 col-span-1 text-xs font-normal leading-none">
              发布时间
            </div>
            <div className="text-gray-4 col-span-1 text-xs font-normal leading-none">
              要求完成时间
            </div>
            {assignTimeRanges.map((cls, index) => (
              <Fragment key={index}>
                <div
                  className={cn(
                    "min-h-7.75 col-span-1 mt-3 flex items-center pr-3 text-sm font-normal leading-tight text-slate-600",
                    index === 0 && "mt-2"
                  )}
                >
                  {cls.classInfo.jobGradeName + cls.classInfo.name}
                </div>

                <div className={cn("col-span-1 mt-3", index === 0 && "mt-2")}>
                  <div className="h-7.75">
                    <AssignDatePicker
                      className="w-full"
                      isStart={true}
                      timeRange={cls}
                      timeRangeIndex={index}
                      onDateTimeChange={onDateTimeChange}
                    />
                  </div>
                  {isInvalidTimeRange(cls) && (
                    <div className="text-danger-2 mt-1 text-xs leading-normal">
                      要求完成时间不允许早于或等于发布时间
                    </div>
                  )}
                </div>

                <div className={cn("col-span-1 mt-3", index === 0 && "mt-2")}>
                  <div className="h-7.75">
                    <AssignDatePicker
                      className="w-full"
                      isStart={false}
                      timeRange={cls}
                      timeRangeIndex={index}
                      recommendEndTime={getRecommendEndTime(cls)}
                      onDateTimeChange={onDateTimeChange}
                    />
                  </div>
                </div>
              </Fragment>
            ))}
          </div>
        </AssignCard>
      </div>

      <div className="flex items-start gap-6">
        <span className="w-17 text-right text-sm leading-normal text-slate-600">
          任务留言
        </span>
        <Textarea
          containerClassName="w-full"
          className="border-line-3 h-27 flex-1"
          value={teacherComment}
          onChange={(e) => onTeacherCommentChange(e.target.value)}
          placeholder="说明您对任务的要求，留言将在学生端显示"
          maxLength={50}
        />
      </div>
    </AssignCard>
  );
}
