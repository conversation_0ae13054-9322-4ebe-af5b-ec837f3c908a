import { useComputed } from "@preact-signals/safe-react";
import { useCallback } from "react";
import { useAssignResourceContext } from "../../store";
import AssignResourceFooterPure from "./AssignResourceFooterPure";

export default function AssignResourceFooter({
  onBack,
}: {
  onBack: () => void;
}) {
  const {
    resources,
    checkedClasses,
    goToSetTime,
    currentAssignStep,
    confirm,
    confirmLoading,
  } = useAssignResourceContext();

  const onCancel = useCallback(() => {
    onBack();
  }, [onBack]);

  const onNext = useCallback(() => {
    if (currentAssignStep.value === "select-target") {
      goToSetTime();
    } else if (currentAssignStep.value === "set-time") {
      confirm();
    }
  }, [currentAssignStep, goToSetTime, confirm]);

  const nextButtonContent = useComputed(() => {
    if (currentAssignStep.value === "select-target") {
      return "下一步";
    } else if (currentAssignStep.value === "set-time") {
      return "完成并布置";
    }
  });

  return (
    <AssignResourceFooterPure
      resources={resources.value}
      nextButtonContent={nextButtonContent.value}
      onCancel={onCancel}
      onNext={onNext}
      nextButtonDisabled={
        !resources.value.length ||
        !checkedClasses.value.length ||
        confirmLoading
      }
    />
  );
}
