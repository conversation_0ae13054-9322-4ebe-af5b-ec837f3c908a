import { AssignButton } from "@/components/assign/assign-button";
import { ResourceItem } from "@/types/resource";
import { cn } from "@/utils/utils";
import SelectedAssignResource from "./SelectedAssignResource";

export interface AssignResourceFooterPureProps {
  style?: React.CSSProperties;
  className?: string;

  resources: ResourceItem[];
  nextButtonDisabled?: boolean;
  nextButtonContent?: string;
  onCancel: () => void;
  onNext: () => void;
  setResources?: (resources: ResourceItem[]) => void;
}

export default function AssignResourceFooterPure({
  style,
  className,
  resources,
  setResources,
  nextButtonDisabled = false,
  nextButtonContent = "下一步",
  onCancel,
  onNext,
}: AssignResourceFooterPureProps) {
  return (
    <div
      className={cn(
        "h-17 fixed bottom-0 left-0 flex w-full select-none items-center justify-end gap-3 border-t border-slate-200 bg-white px-8 shadow-[0px_8px_32px_0px_rgba(16,18,25,0.10)]",
        className
      )}
      style={style}
    >
      <SelectedAssignResource
        resources={resources}
        setResources={setResources}
      />
      <AssignButton
        variant="outline"
        className="outline-line-1 px-5"
        onClick={onCancel}
      >
        取消布置
      </AssignButton>
      <AssignButton
        className="px-5"
        disabled={nextButtonDisabled}
        onClick={onNext}
      >
        {nextButtonContent}
      </AssignButton>
    </div>
  );
}
