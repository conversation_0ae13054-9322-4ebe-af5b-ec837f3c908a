import { AssignEmpty } from "@/components/assign/assign-empty";
import { ResourceItem } from "@/types/resource";
import { Button } from "@/ui/tch-button";
import { TchDrawer } from "@/ui/tch-drawer";
import { cn } from "@/utils/utils";
import { useSignal } from "@preact-signals/safe-react";
import { memo, useCallback } from "react";
import ResourceItemCard from "./ResourceItemCard";

export default memo(function SelectedAssignResource({
  className,
  style,
  resources,
  setResources,
}: {
  className?: string;
  style?: React.CSSProperties;
  resources: ResourceItem[];
  setResources?: (resources: ResourceItem[]) => void;
}) {
  const open = useSignal(false);

  const resourcesCount = resources.length;

  const onRemove = useCallback(
    (resource: ResourceItem) => {
      setResources?.(
        resources.filter((item) => item.resourceId !== resource.resourceId)
      );
    },
    [resources, setResources]
  );

  return (
    <>
      <Button
        type={resourcesCount > 0 ? "outline" : "default"}
        size="lg"
        radius="full"
        disabled={resourcesCount === 0}
        onClick={() => {
          open.value = true;
        }}
        className={cn("", className)}
        style={style}
      >
        已加入 ({resourcesCount})
      </Button>
      <TchDrawer
        open={open.value}
        onOpenChange={(v) => (open.value = v)}
        title={`已加入 (${resourcesCount})`}
      >
        {resourcesCount > 0 ? (
          <div className="flex flex-col space-y-4">
            <div className="text-gray-2 text-base">
              已加入资源任务（{resourcesCount}）
            </div>
            {/* TODO: 加入资源列表的单项展示 */}
            {resources.map((item) => (
              <ResourceItemCard
                key={item.resourceId}
                data={item}
                onRemove={setResources ? onRemove : undefined}
              />
            ))}
          </div>
        ) : (
          <AssignEmpty
            type="course"
            className="flex-1 rounded-2xl border-none"
            content="还没有加入任何资源哦~"
          />
        )}
      </TchDrawer>
    </>
  );
});
