import { ResourceItem } from "@/types/resource";
import { getFileIcon } from "@/utils/file";
import { memo } from "react";

export default memo(function ResourceItemCard({
  data,
  onRemove,
}: {
  data: ResourceItem;
  onRemove?: (resource: ResourceItem) => void;
}) {
  const FileIcon = getFileIcon(data.fileExtension);
  return (
    <div className="min-h-18 flex items-center justify-between rounded-2xl bg-white p-5">
      <div className="flex items-center gap-3">
        <FileIcon className="flex-shrink-0" />
        <div>{data.resourceName}</div>
      </div>
      {onRemove ? (
        <button
          onClick={() => {
            onRemove(data);
          }}
          className="text-danger-1 hover:text-danger-1 shrink-0 cursor-pointer text-sm font-medium transition-colors"
        >
          取消加入
        </button>
      ) : null}
    </div>
  );
});
