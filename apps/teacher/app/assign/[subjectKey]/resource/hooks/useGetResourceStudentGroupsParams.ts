import { AssignCourseTimeRange } from "@/types/assign/course";
import { Signal } from "@preact-signals/safe-react";
import { endOfDay } from "date-fns";
import { useCallback } from "react";

export default function useGetResourceStudentGroupsParams(assignTimeRanges: Signal<AssignCourseTimeRange[]>) {
    const getResourceStudentGroupsParams = useCallback(function () {
        return assignTimeRanges.value.map((item) => {
            const { classInfo, startTime, endTime, isImmediate, isRecommend } = item;
            return {
                groupType: 2,// 有待确认是个啥
                groupId: classInfo.jobClass,
                studentIds: [],
                startTime: isImmediate
                    ? Math.floor(Date.now() / 1000)
                    : Math.floor(startTime / 1000),
                deadline: isRecommend
                    ? Math.floor(endOfDay(Date.now()).getTime() / 1000)
                    : Math.floor(endTime / 1000),
            };
        })
    }, [assignTimeRanges])

    return {
        getResourceStudentGroupsParams,
    }
}