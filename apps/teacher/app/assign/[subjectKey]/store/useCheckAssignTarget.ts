import { useApp } from "@/hooks/useApp";
import { getStudentsByClassIds } from "@/services/assign";
import { TargetJobClass } from "@/types";
import { getTeacherClassListBySubjectKey } from "@/utils";
import { useSignal } from "@preact-signals/safe-react";
import { useRequest } from "ahooks";
import { useCallback, useMemo } from "react";

export default function useCheckAssignTarget(
  currentSubjectKey: number,
  defaultSelectFirstClass: boolean = false
) {
  const { userInfo } = useApp();

  // 可选择的班级 from 用户信息
  const classList = useMemo(() => {
    return getTeacherClassListBySubjectKey(currentSubjectKey, userInfo);
  }, [currentSubjectKey, userInfo]);

  // 选中的班级
  const checkedClasses = useSignal<TargetJobClass[]>(
    defaultSelectFirstClass ? [classList[0]] : []
  );

  const isCheckedClassStudentListReady = useSignal(false);
  // NOTE: 目前是选了班级就需要学生信息，未来可以考虑拆除去，但目前是非必须
  // 选中的班级学生列表
  const {
    data: checkedClassStudentList,
    loading: isLoadingCheckedClassStudentList,
  } = useRequest(
    async () => {
      const classIdsStr = checkedClasses
        .peek()
        .map((item) => item.jobClass)
        .join(",");

      if (!classIdsStr) {
        return [];
      }

      const res = await getStudentsByClassIds(classIdsStr);
      return res.map((item) => ({
        ...item,
        classIDStr: String(item.classID),
      }));
    },
    {
      refreshDeps: [
        checkedClasses.value.map((item) => item.jobClass).join(","),
      ],
      ready: isCheckedClassStudentListReady.value || defaultSelectFirstClass,
      debounceWait: 300,
    }
  );

  // Methods
  function toggleClass(jobClass: TargetJobClass) {
    isCheckedClassStudentListReady.value = true;
    const alreadyHas = checkedClasses.value.find(
      (item) => item.jobClass === jobClass.jobClass
    );
    if (alreadyHas) {
      checkedClasses.value = checkedClasses.value.filter(
        (item) => item.jobClass !== jobClass.jobClass
      );
    } else {
      checkedClasses.value = checkedClasses.value.concat(jobClass);
    }
  }

  const resetCheckedClasses = useCallback(() => {
    checkedClasses.value = [];
  }, [checkedClasses]);

  return {
    classList,

    checkedClasses,
    checkedClassStudentList,
    isLoadingCheckedClassStudentList,

    toggleClass,
    resetCheckedClasses,
  };
}
