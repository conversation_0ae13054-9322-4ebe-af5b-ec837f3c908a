"use client";
import Link from "@/components/TchLink";
import { LOCALSTORAGE_RESOURCE_CAR_KEY } from "@/configs";
import FileOpenIcon from "@/public/files/file-open.svg";
import { ResourceItem } from "@/types/resource";
import { cn } from "@/utils";
import { useLocalStorageState } from "ahooks";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { FileResourceDrawer } from "./_components/FileResourceDrawer";

export default function Layout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  const [resourcesCar] = useLocalStorageState<ResourceItem[] | undefined>(
    LOCALSTORAGE_RESOURCE_CAR_KEY,
    {
      listenStorageChange: true,
    }
  );

  const [fileResourceDrawerOpen, setFileResourceDrawerOpen] = useState(false);

  return (
    <div className="flex h-full flex-col">
      <div className="h-17.5 flex flex-none items-center gap-6 px-6">
        <Link
          href="/resource/my"
          className={cn(
            "text-gray-5 before:rounded-xs relative text-xl/normal font-medium before:absolute before:-bottom-1 before:left-1/2 before:h-1 before:w-3 before:-translate-x-1/2 before:bg-transparent",
            pathname === "/resource/my" && "text-gray-1 before:bg-primary-1"
          )}
        >
          我的资源
        </Link>

        <Link
          href="/resource/common"
          className={cn(
            "text-gray-5 before:rounded-xs relative text-xl/normal font-medium before:absolute before:-bottom-1 before:left-1/2 before:h-1 before:w-3 before:-translate-x-1/2 before:bg-transparent",
            pathname === "/resource/common" && "text-gray-1 before:bg-primary-1"
          )}
        >
          公共资源
        </Link>
        <Link
          href="/resource/school-based"
          className={cn(
            "text-gray-5 before:rounded-xs relative text-xl/normal font-medium before:absolute before:-bottom-1 before:left-1/2 before:h-1 before:w-3 before:-translate-x-1/2 before:bg-transparent",
            pathname === "/resource/school-based" &&
              "text-gray-1 before:bg-primary-1"
          )}
        >
          校本资源
        </Link>

        <div id="keyword-input-container" className="ml-auto"></div>
      </div>

      <div className="flex-1 overflow-hidden">
        {children}

        {/* 资源栏 */}
        <div className="bottom-25 absolute right-0">
          {/* 文件资源 */}
          <div
            className="bg-orange-2 hover:bg-orange-1 flex h-10 w-16 cursor-pointer items-center justify-center gap-2 rounded-l-full transition-colors"
            onClick={() => setFileResourceDrawerOpen(true)}
          >
            <FileOpenIcon />
            <span className="text-sm/normal font-extrabold text-white">
              {resourcesCar?.length ?? 0}
            </span>
          </div>
        </div>

        {/* 文件资源抽屉 */}
        <FileResourceDrawer
          open={fileResourceDrawerOpen}
          onOpenChange={setFileResourceDrawerOpen}
        />
      </div>
    </div>
  );
}
