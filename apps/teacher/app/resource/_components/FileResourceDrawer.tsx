"use client";

import DataEmpty from "@/components/DataEmpty";
import { LOCALSTORAGE_RESOURCE_CAR_KEY } from "@/configs";
import DeleteIcon from "@/public/icons/ic_delete.svg";
import { ResourceItem } from "@/types/resource";
import { ScrollArea } from "@/ui/scroll-area";
import { Button } from "@/ui/tch-button";
import { TchSheet } from "@/ui/tch-sheet";
import { getFileIcon } from "@/utils";
import { useLocalStorageState } from "ahooks";
import { produce } from "immer";
import { useRouter } from "next/navigation";
import { match } from "ts-pattern";

export function FileResourceDrawer({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const router = useRouter();
  const [resourcesCar, setResourcesCar] = useLocalStorageState<ResourceItem[]>(
    LOCALSTORAGE_RESOURCE_CAR_KEY,
    {
      defaultValue: [],
      listenStorageChange: true,
    }
  );

  return (
    <TchSheet
      className="w-69 top-25 bottom-20 h-[unset] shadow-[0_0.5rem_4rem_0_rgba(16,18,25,0.10)]"
      open={open}
      onOpenChange={onOpenChange}
      side="right"
      mask={false}
    >
      {/* 抽屉内容 */}
      <div className="flex flex-1 flex-col overflow-hidden">
        <div className="flex items-center justify-between px-5 py-4">
          <div className="text-gray-1 text-base/normal font-semibold">
            文件({resourcesCar.length})
          </div>
          <span
            className="text-gray-3 flex cursor-pointer items-center gap-1 text-sm/normal"
            onClick={() => {
              setResourcesCar([]);
            }}
          >
            <DeleteIcon></DeleteIcon>
            清空
          </span>
        </div>

        {match(resourcesCar.length)
          .with(0, () => <DataEmpty></DataEmpty>)
          .otherwise(() => (
            <>
              <ScrollArea className="h-full" viewportClassName="[&>div]:flex!">
                <div className="space-y-2 overflow-hidden px-5">
                  {resourcesCar.map((item) => {
                    const FileIcon = getFileIcon(item.fileExtension);

                    return (
                      <div
                        key={item.resourceId}
                        className="border-line-2 flex w-full items-center gap-3 overflow-hidden rounded-md border px-4 py-3"
                      >
                        <FileIcon className="flex-none" />

                        <div className="text-gray-1 flex-1 truncate text-sm/normal">
                          {item.userFilename}
                        </div>

                        <DeleteIcon
                          className="flex-none cursor-pointer"
                          onClick={() => {
                            setResourcesCar(
                              produce(resourcesCar, (draft) => {
                                draft.splice(
                                  draft.findIndex(
                                    (resource) =>
                                      resource.resourceId === item.resourceId
                                  ),
                                  1
                                );
                              })
                            );
                          }}
                        />
                      </div>
                    );
                  })}
                </div>
              </ScrollArea>

              <div className="flex justify-center gap-3 py-3">
                <Button radius="full" onClick={() => onOpenChange(false)}>
                  关闭
                </Button>
                <Button
                  className="bg-primary-2"
                  type="primary"
                  radius="full"
                  onClick={() => router.push("/assign/resource")}
                >
                  去布置
                </Button>
              </div>
            </>
          ))}
      </div>
    </TchSheet>
  );
}
