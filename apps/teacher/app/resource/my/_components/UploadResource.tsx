"use client";
import {
  TT_UPLOADER_APPID,
  TT_UPLOADER_SPACE_NAME,
  TT_UPLOADER_USER_ID,
} from "@/configs";
import { addResource, getUploadToken } from "@/services";
import { Button } from "@/ui/tch-button";
import { toast } from "@/ui/toast";
import { getFileNameAndExtension, validateFileWithMagicBytes } from "@/utils";
import AwsS3 from "@uppy/aws-s3";
import Uppy, { Body, Meta, UppyFile } from "@uppy/core";
import { UppyContextProvider } from "@uppy/react";
import { useRequest } from "ahooks";
import { useState } from "react";
import { match } from "ts-pattern";
import TTUploader, { VodFileOption } from "tt-uploader";
import EditResource from "./EditResource";
import { Dashboard } from "./UppyDashboard";

export default function UploadResource({
  onCancel,
  onSuccess,
}: {
  onCancel: () => void;
  onSuccess: () => void;
}) {
  const [editResource, setEditResource] = useState(false);
  // 上传完成的文件
  const [file, setFile] = useState<UppyFile<Meta, Body>>();

  const addResourceRequest = useRequest(
    (file: UppyFile<Meta, Body>) => {
      const payload = {
        fileSizebytes: file.size!,
        objectKey: file.meta.objectKey as string,
        originalFileName: file.name!,
        vid: file.meta.vid as string,
        vodUrl: file.meta.vodUrl as string,
        duration: (file.meta.duration || 0) as number,
      };

      return addResource(payload);
    },
    {
      manual: true,
      onSuccess(data, params) {
        const file = params[0];
        file.meta.resouceId = data.resouceId;
        file.meta.teacherResourceDbId = data.teacherResourceDbId;
        setFile(file);
        onSuccess();
      },
    }
  );

  const getUploadTokenRequest = useRequest(getUploadToken, { manual: true });

  const [uppy] = useState(() => {
    return new Uppy({
      autoProceed: true, // 文件添加后自动开始上传
      restrictions: {
        maxNumberOfFiles: 1,
        allowedFileTypes: [
          // 文档类型
          "application/pdf", // pdf
          "application/msword", // doc
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // docx
          "application/vnd.ms-excel", // xls
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // xlsx
          "application/vnd.ms-powerpoint", // ppt
          "application/vnd.openxmlformats-officedocument.presentationml.presentation", // pptx

          // 图片类型
          "image/jpeg", // jpg/jpeg
          "image/png", // png

          // 音频类型
          "audio/mpeg", // mp3

          // 视频类型
          "video/mp4", // mp4
        ],
        maxFileSize: 100 * 1024 * 1024,
      },
      allowMultipleUploadBatches: true,
    })
      .use(AwsS3, {
        shouldUseMultipart: false,
        getUploadParameters: async (file) => {
          const isValid = await validateFileWithMagicBytes(file.data as File);

          if (!isValid) {
            const errorMsg = "文件损坏，无法正确打开，请重新上传文件";

            // 文件异常，添加元信息error
            uppy.setFileMeta(file.id, {
              error: errorMsg,
            });

            return Promise.reject(errorMsg);
          }

          const [name, ext] = getFileNameAndExtension(file.meta.name);
          const newName = `${name}_${Math.floor(Date.now() / 1000)}.${ext}`;

          file.name = newName;

          const token = await getUploadTokenRequest.runAsync({
            originFile: newName,
            fileType: file.type,
          });

          file.meta.vodToken = token.vodToken;
          file.meta.objectKey = token.objectKey;
          // vod 的上传进度
          file.meta.vodPercentage = 0;

          return {
            method: "PUT",
            url: token.uploadUrl,
            headers: {
              "Content-Type": file.type,
            },
          };
        },
      })
      .on("upload-success", (file) => {
        if (!file) {
          return;
        }

        if (!file.meta.vodToken) {
          return addResourceRequest.run(file);
        }

        const vodToken = file.meta.vodToken as VodFileOption["stsToken"];

        // TTUploader 重新上传
        const uploader = new TTUploader({
          // 必填，在视频点播控制台获取的 AppID
          appId: TT_UPLOADER_APPID,
          // 必填，建议设置能识别用户的唯一标识 ID，用于上传出错时排查问题，不要传入非 ASCII 编码
          userId: TT_UPLOADER_USER_ID,
          // 必填，上传相关配置
          videoConfig: {
            // 必填，上传到的点播空间名
            spaceName: TT_UPLOADER_SPACE_NAME,
            // 非必填，视频/文件上传后的处理 action 对象，类型是一个数组，有多个处理请求时可以配置多个动作对象。对象中的 input 会被透传到对应的处理服务中
          },
        });

        const fileKey = uploader.addFile({
          file: file.data,
          fileName: file.meta.objectKey as string,
          stsToken: vodToken, //从应用服务端获取到的临时上传 token
          type: "video", // 上传文件类型，四个可选值：video（视频或者音频，默认值），image（图片），object（普通文件，例如字幕），media（素材文件）
        });

        // 更新进度
        uploader.on("progress", (info) => {
          uppy.setFileMeta(file.id, {
            vodPercentage: info.percent,
          });
        });

        uploader.on("complete", (infor) => {
          file.meta.vid = infor.uploadResult.Vid;
          file.meta.vodUrl = infor.uploadResult.SourceInfo.StoreUri;
          file.meta.duration = Math.ceil(
            infor.uploadResult.SourceInfo.Duration * 1000
          );

          addResourceRequest.run(file);
        });

        uploader.on("error", (infor) => {
          uppy.setFileState(file.id, {
            error: infor.res,
          });
        });

        uploader.start(fileKey);
      });
  });

  return match(editResource && Boolean(file))
    .with(false, () => (
      <>
        <UppyContextProvider uppy={uppy}>
          <Dashboard />
        </UppyContextProvider>

        <div className="pt-13.5 flex justify-end gap-3 px-6 pb-5">
          <Button
            type="default"
            size="lg"
            radius="full"
            className="feedback_btn_cancel text-gray-2 min-w-24"
            onClick={onCancel}
          >
            取 消
          </Button>
          <Button
            type="primary"
            size="lg"
            radius="full"
            loading={addResourceRequest.loading}
            onClick={() => {
              if (uppy.getFiles().length === 0) {
                return toast.warning(`请先选择要上传的资源`);
              }

              if (!file) {
                return toast.warning(`资源添加中，请稍后重试`);
              }

              setEditResource(true);
            }}
          >
            下一步
          </Button>
        </div>
      </>
    ))
    .with(true, () => {
      return (
        <EditResource
          fileName={file!.meta.name}
          teacherResourceDbId={file!.meta.teacherResourceDbId as number}
          onSuccess={() => {
            onSuccess();
            onCancel();
          }}
          onCancel={onCancel}
        />
      );
    })
    .otherwise(() => null);
}
