"use client";

import {
  GET_GRADE_AND_SUBJECT_LIST_REQUEST_KEY,
  LOCALSTORAGE_RESOURCE_CAR_KEY,
} from "@/configs";
import IcUpload from "@/public/icons/ic_upload.svg";
import { deleteResource, getGradeAndSubjectList } from "@/services";
import { ResourceItem } from "@/types/resource";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/ui/alert-dialog";
import { InputSearch } from "@/ui/searchInput";
import { Button } from "@/ui/tch-button";
import { TchSelect } from "@/ui/tch-select";
import { useCreation, useLocalStorageState, useRequest } from "ahooks";
import { produce } from "immer";
import { useRef, useState } from "react";
import {
  ResourceTable,
  ResourceTableRef,
} from "../../../components/resource/ResourceTable";
import EditResource from "./_components/EditResource";
import ResourceDialog from "./_components/ResourceDialog";
import UploadResource from "./_components/UploadResource";

export default function ResourcePage() {
  const resourceTableRef = useRef<ResourceTableRef>(null);

  const getGradeAndSubjectListRequest = useRequest(getGradeAndSubjectList, {
    cacheKey: GET_GRADE_AND_SUBJECT_LIST_REQUEST_KEY,
    staleTime: 1000 * 60 * 60,
  });

  const gradeList = useCreation(() => {
    const grades =
      getGradeAndSubjectListRequest.data?.phraseGrade?.map((item) => {
        return item.grades;
      }) ?? [];

    return grades.flat();
  }, [getGradeAndSubjectListRequest.data]);

  const subjectList = useCreation(() => {
    return getGradeAndSubjectListRequest.data?.subjects || [];
  }, [getGradeAndSubjectListRequest.data]);

  const [searchKeyword, setSearchKeyword] = useState("");

  const [selectedGradeList, setSelectedGradeList] = useState<
    readonly { id: number; name: string }[]
  >([]);
  const [selectedSubjectList, setSelectedSubjectList] = useState<
    readonly { id: number; name: string }[]
  >([]);
  const [currentPage, setCurrentPage] = useState(1);

  const [open, setOpen] = useState(false);

  const resetParams = () => {
    setCurrentPage(1);
    setSelectedGradeList([]);
    setSelectedSubjectList([]);
  };

  const [currentResource, setCurrentResource] = useState<ResourceItem | null>(
    null
  );
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  const [resourcesCar, setResourcesCar] = useLocalStorageState<ResourceItem[]>(
    LOCALSTORAGE_RESOURCE_CAR_KEY,
    {
      defaultValue: [],
      listenStorageChange: true,
    }
  );

  const deleteResourceRequest = useRequest(deleteResource, {
    manual: true,
    onSuccess() {
      const index = resourcesCar.findIndex(
        (item) =>
          item.teacherResourceDbId === currentResource?.teacherResourceDbId
      );

      // 如果在资源车里，也需要同步移除
      if (index > -1) {
        setResourcesCar(
          produce(resourcesCar, (draft) => {
            draft.splice(index, 1);
          })
        );
      }

      resourceTableRef.current?.refresh();
      setDeleteDialogOpen(false);
      setCurrentResource(null);
    },
  });

  return (
    <div className="border-1 border-line-1 mx-6 flex h-full flex-col overflow-hidden rounded-xl bg-white p-5">
      {/* 筛选器 */}
      <div className="border-b-1 border-line-2 h-9.5 mb-4 flex flex-none gap-4">
        <div className="h-17.5 absolute right-6 top-0 ml-auto flex items-center">
          <InputSearch
            className="w-60"
            type="text"
            placeholder="输入关键字搜索资源"
            value={searchKeyword}
            onChange={(event) => {
              setCurrentPage(1);
              setSearchKeyword(event.target.value);
            }}
            debounce
            wait={300}
          />
        </div>

        <TchSelect
          value={selectedGradeList}
          isMulti
          onChange={(options) => {
            setCurrentPage(1);
            setSelectedGradeList(options);
          }}
          options={gradeList}
          getOptionLabel={(option) => option.name}
          getOptionValue={(option) => option.id.toString()}
          placeholder="年级"
        />

        <TchSelect
          value={selectedSubjectList}
          isMulti
          onChange={(options) => {
            setCurrentPage(1);
            setSelectedSubjectList(options);
          }}
          options={subjectList}
          getOptionLabel={(option) => option.name}
          getOptionValue={(option) => option.id.toString()}
          placeholder="学科"
        />

        <Button
          size="sm"
          className="text-gray-2 ml-auto h-7 gap-2 rounded-md text-sm"
          onClick={() => setOpen(true)}
        >
          <IcUpload className="size-3.5" />
          上传文件
        </Button>
      </div>

      <div className="flex-1 overflow-hidden">
        <ResourceTable
          ref={resourceTableRef}
          resetParams={resetParams}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          searchKeyword={searchKeyword}
          selectedGradeList={selectedGradeList}
          selectedSubjectList={selectedSubjectList}
          onDelete={(resource) => {
            setCurrentResource(resource);
            setDeleteDialogOpen(true);
          }}
          onEdit={(resource) => {
            setCurrentResource(resource);
            setEditDialogOpen(true);
          }}
          needOperation
          joinedResources={resourcesCar}
          setJoinedResources={setResourcesCar}
        />
      </div>

      <ResourceDialog open={open} setOpen={setOpen} title="上传资源">
        <UploadResource
          onCancel={() => setOpen(false)}
          onSuccess={() => resetParams()}
        />
      </ResourceDialog>

      {currentResource && (
        <ResourceDialog
          title="编辑资源"
          open={editDialogOpen}
          setOpen={setEditDialogOpen}
        >
          <EditResource
            onSuccess={() => {
              setEditDialogOpen(false);
              setCurrentResource(null);
              resourceTableRef.current?.refresh();
            }}
            onCancel={() => {
              setEditDialogOpen(false);
              setCurrentResource(null);
            }}
            teacherResourceDbId={currentResource!.teacherResourceDbId}
            fileName={
              currentResource!.userFilename +
              "." +
              currentResource!.fileExtension
            }
            defaultValues={{
              userFileName: currentResource!.userFilename,
              grade: currentResource!.gradeIds,
              subject: currentResource!.subjectIds,
            }}
          />
        </ResourceDialog>
      )}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-lg font-medium text-gray-900">
              确认要删除文件吗？
            </AlertDialogTitle>
            <AlertDialogDescription className="mt-1 text-sm text-gray-500">
              删除后数据不再保存
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex gap-3 pt-4">
            <AlertDialogCancel
              onClick={() => {
                setDeleteDialogOpen(false);
                setCurrentResource(null);
              }}
            >
              取消
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (currentResource) {
                  deleteResourceRequest.run(
                    currentResource!.teacherResourceDbId
                  );
                }
              }}
              disabled={deleteResourceRequest.loading}
            >
              确认
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
