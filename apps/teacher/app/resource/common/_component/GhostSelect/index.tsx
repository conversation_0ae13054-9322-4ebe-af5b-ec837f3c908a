import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/ui/select";
import { cn } from "@/utils/utils";

export default function GhostSelect({
  value,
  onChange,
  options,
  placeholder = "请选择",
  className,
  defaultValue,
}: {
  options: { label: string; value: string }[];
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  defaultValue?: string;
}) {
  return (
    <Select value={value} onValueChange={onChange} defaultValue={defaultValue}>
      <SelectTrigger
        className={cn(
          "text-gray-2 box-shadow-none h-5 min-h-0 cursor-pointer border-none pr-0 shadow-none focus-visible:border-none focus-visible:ring-0",
          className
        )}
        classNames={{
          icon: "text-gray-2",
        }}
        style={{
          boxShadow: "none !important",
          border: "none !important",
        }}
      >
        <SelectValue
          className="text-gray-2"
          placeholder={placeholder}
        ></SelectValue>
      </SelectTrigger>
      <SelectContent>
        {options?.map((option) => (
          <SelectItem value={String(option.value)} key={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
