"use client";

import { getQuestionList } from "@/services/assign-homework";
import {
  convertQuestion,
  QuestionFilterEnumData,
  QuestionListParams,
  QuestionListResponseData,
} from "@/types/assign";
import { ScrollArea } from "@/ui/scroll-area";
import { But<PERSON> } from "@/ui/tch-button";
import { useEffect } from "@preact-signals/safe-react/react";
import { QuestionList } from "@repo/core/views/tch-question-view";
import { useRequest } from "ahooks";
import { useCallback, useMemo, useRef, useState } from "react";
import { useQuestionStoreContext } from "./store";

export default function QuestionListLayout({
  enums,
}: {
  enums?: QuestionFilterEnumData;
}) {
  const {
    questionType,
    questionDifficulty,
    year,
    subject,
    treeType,
    treeId,
    bizTreeNodeId,
    sortBy,
    page,
    setPage,
    province,
  } = useQuestionStoreContext();

  const [questionList, setQuestionList] =
    useState<QuestionListResponseData | null>(null);

  const { loading: loading } = useRequest(
    async () => {
      if (
        !subject.value ||
        !treeType.value ||
        !treeId.value ||
        !bizTreeNodeId.value
      ) {
        return undefined;
      }

      const searchTreeInfo: QuestionListParams = {
        questionType: questionType.value ? [questionType.value] : undefined,
        questionDifficult: questionDifficulty.value
          ? [questionDifficulty.value]
          : undefined,
        questionYears: year.value ? [year.value] : undefined,
        subject: subject.value,
        sort: sortBy.value,
        questionProvince: province.value ? [province.value] : undefined,

        page: page.value,
        pageSize: 20,
      };
      if (treeType.value === "chapter") {
        searchTreeInfo.bizTreeNodeIds = [bizTreeNodeId.value];
      } else {
        searchTreeInfo.baseTreeNodeIds = [bizTreeNodeId.value];
      }

      try {
        const res = await getQuestionList(searchTreeInfo);
        if (page.value === 1) {
          setQuestionList(res);
        } else {
          setQuestionList((prev) => {
            if (!prev) {
              return res;
            }
            return {
              ...prev,
              list: [...prev.list, ...res.list],
            };
          });
        }
        return res;
      } catch (error) {
        console.error(error);
        if (page.value === 1) {
          setQuestionList(null);
        }

        return {
          list: [],
          total: 0,
        };
      }
    },
    {
      refreshDeps: [
        subject.value,
        treeType.value,
        treeId.value,
        bizTreeNodeId.value,
        page.value,
        questionType.value,
        questionDifficulty.value,
        year.value,
        sortBy.value,
        province.value,
      ],
    }
  );

  const questionFilterEnumMap = useMemo(() => {
    return {
      questionTypeList: new Map(
        enums?.questionTypeList.map((item) => [item.value, item])
      ),
      questionDifficultList: new Map(
        enums?.questionDifficultList.map((item) => [item.value, item])
      ),
      yearList: new Map(enums?.yearList.map((item) => [item.value, item])),
      provinceList: new Map(
        enums?.provinceList.map((item) => [item.value, item])
      ),
    };
  }, [enums]);

  const transformQuestionList = useMemo(() => {
    return (
      questionList?.list.map((item) => {
        return convertQuestion(item, questionFilterEnumMap || {});
      }) || []
    );
  }, [questionFilterEnumMap, questionList?.list]);

  const ref = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState(0);
  useEffect(() => {
    if (ref.current) {
      setHeight(ref.current.getBoundingClientRect().top);
    }
  }, [ref]);

  const loadMore = useCallback(() => {
    setPage(page.value + 1);
  }, [page, setPage]);

  return (
    <div className="flex h-full flex-1 flex-col">
      <div className="flex shrink-0 items-center">
        <span className="text-sm font-bold">试题列表</span>
        <span className="text-gray-4 ml-2 text-xs">
          共{questionList?.total || "0"}
          道试题
        </span>
      </div>
      <div ref={ref} className="mt-4 flex-1">
        <ScrollArea
          className="h-full"
          style={{ height: `calc(100vh - ${height + 78}px)` }}
        >
          <QuestionList
            className="px-0"
            questions={transformQuestionList}
            loading={page.value === 1 && loading}
            hasFooterButton={false}
          />
          {transformQuestionList.length < (questionList?.total || 0) && (
            <div className="flex justify-center py-4">
              <Button
                type="primary"
                onClick={loadMore}
                disabled={loading}
                loading={loading}
              >
                {loading ? "加载中..." : "加载更多"}
              </Button>
            </div>
          )}
        </ScrollArea>
      </div>
    </div>
  );
}
