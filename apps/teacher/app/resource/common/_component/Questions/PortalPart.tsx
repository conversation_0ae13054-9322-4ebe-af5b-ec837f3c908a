"use client";
import useSubjectInfosByUserInfo from "@/hooks/useSubjectInfosByUserInfo";
import { InputSearch } from "@/ui/searchInput";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/ui/select";
import { Signal } from "@preact-signals/safe-react";
import { createPortal } from "react-dom";

export default function PortalPart({
  searchKeyword,
  setSearchKeyword,
  subject,
  setSubject,
}: {
  subject: Signal<number>;
  searchKeyword: Signal<string>;
  setSearchKeyword: (value: string) => void;
  setSubject: (value: number) => void;
}) {
  // Dirty portal
  const container = document.querySelector("#keyword-input-container");

  const { subjectInfos } = useSubjectInfosByUserInfo();

  if (!container) return null;

  return createPortal(
    <div className="flex items-center gap-6">
      <InputSearch
        className="ml-auto h-8 w-60"
        type="text"
        placeholder="输入关键字搜索资源"
        defaultValue={searchKeyword.peek()}
        onChange={(e) => setSearchKeyword(e.target.value)}
        debounce
        wait={300}
      />
      <Select
        defaultValue={String(subject.peek())}
        onValueChange={(value) => setSubject(Number(value))}
      >
        <SelectTrigger
          className="w-19 min-h-8 gap-1.5 rounded-2xl data-[size=default]:h-8"
          classNames={{
            icon: "text-gray-2",
          }}
        >
          <SelectValue placeholder="学科" />
        </SelectTrigger>
        <SelectContent>
          {subjectInfos.value.map((e) => {
            return (
              <SelectItem key={e.subjectKey} value={String(e.subjectKey)}>
                {e.subjectName}
              </SelectItem>
            );
          })}
        </SelectContent>
      </Select>
    </div>,
    container
  );
}
