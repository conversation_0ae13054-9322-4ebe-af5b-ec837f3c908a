import { Card } from "@/ui/card";
import { cn } from "@/utils/utils";

export default function QuestionsCard({
  children,
  className,
  ...props
}: {
  children: React.ReactNode;
  className?: string;
  props?: React.ComponentProps<typeof Card>;
}) {
  return (
    <Card
      className={cn("flex flex-col gap-4 rounded-[0.625rem]", className)}
      {...props}
    >
      {children}
    </Card>
  );
}
