import Card from "./Card";
import PortalPart from "./PortalPart";
import QuestionList from "./QuestionListCard";
import useQuestionsStore, {
  QuestionStoreContext,
  useQuestionStoreContext,
} from "./store";
import TreeDetailList from "./TreeDetailList";
import TreeTypeSelect from "./TreeTypeSelect";

function Content() {
  const { searchKeyword, setSearchKeyword, subject, setSubject } =
    useQuestionStoreContext();
  return (
    <>
      <PortalPart
        searchKeyword={searchKeyword}
        setSearchKeyword={setSearchKeyword}
        subject={subject}
        setSubject={setSubject}
      />
      <div className="flex h-full flex-col pb-0 pt-5">
        <div className="flex flex-1 gap-5">
          {/* 树和选择 */}
          <Card className="min-w-30 flex flex-1 flex-col gap-4 p-4 pb-0">
            <TreeTypeSelect />
            <TreeDetailList />
          </Card>

          {/* 题目列表 */}
          <Card className="flex-3 pb-0 pt-4">
            <QuestionList />
          </Card>
        </div>
      </div>
    </>
  );
}

export default function Questions() {
  const storeState = useQuestionsStore();

  return (
    <QuestionStoreContext.Provider value={storeState}>
      <Content />
    </QuestionStoreContext.Provider>
  );
}
