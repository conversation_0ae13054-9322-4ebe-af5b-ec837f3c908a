import useSubjectInfosByUserInfo from "@/hooks/useSubjectInfosByUserInfo";
import { batch, useSignal } from "@preact-signals/safe-react";
import { createContext, useCallback, useContext } from "react";
import { TreeType } from "../type";

export default function useQuestionsStore() {
  const { subjectInfos } = useSubjectInfosByUserInfo();

  const treeType = useSignal<TreeType>("chapter");
  const treeId = useSignal<number | null>(null);
  const subject = useSignal<number>(subjectInfos.value?.[0]?.subjectKey ?? 0);
  const searchKeyword = useSignal<string>("");
  const bizTreeNodeId = useSignal<number | null>(null);
  const questionType = useSignal<number>(0);
  const questionDifficulty = useSignal<number>(0);
  const year = useSignal<number>(0);
  const sortBy = useSignal<"createTime" | "useCount">("createTime");
  const province = useSignal<number>(0);
  const page = useSignal<number>(1);

  // Business Callbacks
  const setPage = useCallback(
    (newVal: number) => {
      page.value = newVal;
    },
    [page]
  );

  const setTreeType = useCallback(
    (type: TreeType) => {
      batch(() => {
        treeType.value = type;
        treeId.value = null;
        bizTreeNodeId.value = null;
        page.value = 1;
      });
    },
    [page, bizTreeNodeId, treeId, treeType]
  );
  const setTreeId = useCallback(
    (id: number | null) => {
      batch(() => {
        treeId.value = id;
        bizTreeNodeId.value = null;
        page.value = 1;
      });
    },
    [page, bizTreeNodeId, treeId]
  );

  const setSearchKeyword = useCallback(
    (newVal: string) => {
      searchKeyword.value = newVal;
      page.value = 1;
    },
    [page, searchKeyword]
  );

  const setSubject = useCallback(
    (newVal: number) => {
      subject.value = newVal;
      page.value = 1;
    },
    [page, subject]
  );

  const setBizTreeNodeId = useCallback(
    (newVal: number) => {
      bizTreeNodeId.value = newVal;
      page.value = 1;
    },
    [page, bizTreeNodeId]
  );

  const setQuestionType = useCallback(
    (newVal: number) => {
      questionType.value = newVal;
      page.value = 1;
    },
    [page, questionType]
  );

  const setQuestionDifficulty = useCallback(
    (newVal: number) => {
      questionDifficulty.value = newVal;
      page.value = 1;
    },
    [page, questionDifficulty]
  );

  const setYear = useCallback(
    (newVal: number) => {
      year.value = newVal;
      page.value = 1;
    },
    [page, year]
  );

  // TODO
  const setSortBy = useCallback(
    (newVal: "createTime" | "useCount") => {
      sortBy.value = newVal;
      page.value = 1;
    },
    [page, sortBy]
  );

  const setProvince = useCallback(
    (newVal: number) => {
      province.value = newVal;
      page.value = 1;
    },
    [page, province]
  );

  return {
    treeType,
    treeId,
    subject,
    setTreeType,
    setTreeId,
    setSubject,

    searchKeyword,
    setSearchKeyword,

    bizTreeNodeId,
    setBizTreeNodeId,

    questionType,
    setQuestionType,

    questionDifficulty,
    setQuestionDifficulty,

    year,
    setYear,

    sortBy,
    setSortBy,

    province,
    setProvince,

    page,
    setPage,
  };
}

export const QuestionStoreContext = createContext<ReturnType<
  typeof useQuestionsStore
> | null>(null);

export const useQuestionStoreContext = () => {
  const context = useContext(QuestionStoreContext);
  if (!context) {
    throw new Error(
      "useQuestionStoreContext must be used within a QuestionStoreProvider"
    );
  }
  return context;
};
