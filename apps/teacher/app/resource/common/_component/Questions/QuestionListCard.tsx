import { getQuestionFilterEnum } from "@/services/assign-homework";
import { useRequest } from "ahooks";
import { useMemo } from "react";
import QuestionListLayout from "./QuestionListLayout";
import QuestionSearchForm from "./QuestionSearchForm";

const formatEnum = (
  list: { nameZh: string; value: number }[],
  allOption: { label: string; value: string }
) => {
  return [
    allOption,
    ...list.map((e) => ({
      label: e.nameZh,
      value: e.value.toString(),
    })),
  ];
};

export default function QuestionListCard() {
  const { data: enums } = useRequest(
    async () => {
      const res = await getQuestionFilterEnum();

      //   if (!questionType.peek()) {
      //     setQuestionType(res.questionTypeList[0].value);
      //   }

      //   if (!questionDifficulty.peek()) {
      //     setQuestionDifficulty(res.questionDifficultList[0].value);
      //   }

      //   if (!year.peek()) {
      //     setYear(res.yearList[0].value);
      //   }
      return res;
    },
    {
      cacheKey: "question-search-form-enums",
      staleTime: 1000 * 60 * 5,
    }
  );

  const enumsList = useMemo(() => {
    if (!enums) {
      return undefined;
    }
    return {
      questionTypeList: formatEnum(enums?.questionTypeList, {
        label: "题型",
        value: "0",
      }),
      questionDifficultyList: formatEnum(enums?.questionDifficultList, {
        label: "难度",
        value: "0",
      }),
      yearList: formatEnum(enums?.yearList, {
        label: "年份",
        value: "0",
      }),
      provinceList: formatEnum(enums?.provinceList, {
        label: "地区",
        value: "0",
      }),
    };
  }, [enums]);

  return (
    <div className="flex flex-1 flex-col px-5">
      <div className="flex-shrink-0">
        {/* 题目筛选 */}
        <QuestionSearchForm enums={enumsList} />
      </div>
      <div className="flex-1">
        {/* 题目列表 */}
        <QuestionListLayout enums={enums} />
      </div>
    </div>
  );
}
