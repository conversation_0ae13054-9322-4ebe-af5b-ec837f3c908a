import { But<PERSON> } from "@/ui/tch-button";
import { cn } from "@/utils/utils";
import { useQuestionStoreContext } from "./store";

export default function TreeTypeSelect() {
  const { treeType, setTreeType } = useQuestionStoreContext();

  return (
    <div className="flex justify-around gap-2">
      <Button
        type="text"
        className={cn(treeType.value === "chapter" && "text-primary-1")}
        onClick={() => treeType.value !== "chapter" && setTreeType("chapter")}
      >
        按章节
      </Button>
      <Button
        type="text"
        className={cn(treeType.value === "knowledge" && "text-primary-1")}
        onClick={() =>
          treeType.value !== "knowledge" && setTreeType("knowledge")
        }
      >
        按知识点
      </Button>
    </div>
  );
}
