import { Skeleton } from "@/ui/skeleton";
import GhostSelect from "../GhostSelect";
import { useQuestionStoreContext } from "./store";

const sortList = [
  { label: "按最新排序", value: "createTime" },
  { label: "按使用次数排序", value: "useCount" },
];

export default function QuestionSearchForm({
  enums,
}: {
  enums?: {
    questionTypeList: { label: string; value: string }[];
    questionDifficultyList: { label: string; value: string }[];
    yearList: { label: string; value: string }[];
    provinceList: { label: string; value: string }[];
  };
}) {
  const {
    questionType,
    setQuestionType,
    questionDifficulty,
    setQuestionDifficulty,
    year,
    setYear,

    province,
    setProvince,

    sortBy,
    setSortBy,
  } = useQuestionStoreContext();

  return (
    <div className="flex justify-between">
      <div className="flex items-center gap-5">
        {enums ? (
          <>
            {/* 类型 */}
            <GhostSelect
              defaultValue={questionType.peek().toString()}
              options={enums?.questionTypeList ?? []}
              onChange={(value) => {
                setQuestionType(Number(value));
              }}
            />
            {/* 难度 */}
            <GhostSelect
              defaultValue={questionDifficulty.peek().toString()}
              options={enums?.questionDifficultyList ?? []}
              onChange={(value) => {
                setQuestionDifficulty(Number(value));
              }}
            />
            {/* 地区 */}
            <GhostSelect
              defaultValue={province.peek().toString()}
              options={enums?.provinceList ?? []}
              onChange={(value) => {
                setProvince(Number(value));
              }}
            />
            {/* 年份 */}
            <GhostSelect
              defaultValue={year.peek().toString()}
              options={enums?.yearList ?? []}
              onChange={(value) => {
                setYear(Number(value));
              }}
            />
          </>
        ) : (
          <Skeleton />
        )}
      </div>
      <div>
        {/* 按xx排序 */}
        <GhostSelect
          defaultValue={sortBy.peek()}
          options={sortList}
          onChange={(value) => {
            setSortBy(value as "createTime" | "useCount");
          }}
        />
      </div>
    </div>
  );
}
