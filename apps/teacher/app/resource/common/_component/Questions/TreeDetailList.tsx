import { AssignTree } from "@/components/assign/assign-tree";
import {
  getBizTreeDetail,
  getBizTreeList,
  getKnowledgeTreeDetail,
  getKnowledgeTreeList,
} from "@/services/assign";
import { BizTreeDetailNode } from "@/types/assign";
import { ScrollArea } from "@/ui/scroll-area";
import { Skeleton } from "@/ui/skeleton";
import { useRequest } from "ahooks";
import { useEffect, useMemo, useRef, useState } from "react";
import SimpleSelect from "../SimpleSelect";
import { useQuestionStoreContext } from "./store";

const leftPathToLeaf = (arr?: BizTreeDetailNode[]) => {
  if (!arr) return [];
  const path = [];

  let current = arr[0];

  while (current) {
    path.push(current.bizTreeNodeId);
    current = current.bizTreeNodeChildren[0];
  }
  return path;
};

export default function TreeDetailList() {
  const { treeType, treeId, subject, setTreeId, bizTreeNodeId } =
    useQuestionStoreContext();

  const { data: books, loading: booksLoading } = useRequest(
    async () => {
      let res;
      if (treeType.value === "chapter") {
        res = await getBizTreeList(subject.value);
      } else {
        res = await getKnowledgeTreeList(subject.value);
      }

      if (
        res.length &&
        (!treeId.value || !res.find((e) => e.bizTreeId === treeId.value))
      ) {
        setTreeId(res[0].bizTreeId);
      }

      return res;
    },
    {
      refreshDeps: [subject.value, treeType.value],
      cacheKey: `${subject.value}-${treeType.value}`,
      cacheTime: 1000 * 60 * 5,
    }
  );

  const { data: treeData, loading: treeDataLoading } = useRequest(
    async () => {
      const _treeId = treeId.peek();
      if (!_treeId) {
        return undefined;
      }
      if (treeType.value === "chapter") {
        const res = await getBizTreeDetail(_treeId);

        const currentBizTreeNodeId = bizTreeNodeId.peek();
        if (
          !res.bizTreeDetail.bizTreeNodeChildren.find(
            (e) => e.bizTreeNodeId === currentBizTreeNodeId
          )
        ) {
          if (res.bizTreeDetail.bizTreeNodeChildren.length) {
            bizTreeNodeId.value =
              res.bizTreeDetail.bizTreeNodeChildren[0].bizTreeNodeId;
          } else {
            bizTreeNodeId.value = null;
          }
        }

        return res.bizTreeDetail.bizTreeNodeChildren.map((e) => ({
          ...e,
          key: e.bizTreeNodeId,
        }));
      } else {
        const res = await getKnowledgeTreeDetail(_treeId);
        return res.bizTreeDetail.bizTreeNodeChildren.map((e) => ({
          ...e,
          key: e.bizTreeNodeId,
        }));
      }
    },
    {
      refreshDeps: [treeId.value, treeType.value],
      ready: !!treeId.value && !!treeType.value && !booksLoading,
      cacheKey: `${treeId.value}-${treeType.value}`,
    }
  );

  const defaultExpandedKeys = useMemo(() => {
    return leftPathToLeaf(treeData);
  }, [treeData]);

  const ref = useRef<HTMLDivElement>(null);

  const [height, setHeight] = useState(0);
  useEffect(() => {
    if (ref.current) {
      setHeight(ref.current.getBoundingClientRect().top);
    }
  }, []);

  return (
    <div className="flex flex-1 flex-col overflow-hidden">
      <SimpleSelect
        value={treeId.value?.toString()}
        onChange={(value) => {
          setTreeId(Number(value));
        }}
        options={
          books?.map((book) => ({
            label: book.bizTreeName,
            value: book.bizTreeId.toString(),
          })) ?? []
        }
        placeholder="请选择教材"
      />
      <div className="flex-1" ref={ref}>
        {treeData && !treeDataLoading && !booksLoading ? (
          <ScrollArea
            className="mt-2 h-full flex-1"
            orientation="vertical"
            style={{
              height: `calc(100vh - ${height + 28}px)`,
            }}
          >
            <AssignTree
              fieldNames={{
                title: "bizTreeNodeName",
                key: "bizTreeNodeId",
                children: "bizTreeNodeChildren",
              }}
              treeData={treeData}
              defaultSelectedKeys={[bizTreeNodeId.peek() ?? 0]}
              defaultExpandParent
              defaultExpandedKeys={defaultExpandedKeys}
              onSelect={(keys) => {
                bizTreeNodeId.value = keys[0] as number;
              }}
            />
          </ScrollArea>
        ) : (
          <Skeleton className="mb-4 h-full w-full flex-1" />
        )}
      </div>
    </div>
  );
}
