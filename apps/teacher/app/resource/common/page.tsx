"use client";

import ComingSoon from "@/components/ComingSoon";
import TabNav from "@/components/common/tab-nav";
import { useSignal } from "@preact-signals/safe-react";
import { useCallback } from "react";
import Questions from "./_component/Questions";

const tabs = [
  {
    id: "questions",
    label: "试题",
  },
  {
    id: "papers",
    label: "试卷",
  },
  {
    id: "otherResources",
    label: "其它资源",
  },
];

export default function Page() {
  const currentTab = useSignal("questions");

  const setCurrentTab = useCallback(
    (tab: string) => {
      currentTab.value = tab;
    },
    [currentTab]
  );

  return (
    <div className="flex h-full flex-1 flex-col overflow-hidden pb-5 pl-9 pr-6">
      <TabNav
        tabs={tabs}
        activeTab={currentTab.value}
        onTabChange={setCurrentTab}
        tabClassName="whitespace-nowrap "
      ></TabNav>
      {currentTab.value === "questions" && <Questions />}
      {currentTab.value === "papers" && <ComingSoon />}
      {currentTab.value === "otherResources" && <ComingSoon />}
    </div>
  );
}
