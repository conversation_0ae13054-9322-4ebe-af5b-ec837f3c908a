import { PageHeader } from "@/components/PageHeader";
import { ScrollArea } from "@/ui/scroll-area";
import { readMarkdownForSSG } from "@/utils/md";

// 启用静态生成 (SSG)
export const dynamic = "force-static";

export default async function PrivacyPolicyPage() {
  // 使用新的 MD 工具，专为 SSG 优化
  const html = await readMarkdownForSSG("privacy-policy");

  return (
    <div className="fixed inset-0 bg-white">
    <div className="flex h-full flex-col">
      <PageHeader needBack className="text-gray-1 gap-2 text-xl font-medium">
        返回
      </PageHeader>

      <ScrollArea className="flex-1 overflow-hidden">
        <div className="px-8 pb-4">
          <article
            className="typography"
            dangerouslySetInnerHTML={{
              __html: html,
            }}
          />
        </div>
      </ScrollArea>
    </div>
    </div>
  );
}

