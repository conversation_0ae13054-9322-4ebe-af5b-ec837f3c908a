"use client";
import ComingSoon from "@/components/ComingSoon";
import { PageHeader } from "@/components/PageHeader";
import { JOB_TYPE } from "@/enums";
import { useApp } from "@/hooks";
import IcAskIcon from "@/public/icons/ic_ask.svg";
import { Button } from "@/ui/button";
import { useRouter } from "next/navigation";

export default function Page() {
  const router = useRouter();
  const { hasJobTypes } = useApp();
  const isClassTeacher = hasJobTypes([
    JOB_TYPE.JOB_TYPE_CLASS_TEACHER,
    JOB_TYPE.JOB_TYPE_SUBJECT_TEACHER,
  ]);
  const gotoAsk = () => {
    router.push("/learning-situation/ask");
  };
  return (
    <div className="relative h-full">
      <PageHeader className="absolute w-full">
        <div className="flex w-full justify-end pr-6">
          {isClassTeacher && (
            <Button
              variant="ghost"
              onClick={gotoAsk}
              className="cursor-pointer border"
            >
              <IcAskIcon className="!w-5.5 !h-5.5" />
              问一问统计
            </Button>
          )}
        </div>
      </PageHeader>

      <ComingSoon />
    </div>
  );
}
