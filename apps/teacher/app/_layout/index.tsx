import React from "react";
import AppLayout from "./AppLayout";
import AuthProvider from "./AuthProvider";
import DeviceProvider from "./DeviceProvider";
import FeedbackProvider from "./feedback/FeedbackProvider";
import "./init-app";

export default function LayoutIndex(props: {
  children: React.ReactNode;
  login: React.ReactNode;
}) {
  return (
    <>
      {/* 设备检测层 */}
      <DeviceProvider>
        {/* 登录状态与权限校验层 */}
        <AuthProvider>
          {/* 用户反馈层 */}
          <FeedbackProvider>
            {/* 布局层 */}
            <AppLayout {...props} />
          </FeedbackProvider>
        </AuthProvider>
      </DeviceProvider>
    </>
  );
}
