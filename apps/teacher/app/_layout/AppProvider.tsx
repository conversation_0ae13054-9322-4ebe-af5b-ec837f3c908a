"use client";

import {
  ACTIVE_USER_EVENT_NAME,
  LOCALSTORAGE_SIDEBAR_STATE_KEY,
  LOCALSTORAGE_USER_INFO_KEY,
} from "@/configs";
import {
  AppContext,
  useActiveHeartbeatSensor,
  useDevice,
  type AppContextProps,
} from "@/hooks";
import { useUserRoles } from "@/hooks/useUserRoles";
import { sensorsManager } from "@/libs";
import { getUserInfo } from "@/services";
import { UserInfo } from "@/types";
import { TooltipProvider } from "@/ui/tooltip";
import { cn } from "@/utils/utils";
import { useSignal } from "@preact-signals/safe-react";
import { getAppInfo } from "@repo/lib/utils/device";
import * as Sentry from "@sentry/nextjs";
import { useLocalStorageState, useRequest } from "ahooks";
import * as React from "react";
import store from "store2";
import AppSidebar from "./app-sidebar";

export default function AppProvider({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const { screenSize, accessChannel } = useDevice();
  // 侧边栏开启
  const open = useSignal(store(LOCALSTORAGE_SIDEBAR_STATE_KEY) ?? true);
  /**
   * 平板设备 statusBarHeight
   */
  const statusBarHeight = useSignal(screenSize?.statusBarHeight ?? 0);
  /**
   * 用户信息
   */
  const [userInfo, setUserInfo] = useLocalStorageState<UserInfo | null>(
    LOCALSTORAGE_USER_INFO_KEY,
    {
      defaultValue: null,
    }
  );

  const [primarySubject, setPrimarySubject] = React.useState<{
    subjectId: number;
    subjectName: string;
  } | null>(null);

  const {
    setJobs,
    hasJobTypes,
    hasJobType,
    computePrimarySubject,
    getRoleSummary,
  } = useUserRoles();

  useRequest(getUserInfo, {
    onSuccess: (res) => {
      setUserInfo(res);
      setJobs(res?.teacherJobInfos || []);
      setPrimarySubject(computePrimarySubject(res?.teacherJobInfos || []));

      const { schoolInfos, teacherJobInfos, ...payload } = res;

      // 添加用户信息
      sensorsManager.setProfile(payload);
      // 触发活跃用户
      sensorsManager.track(ACTIVE_USER_EVENT_NAME);

      const appInfo = getAppInfo();
      const { userID: id, userName: username } = res;
      Sentry.setUser({
        id,
        username,
      });
      appInfo && Sentry.setTag("versionName", appInfo.versionName);
    },
  });

  /**
   *  神策统计，自动触发老师活跃心跳
   */
  useActiveHeartbeatSensor();

  const contextValue = React.useMemo<AppContextProps>(
    () => ({
      statusBarHeight: statusBarHeight.value,
      primarySubject,
      hasJobTypes,
      hasJobType,
      getRoleSummary,
      open,
      setOpen: (value: boolean) => {
        open.value = value;
        store(LOCALSTORAGE_SIDEBAR_STATE_KEY, value);
      },
      userInfo,
    }),
    [
      statusBarHeight.value,
      primarySubject,
      hasJobTypes,
      hasJobType,
      getRoleSummary,
      open,
      userInfo,
    ]
  );

  return (
    <AppContext.Provider value={contextValue}>
      <TooltipProvider delayDuration={0}>
        <div
          data-slot="sidebar-wrapper"
          className={cn(
            "group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar h-full"
          )}
        >
          <div
            className="bg-fill-light flex h-full overflow-hidden"
            style={{
              paddingTop: `${statusBarHeight.value}px`,
            }}
          >
            <AppSidebar
              style={{
                paddingTop: statusBarHeight.value,
                minHeight: screenSize?.height,
              }}
            />

            <main className="flex-1 overflow-hidden">{children}</main>
          </div>
        </div>
      </TooltipProvider>
    </AppContext.Provider>
  );
}
