"use client";
import { ACCESS_CHANNEL } from "@/enums";
import { DeviceContext } from "@/hooks";
import { sensorsManager } from "@/libs";
import { getScreenSize } from "@repo/lib/utils/device";
import { useCreation, useMount } from "ahooks";
import dynamic from "next/dynamic";
import React from "react";

const AppSkeleton = dynamic(
  () => import(/*webpackPrefetch: true*/ "./AppSkeleton"),
  { ssr: false }
);

function DeviceProvider({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  /**
   * 只有 APP 端有值，设备尺寸
   */
  const screenSize = useCreation(() => getScreenSize(), []);
  /**
   * 访问渠道
   */
  const accessChannel = screenSize
    ? ACCESS_CHANNEL.Android
    : ACCESS_CHANNEL.Web;

  useMount(() => {
    sensorsManager.init(accessChannel);
  });

  return (
    <>
      <AppSkeleton />

      <DeviceContext value={{ screenSize, accessChannel }}>
        {children}
      </DeviceContext>
    </>
  );
}

export default DeviceProvider;
