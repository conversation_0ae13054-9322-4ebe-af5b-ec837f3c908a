import { Button } from "@/ui/button";
import { Checkbox } from "@/ui/checkbox";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/popover";
import { ScrollArea } from "@/ui/scroll-area";
import { Skeleton } from "@/ui/skeleton";
import { toast } from "@/ui/toast";
import { cn } from "@/utils/utils";
import {
  ColumnDef,
  ColumnFiltersState,
  RowSelectionState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useVirtualizer } from "@tanstack/react-virtual";
import { ChevronLeft, ChevronRight, Settings } from "lucide-react";
import * as React from "react";
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";

// ============================== 类型定义 ==============================

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export interface TableColumn<T = Record<string, any>> {
  title: React.ReactNode;
  field: keyof T | "operation";
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  render?: (value: any, record: T) => React.ReactNode;
  width?: number;
  align?: "left" | "center" | "right";
  sortable?: boolean;
  fixed?: "left" | "right";
  visible?: boolean;
  className?: string;
  /** 是否在编辑表格弹窗中隐藏此列，true表示不可编辑（隐藏） */
  hideInColumnSetting?: boolean;
}

// 列元数据类型定义，修复类型错误
interface ColumnMeta {
  align?: "left" | "center" | "right";
  className?: string;
  fixed?: "left" | "right";
  sortable?: boolean;
}

interface TableStyles {
  container?: string;
  table?: string;
  tableWrapper?: string;
  header?: string;
  headerRow?: string;
  headerCell?: string;
  row?: string;
  cell?: string;
  toolbar?: string;
  pagination?: string;
  paginationButton?: string;
  empty?: string;
  loading?: string;
}

export interface AdvancedTableProps<T> {
  localKey: string;
  data: T[];
  columns: TableColumn<T>[];
  rowKey?: string | ((record: T) => string);
  pagination?: {
    page?: number;
    pageSize?: number;
    onChange?: (page: number, pageSize?: number) => void;
    total?: number;
    pageSizeOptions?: number[];
    showTotal?: boolean;
    numberMode?: boolean;
  };
  selection?: {
    title?: string;
    selectedKeys?: React.Key[];
    onIsSelected?: (row: T) => boolean;
    onChange?: (selectedKeys: React.Key[], selectedRows: T[]) => void;
    onConfirm?: (selectedKeys: React.Key[], selectedRows: T[]) => void;
    onCancel?: () => void;
    persistKey?: string;
    showToolbar?: boolean;
  };
  singleSelection?: {
    title?: string;
    selectedKey?: React.Key;
    onChange?: (selectedKey: React.Key, selectedRow: T) => void;
    persistKey?: string;
    /** 是否全选模式，当为 true 时所有行都显示为选中状态 */
    selectAll?: boolean;
  };
  toolbar?: React.ReactNode;
  empty?: React.ReactNode;
  loading?: boolean;
  onRowClick?: (record: T) => void;
  className?: string;
  classNames?: TableStyles;
  showColumnSetting?: boolean;
  showToolbar?: boolean;
  height?: string | number;
  virtualized?: boolean;
  onSortingChange?: (sortingState: SortingState) => void;
  skeletonLoading?: boolean;
  skeletonLength?: number;
}

// ============================== 辅助函数 ==============================

const LOCAL_STORAGE_PREFIX = "advanced_table_";

function saveToStorage(key: string, value: unknown): void {
  if (typeof window === "undefined") return;
  try {
    localStorage.setItem(
      `${LOCAL_STORAGE_PREFIX}${key}`,
      JSON.stringify(value)
    );
  } catch (e) {
    console.error("Failed to save to localStorage:", e);
  }
}

function getFromStorage<T>(key: string, defaultValue: T): T {
  if (typeof window === "undefined") return defaultValue;
  try {
    const item = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}${key}`);
    return item ? JSON.parse(item) : defaultValue;
  } catch (e) {
    console.error("Failed to get from localStorage:", e);
    return defaultValue;
  }
}
// ============================== 组件实现 ==============================

// 表格列设置弹窗
export const ColumnSettingPopover = memo(function ColumnSettingPopover<T>({
  localKey,
  columns,
  columnVisibility,
  setColumnVisibility,
}: {
  localKey: string;
  columns: TableColumn<T>[];
  columnVisibility: VisibilityState;
  setColumnVisibility: (state: VisibilityState) => void;
}) {
  const [open, setOpen] = useState(false);
  const [tempVisibility, setTempVisibility] =
    useState<VisibilityState>(columnVisibility);

  useEffect(() => {
    setTempVisibility(columnVisibility);
  }, [open, columnVisibility]);

  const handleConfirm = () => {
    setColumnVisibility(tempVisibility);
    saveToStorage(localKey + "_column_visibility", tempVisibility);
    setOpen(false);
    toast.success("设置已更新");
  };
  const getTempVisibility = useCallback(() => {
    let count = 0;
    const tempVisibilityKeys = { ...tempVisibility };
    delete tempVisibilityKeys["studentId"];
    Object.keys(tempVisibilityKeys).forEach((key) => {
      if (tempVisibilityKeys[key]) {
        count++;
      }
    });
    return count;
  }, [tempVisibility]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "border-line-3 h-7 cursor-pointer gap-1 rounded-[0.375rem] border transition-all",
            open && "border-primary text-primary"
          )}
        >
          <Settings className="h-4 w-4" />
          <span className="mt-[1px] text-xs">编辑表格</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        side="bottom"
        align="end"
        avoidCollisions={false}
        className="w-auto min-w-fit p-0"
      >
        <div className="flex max-h-[40vh] flex-col gap-2 overflow-auto pt-3">
          {columns
            .filter((col) => !col.hideInColumnSetting) // 过滤掉不可编辑的列
            .map((col) => {
              const key = String(col.field);
              return (
                <label
                  key={key}
                  className="flex cursor-pointer items-center gap-2 px-4 py-1"
                >
                  <Checkbox
                    checked={!tempVisibility[key] === false}
                    onCheckedChange={(checked) => {
                      setTempVisibility({
                        ...tempVisibility,
                        [key]: !!checked,
                      });
                    }}
                  />
                  <span className="text-sm font-normal">{col.title}</span>
                </label>
              );
            })}
        </div>
        <div className="mt-2 flex justify-end gap-3 border-t p-3">
          <Button
            variant="outline"
            size="sm"
            className="cursor-pointer rounded-full"
            onClick={() => setOpen(false)}
          >
            取消
          </Button>
          <Button
            variant="default"
            size="sm"
            className="cursor-pointer rounded-full"
            disabled={getTempVisibility() === 0}
            onClick={handleConfirm}
          >
            确认
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
});

// 选择工具栏
const SelectionToolbar = memo(function SelectionToolbar({
  selectedCount,
  onCancel,
  onConfirm,
  className,
}: {
  selectedCount: number;
  onCancel: () => void;
  onConfirm: () => void;
  className?: string;
}) {
  if (selectedCount === 0) return null;

  return (
    <div
      className={cn(
        "flex items-center justify-between rounded-md border border-blue-200 bg-blue-50 p-2",
        className
      )}
    >
      <div className="text-sm text-blue-700">
        已选择 <span className="font-medium">{selectedCount}</span> 项
      </div>
      <div className="flex gap-2">
        <Button variant="outline" size="sm" onClick={onCancel}>
          取消
        </Button>
        <Button variant="default" size="sm" onClick={onConfirm}>
          确认
        </Button>
      </div>
    </div>
  );
});

// 自定义Checkbox组件，包含indeterminate状态
const IndeterminateCheckbox = ({
  checked,
  indeterminate,
  onChange,
  disabled,
}: {
  checked: boolean;
  indeterminate?: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
}) => {
  const ref = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (ref.current) {
      ref.current.indeterminate = !!indeterminate;
    }
  }, [indeterminate]);

  return (
    <input
      type="checkbox"
      ref={ref}
      checked={checked}
      className="text-primary focus:ring-primary h-4 w-4 rounded border-gray-300"
      onChange={onChange}
      disabled={disabled}
    />
  );
};

// 自定义Radio组件
const RadioButton = ({
  checked,
  onChange,
  disabled,
}: {
  checked: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
}) => {
  return (
    <input
      type="radio"
      checked={checked}
      className="text-primary focus:ring-primary h-4 w-4 border-gray-300"
      onChange={onChange}
      disabled={disabled}
    />
  );
};

const GeneratePage = ({
  currentPage,
  totalPages,
  onPageChange,
  pageRangeDisplayed = 7,
  marginPagesDisplayed = 1,
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (index: number) => void;
  pageRangeDisplayed?: number;
  marginPagesDisplayed?: number;
}) => {
  // 处理页码点击
  const handlePageClick = (page: number) => {
    if (page < 1 || page > totalPages) return;
    onPageChange(page - 1);
  };

  // 生成页码数组
  const generatePageNumbers = () => {
    const pages = [];
    const range = [];
    const startPage = Math.max(
      2,
      currentPage - Math.floor(pageRangeDisplayed / 2)
    );
    const endPage = Math.min(
      totalPages - 1,
      currentPage + Math.floor(pageRangeDisplayed / 2)
    );

    // 添加第一页
    if (1 <= totalPages) {
      pages.push(1);
    }

    // 添加第一页后的省略号
    if (startPage > 2) {
      pages.push("...");
    }

    // 添加中间的页码范围
    for (let i = startPage; i <= endPage; i++) {
      range.push(i);
    }
    pages.push(...range);

    // 添加最后一页前的省略号
    if (endPage < totalPages - 1) {
      pages.push("...");
    }

    // 添加最后一页
    if (totalPages > 1) {
      pages.push(totalPages);
    }

    return pages;
  };

  const pageNumbers = generatePageNumbers();

  return (
    <ul className="pagination flex list-none items-center space-x-1 p-0 md:space-x-2">
      {/* 页码按钮 */}
      {pageNumbers.map((pg, index) => (
        <li key={index}>
          {pg === "..." ? (
            <span className="px-3 py-2 text-gray-500">...</span>
          ) : (
            <Button
              variant="outline"
              onClick={() => handlePageClick(pg as number)}
              className={`pagination-btn h-7 w-7 cursor-pointer rounded-md transition-all ${
                currentPage === pg
                  ? "border border-blue-600 bg-blue-500 text-white"
                  : "hover:bg-accent hover:text-accent-foreground border-gray-300 bg-white"
              }`}
            >
              {pg}
            </Button>
          )}
        </li>
      ))}
    </ul>
  );
};
// 分页组件
const Pagination = memo(function Pagination({
  pageIndex,
  pageSize,
  pageCount,
  total,
  onPageChange,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  className,
  buttonClassName,
  showTotal = true,
  numberMode = false,
}: {
  pageIndex: number;
  pageSize: number;
  pageCount: number;
  total: number;
  onPageChange: (page: number) => void;
  onPageSizeChange?: (size: number) => void;
  pageSizeOptions?: number[];
  className?: string;
  buttonClassName?: string;
  showTotal?: boolean;
  numberMode?: boolean;
}) {
  const [inputPage, setInputPage] = useState<string>(
    (pageIndex + 1).toString()
  );

  // 同步页码
  useEffect(() => {
    setInputPage((pageIndex + 1).toString());
  }, [pageIndex]);

  // 跳转页码
  const handleJump = () => {
    let num = parseInt(inputPage, 10);
    if (isNaN(num) || num < 1) num = 1;
    if (num > pageCount) num = pageCount;
    if (num - 1 !== pageIndex) {
      onPageChange(num - 1);
    }
    setInputPage(num.toString());
  };

  return (
    <>
      {numberMode !== true ? (
        <div className={cn("flex items-center justify-between", className)}>
          {showTotal && (
            <div className="text-sm text-gray-500">共 {total} 条</div>
          )}
          <div className="flex items-center gap-2">
            {onPageSizeChange && (
              <select
                className={cn(
                  "rounded border px-2 py-1 text-sm focus:outline-none",
                  buttonClassName
                )}
                value={pageSize}
                onChange={(e) => {
                  const size = parseInt(e.target.value, 10);
                  onPageSizeChange(size);
                  // onPageChange(0); // 重置到第一页
                }}
              >
                {pageSizeOptions.map((opt) => (
                  <option key={opt} value={opt}>
                    {opt} 条/页
                  </option>
                ))}
              </select>
            )}
            <Button
              variant="ghost"
              size="sm"
              className={buttonClassName}
              onClick={() => onPageChange(pageIndex - 1)}
              disabled={pageIndex === 0}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm">
              第
              <input
                type="number"
                min={1}
                max={pageCount}
                value={inputPage}
                onChange={(e) => setInputPage(e.target.value)}
                onBlur={handleJump}
                onKeyDown={(e) => {
                  if (e.key === "Enter") handleJump();
                }}
                className={cn(
                  "mx-1 w-12 rounded border text-center text-sm focus:outline-none",
                  buttonClassName
                )}
                style={{ width: 40 }}
              />
              页 / 共 {pageCount} 页
            </span>
            <Button
              variant="ghost"
              size="sm"
              className={buttonClassName}
              onClick={() => onPageChange(pageIndex + 1)}
              disabled={pageIndex >= pageCount - 1}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ) : (
        <div className={cn("flex items-center justify-end", className)}>
          <div className="flex items-center">
            {showTotal && (
              <div className="mr-2 text-sm text-gray-500">共 {total} 条</div>
            )}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                className={cn("h-7 w-7", buttonClassName)}
                onClick={() => onPageChange(pageIndex - 1)}
                disabled={pageIndex === 0}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <GeneratePage
                currentPage={pageIndex + 1}
                totalPages={pageCount}
                onPageChange={onPageChange}
              />
              <Button
                variant="outline"
                size="sm"
                className={cn("h-7 w-7", buttonClassName)}
                onClick={() => onPageChange(pageIndex + 1)}
                disabled={pageIndex >= pageCount - 1}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              {onPageSizeChange && (
                <select
                  className={cn(
                    "h-7 rounded border px-2 text-sm focus:outline-none",
                    buttonClassName
                  )}
                  value={pageSize}
                  onChange={(e) => {
                    const size = parseInt(e.target.value, 10);
                    onPageSizeChange(size);
                    // onPageChange(0); // 重置到第一页
                  }}
                >
                  {pageSizeOptions.map((opt) => (
                    <option key={opt} value={opt}>
                      {opt} 条/页
                    </option>
                  ))}
                </select>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
});

// 主表格组件

export function DataTable<T extends object = Record<string, unknown>>({
  localKey,
  data,
  columns,
  rowKey = "id",
  pagination,
  selection,
  singleSelection,
  toolbar,
  empty = "暂无数据",
  loading,
  onRowClick,
  className,
  classNames = {},
  showColumnSetting = true,
  showToolbar = true,
  height,
  virtualized = false,
  onSortingChange,
  skeletonLoading,
  skeletonLength,
}: AdvancedTableProps<T>) {
  // =============== 状态管理 ===============
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [singleRowSelection, setSingleRowSelection] = useState<string | null>(
    null
  );
  const [pageIndex, setPageIndex] = useState(pagination?.page || 0);
  const [pageSize, setPageSize] = useState(pagination?.pageSize || 10);

  // 从LocalStorage恢复列可见性设置
  const defaultColumnVisibility = useMemo(() => {
    return columns.reduce<VisibilityState>((acc, col) => {
      acc[col.field.toString()] = col.visible !== false;
      return acc;
    }, {});
  }, [columns]);

  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    getFromStorage("column_visibility", defaultColumnVisibility)
  );

  // 表格容器引用
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // 获取行的唯一键值
  const getRowId = React.useCallback(
    (row: T) => {
      if (typeof rowKey === "function") {
        return rowKey(row);
      }
      return String(row[rowKey as keyof T]);
    },
    [rowKey]
  );

  // 计算表格最小宽度，确保有横向滚动
  const tableMinWidth = useMemo(() => {
    let totalWidth = 0;

    // 如果有选择列，添加宽度
    if (selection) {
      totalWidth += 40; // 选择列宽度
    }

    // 计算所有列的总宽度
    columns.forEach((col) => {
      if (!columnVisibility[String(col.field)] === false) {
        // 只计算可见列
        totalWidth += col.width || 150; // 默认宽度 150px
      }
    });

    // 返回计算的最小宽度，确保至少有足够的宽度触发滚动
    return Math.max(totalWidth, 600); // 至少800px宽，可以根据需要调整
  }, [columns, selection, columnVisibility]);

  // =============== 副作用 ===============

  // 分页改变时重置页码
  // useEffect(() => {
  //   if (pagination) {
  //     setPageIndex(pagination?.page || 0);
  //     setPageIndex(0);
  //   }
  // }, [data.length, pageSize, pagination]);
  useEffect(() => {
    setPageIndex(pagination?.page || 0);
  }, [pagination?.page]);

  // 选择状态持久化
  useEffect(() => {
    if (selection?.persistKey) {
      const selectedKeys = Object.keys(rowSelection).filter(
        (key) => rowSelection[key]
      );
      if (selectedKeys.length > 0) {
        saveToStorage(selection.persistKey, selectedKeys);
      }
    }
  }, [rowSelection, selection?.persistKey]);

  // 从选择键恢复为行选择状态
  useEffect(() => {
    if (selection?.selectedKeys?.length) {
      const newSelection: RowSelectionState = {};
      selection.selectedKeys.forEach((key) => {
        newSelection[String(key)] = true;
      });
      setRowSelection(newSelection);
    } else if (selection?.persistKey) {
      try {
        const savedKeys = getFromStorage<string[]>(selection.persistKey, []);
        if (savedKeys.length) {
          const newSelection: RowSelectionState = {};
          savedKeys.forEach((key) => {
            newSelection[key] = true;
          });
          setRowSelection(newSelection);
        }
      } catch (e) {
        console.error("Failed to restore selection:", e);
      }
    }
  }, [selection?.selectedKeys, selection?.persistKey]);

  // 从单选键恢复为单行选择状态
  useEffect(() => {
    if (singleSelection?.selectedKey) {
      setSingleRowSelection(String(singleSelection.selectedKey));
    } else if (singleSelection?.persistKey) {
      try {
        const savedKey = getFromStorage<string | null>(
          singleSelection.persistKey,
          null
        );
        if (savedKey) {
          setSingleRowSelection(savedKey);
        }
      } catch (e) {
        console.error("Failed to restore single selection:", e);
      }
    } else if (singleSelection?.selectAll) {
      setSingleRowSelection(null);
    }
  }, [
    singleSelection?.selectedKey,
    singleSelection?.persistKey,
    singleSelection?.selectAll,
  ]);

  // 添加排序变化的副作用
  useEffect(() => {
    // 当排序状态改变时，调用外部回调
    // console.log("table sorting", sorting, performance.now());
    if (onSortingChange) {
      onSortingChange(sorting);
    }
  }, [sorting, onSortingChange]);

  // =============== 辅助函数 ===============

  // 获取当前选中的行数据
  const getSelectedRows = React.useCallback(() => {
    const selectedKeys = Object.keys(rowSelection).filter(
      (key) => rowSelection[key]
    );
    return data.filter((item) => selectedKeys.includes(getRowId(item)));
  }, [data, rowSelection, getRowId]);

  // 处理选择确认
  const handleConfirmSelection = () => {
    if (selection?.onConfirm) {
      const selectedKeys = Object.keys(rowSelection)
        .filter((key) => rowSelection[key])
        .map((key) => key);
      selection.onConfirm(selectedKeys, getSelectedRows());
    }
  };

  // 处理选择取消
  const handleCancelSelection = () => {
    setRowSelection({});
    if (selection?.persistKey) {
      saveToStorage(selection.persistKey, []);
    }
    if (selection?.onCancel) {
      selection.onCancel();
    }
  };

  // 处理单选变更
  const handleSingleSelectionChange = React.useCallback(
    (rowId: string) => {
      setSingleRowSelection(rowId);
      if (singleSelection?.persistKey) {
        saveToStorage(singleSelection.persistKey, rowId);
      }
      if (singleSelection?.onChange) {
        const selectedRow = data.find((item) => getRowId(item) === rowId);
        if (selectedRow) {
          singleSelection.onChange(rowId, selectedRow);
        }
      }
    },
    [data, getRowId, singleSelection, setSingleRowSelection]
  );

  // =============== 数据转换 ===============

  // 将自定义列定义转换为TanStack Table的列定义
  const tableColumns = useMemo(() => {
    const result: ColumnDef<T>[] = [];

    // 如果启用了选择功能，添加选择列
    if (selection) {
      result.push({
        id: "selection",
        header: ({ table }) =>
          selection.title ? (
            selection.title
          ) : (
            <IndeterminateCheckbox
              checked={
                table.getPreFilteredRowModel().rows.length > 0 &&
                table.getIsAllRowsSelected()
              }
              indeterminate={
                !table.getIsAllRowsSelected() && table.getIsSomeRowsSelected()
              }
              onChange={table.getToggleAllRowsSelectedHandler()}
            />
          ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={selection.onIsSelected?.(row as T) ?? false}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={() =>
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              selection.onChange && selection.onChange("" as any, row as any)
            }
          />
        ),
        size: 40,
        minSize: 40,
        maxSize: 40,
        meta: {
          align: "center",
        } as ColumnDef<T>["meta"],
        enableSorting: false,
        enableHiding: false,
      });
    }

    // 如果启用了单选功能，添加单选列
    if (singleSelection) {
      result.push({
        id: "selection",
        header: () => singleSelection.title, // 单选不需要表头
        cell: ({ row }) => (
          <RadioButton
            checked={singleRowSelection === row.id}
            onChange={() => handleSingleSelectionChange(row.id)}
            disabled={false}
          />
        ),
        size: 40,
        minSize: 40,
        maxSize: 40,
        meta: {
          align: "center",
        } as ColumnDef<T>["meta"],
        enableSorting: false,
        enableHiding: false,
      });
    }

    // 转换自定义列定义为TanStack列定义
    columns.forEach((col) => {
      if (virtualized) {
        if (!col.width) {
          throw new Error(
            "虚拟滚动模式下，列宽必须设置: " + col.field.toString()
          );
        }
      }
      result.push({
        id: String(col.field),
        accessorKey: col.field,
        header: () => col.title,
        cell: ({ row }) => {
          return (
            col.render?.(row.getValue(String(col.field)), row.original) ||
            row.getValue(String(col.field))
          );
        },
        size: col.width || 150,
        minSize: col.width || 150,
        enableSorting: col.sortable || false,
        meta: {
          align: col.align,
          className: col.className,
          fixed: col.fixed,
          sortable: col.sortable,
        } as ColumnDef<T>["meta"],
      });
    });

    return result;
  }, [
    columns,
    selection,
    singleSelection,
    singleRowSelection,
    handleSingleSelectionChange,
    virtualized,
  ]);

  // 左侧固定列ID
  const leftPinnedColumns = useMemo(() => {
    const result: string[] = [];
    if (selection || singleSelection) result.push("selection");

    columns.forEach((col) => {
      if (col.fixed === "left") {
        result.push(String(col.field));
      }
    });

    return result;
  }, [columns, selection, singleSelection]);

  // 右侧固定列ID
  const rightPinnedColumns = useMemo(() => {
    return columns
      .filter((col) => col.fixed === "right")
      .map((col) => String(col.field));
  }, [columns]);

  // =============== 表格实例 ===============

  const table = useReactTable({
    data,
    columns: tableColumns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination: pagination
        ? {
            pageIndex,
            pageSize,
          }
        : undefined,
      columnPinning: {
        left: leftPinnedColumns,
        right: rightPinnedColumns,
      },
    },
    getRowId: getRowId,
    enableRowSelection: !!selection && !singleSelection, // 禁用行选择如果是单选模式
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: pagination ? getPaginationRowModel() : undefined,
    manualPagination: !!pagination && pagination.total !== undefined,
    pageCount:
      pagination && pagination.total !== undefined
        ? Math.ceil(pagination.total / pageSize)
        : undefined,
  });

  // =============== 虚拟滚动 ===============
  // 如果启用了虚拟滚动
  const { rows } = table.getRowModel();
  const rowVirtualizer = useVirtualizer({
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => 40,
    overscan: 10,
    count: rows.length,
  });

  const virtualRows = rowVirtualizer ? rowVirtualizer.getVirtualItems() : null;

  const totalSize = rowVirtualizer ? rowVirtualizer.getTotalSize() : null;

  const paddingTop = virtualRows ? virtualRows[0]?.start || 0 : 0;

  const paddingBottom = virtualRows
    ? totalSize
      ? totalSize - (virtualRows[virtualRows.length - 1]?.end || 0)
      : 0
    : 0;

  // =============== 渲染 ===============

  // 如果启用了选择功能，处理选择事件
  useEffect(() => {
    // console.log("table rowSelection", rowSelection, performance.now());
    if (selection?.onChange) {
      const selectedKeys = Object.keys(rowSelection)
        .filter((key) => rowSelection[key])
        .map((key) => key);
      selection.onChange(selectedKeys, getSelectedRows());
    }
  }, [rowSelection, selection, getSelectedRows]);

  // 获取页码和分页数据
  const totalRows = pagination?.total ?? data.length;
  const pageCount = pagination
    ? Math.max(1, Math.ceil(totalRows / pageSize))
    : 1;

  // 阴影样式 - 用于固定列的阴影效果
  const leftShadowClass =
    "after:content-[''] after:absolute after:top-0 after:right-0 after:bottom-0 after:w-[6px] after:bg-gradient-to-r after:from-transparent after:to-[rgba(0,0,0,0.1)] after:pointer-events-none after:opacity-0 group-has-[[data-scroll-left='true']]:after:opacity-100 after:transition-opacity after:duration-300";
  const rightShadowClass =
    "before:content-[''] before:absolute before:top-0 before:left-0 before:bottom-0 before:w-[6px] before:bg-gradient-to-l before:from-transparent before:to-[rgba(0,0,0,0.1)] before:pointer-events-none before:opacity-0 group-has-[[data-scroll-right='true']]:before:opacity-100 before:transition-opacity before:duration-300";

  // 检测滚动位置
  const [hasLeftScroll, setHasLeftScroll] = useState(false);
  const [hasRightScroll, setHasRightScroll] = useState(false);

  // 监听滚动事件
  useEffect(() => {
    // console.log("table scroll", performance.now());
    const container = tableContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const hasLeft = container.scrollLeft > 5;
      const hasRight =
        container.scrollLeft <
        container.scrollWidth - container.clientWidth - 5;

      setHasLeftScroll(hasLeft);
      setHasRightScroll(hasRight);

      // 利用 data 属性实现阴影效果
      container.setAttribute("data-scroll-left", hasLeft ? "true" : "false");
      container.setAttribute("data-scroll-right", hasRight ? "true" : "false");
    };

    // 初始检查
    handleScroll();

    // 添加滚动监听
    container.addEventListener("scroll", handleScroll);

    // 添加大小监听，当表格尺寸变化时也检查滚动状态
    const resizeObserver = new ResizeObserver(handleScroll);
    resizeObserver.observe(container);

    return () => {
      container.removeEventListener("scroll", handleScroll);
      resizeObserver.disconnect();
    };
  }, []);

  // CSS 样式 - 固定列阴影效果
  useEffect(() => {
    // 添加全局样式
    // console.log("table shadow", performance.now());
    const styleId = "table-shadow-styles";
    if (!document.getElementById(styleId)) {
      const styleEl = document.createElement("style");
      styleEl.id = styleId;
      styleEl.textContent = `
        /* 左侧最后一个固定列的阴影 */
        .fixed-left-last {
          position: relative;
        }
        .fixed-left-last::before {
          z-index: 100;
          content: '';
          position: absolute;
          right: 0;
          transform: translateX(100%);
          top: 0;
          bottom: 0;
          width: 40px;
          box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.1) !important;
        }
        
        /* 右侧第一个固定列的阴影 */
        .fixed-right-first {
          position: relative;
        }
        .fixed-right-first::after {
          z-index: 100;
          content: '';
          position: absolute;
          left: 0;
          transform: translateX(-100%);
          top: 0;
          bottom: 0;
          width: 40px;
          box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.1) !important;
        }
        
        /* 只在滚动时显示阴影 */
        .table-container[data-scroll-left="false"] .fixed-left-last::before {
          box-shadow: none !important;
        }
        
        .table-container[data-scroll-right="false"] .fixed-right-first::after {
          box-shadow: none !important;
        }
        
      `;
      document.head.appendChild(styleEl);
    }

    return () => {
      const styleEl = document.getElementById(styleId);
      if (styleEl) {
        styleEl.remove();
      }
    };
  }, []);

  // 获取左右固定列的最内侧列ID
  const leftLastColumnId =
    leftPinnedColumns.length > 0
      ? leftPinnedColumns[leftPinnedColumns.length - 1]
      : null;
  const rightFirstColumnId =
    rightPinnedColumns.length > 0 ? rightPinnedColumns[0] : null;
  // console.log("table render", performance.now());
  // 渲染表格
  return (
    <div className={cn("h-full w-full", classNames.container)}>
      {/* 选择工具栏 - 只在多选模式下显示 */}
      {selection?.showToolbar !== false && !singleSelection && (
        <SelectionToolbar
          selectedCount={
            Object.keys(rowSelection).filter((k) => rowSelection[k]).length
          }
          onCancel={handleCancelSelection}
          onConfirm={handleConfirmSelection}
          className={classNames.toolbar}
        />
      )}

      {/* 工具栏 */}
      {showToolbar && (
        <div
          className={cn(
            "mb-4 flex items-center gap-4",
            toolbar ? "justify-between" : "justify-end",
            classNames.toolbar
          )}
        >
          {toolbar}

          {showColumnSetting && (
            <ColumnSettingPopover
              localKey={localKey}
              columns={columns as TableColumn<unknown>[]}
              columnVisibility={columnVisibility}
              setColumnVisibility={setColumnVisibility}
            />
          )}
        </div>
      )}

      {/* 表格区域 */}
      <ScrollArea
        ref={tableContainerRef}
        className={cn(
          "table-container relative h-full w-full !overflow-auto rounded-md border",
          classNames.tableWrapper,
          className
        )}
        style={{
          height: height || "auto",
        }}
        data-scroll-left={hasLeftScroll ? "true" : "false"}
        data-scroll-right={hasRightScroll ? "true" : "false"}
        viewportClassName="!overflow-x-auto"
      >
        <div className="relative">
          <table
            className={cn("w-full border-collapse", classNames.table)}
            style={{
              minWidth: `${tableMinWidth}px`, // 设置最小宽度确保滚动
            }}
          >
            <thead
              className={cn(
                "bg-fill-gray-2 sticky top-0 z-50",
                classNames.header
              )}
            >
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id} className={classNames.headerRow}>
                  {headerGroup.headers.map((header) => {
                    const meta =
                      (header.column.columnDef.meta as ColumnMeta) || {};
                    const isLeft = leftPinnedColumns.includes(header.column.id);
                    const isRight = rightPinnedColumns.includes(
                      header.column.id
                    );
                    const isLastLeftFixed =
                      header.column.id === leftLastColumnId;
                    const isFirstRightFixed =
                      header.column.id === rightFirstColumnId;

                    return (
                      <th
                        key={header.id}
                        colSpan={header.colSpan}
                        className={cn(
                          "bg-fill-gray-2 z-20 h-10 whitespace-nowrap px-4 text-left align-middle text-sm font-medium transition-colors",
                          meta.align === "center" && "text-center",
                          meta.align === "right" && "text-right",
                          isLeft &&
                            "sticky left-0 z-20 shadow-[1px_0_0_0_#e5e7eb]",
                          isRight &&
                            "sticky right-0 z-20 shadow-[-1px_0_0_0_#e5e7eb]",
                          isLastLeftFixed && "fixed-left-last",
                          isFirstRightFixed && "fixed-right-first",
                          isLeft && leftShadowClass,
                          isRight && rightShadowClass,
                          classNames.headerCell
                        )}
                        style={{
                          width: `${header.getSize()}px`,
                          minWidth: `${header.getSize()}px`,
                          maxWidth: `${header.getSize()}px`,
                          position: isLeft || isRight ? "sticky" : undefined,
                          left: isLeft ? `${header.getStart()}px` : undefined,
                          right: isRight ? 0 : undefined, // 修复右侧固定列定位问题
                        }}
                      >
                        {header.isPlaceholder ? null : (
                          <div
                            className={cn(
                              "flex items-center gap-2",
                              meta.align === "center" && "justify-center",
                              meta.align === "right" && "justify-end",
                              !meta.align && "justify-start"
                            )}
                          >
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {header.column.getCanSort() && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 !p-0"
                                onClick={() => header.column.toggleSorting()}
                              >
                                <svg
                                  width="12"
                                  height="12"
                                  viewBox="0 0 16 16"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M7.48067 0.83611C7.37185 0.67782 7.19207 0.583252 6.99998 0.583252C6.80789 0.583252 6.62811 0.67782 6.51929 0.83611L3.31096 5.50278C3.18823 5.68129 3.17453 5.9131 3.27538 6.10482C3.37623 6.29654 3.57502 6.41659 3.79165 6.41659H10.2083C10.4249 6.41659 10.6237 6.29654 10.7246 6.10482C10.8254 5.9131 10.8117 5.68129 10.689 5.50278L7.48067 0.83611Z"
                                    fill={
                                      header.column.getIsSorted() === "asc"
                                        ? "#4A4FED"
                                        : "#838BAB"
                                    }
                                  />
                                  <path
                                    d="M3.79165 7.58325C3.57502 7.58325 3.37623 7.7033 3.27538 7.89502C3.17453 8.08673 3.18823 8.31855 3.31096 8.49706L6.51929 13.1637C6.62811 13.322 6.80789 13.4166 6.99998 13.4166C7.19207 13.4166 7.37185 13.322 7.48067 13.1637L10.689 8.49706C10.8117 8.31855 10.8254 8.08673 10.7246 7.89502C10.6237 7.7033 10.4249 7.58325 10.2083 7.58325H3.79165Z"
                                    fill={
                                      header.column.getIsSorted() === "desc"
                                        ? "#4A4FED"
                                        : "#838BAB"
                                    }
                                  />
                                </svg>
                              </Button>
                            )}
                          </div>
                        )}
                      </th>
                    );
                  })}
                </tr>
              ))}
            </thead>
            {skeletonLoading ? (
              <tbody>
                {/* <div className="p-2 h-[calc(100vh-17rem)]">
              {Array(7)
                .fill(0)
                .map((_, i) => (
                  <div key={i + "table_row"} className="grid grid-cols-7 gap-4 p-2 py-2">
                    {Array(7)
                      .fill(0)
                      .map((_, j) => (
                        <Skeleton key={j + "table_cell"} className="h-6 w-full" />
                      ))}
                  </div>
                ))}
            </div> */}
                {Array(7)
                  .fill(0)
                  .map((_, i) => (
                    <tr key={i + "table_row"} className="p-2 py-2">
                      {Array(skeletonLength || 7)
                        .fill(0)
                        .map((_, j) => (
                          <td key={j + "table_cell"} className="p-2">
                            <Skeleton
                              key={j + "table_cell"}
                              className="h-6 w-full"
                            />
                          </td>
                        ))}
                    </tr>
                  ))}
              </tbody>
            ) : (
              <tbody>
                {loading ? (
                  <tr>
                    <td
                      colSpan={table.getAllColumns().length}
                      className={cn(
                        "h-24 text-center text-gray-500",
                        classNames.loading
                      )}
                    >
                      加载中...
                    </td>
                  </tr>
                ) : table.getRowModel().rows?.length ? (
                  virtualized ? (
                    <>
                      {paddingTop > 0 && (
                        <tr>
                          <td
                            style={{ height: `${paddingTop}px` }}
                            colSpan={table.getAllColumns().length}
                          />
                        </tr>
                      )}
                      {virtualRows!.map((virtualRow) => {
                        const row = rows[virtualRow.index];
                        return (
                          <tr
                            key={row.id}
                            className={cn(
                              "h-[50px] border-b border-b-[#e1e5f2] transition-colors last:border-b-0",
                              onRowClick && "cursor-pointer hover:bg-gray-50",
                              row.getIsSelected() && "bg-blue-50",
                              classNames.row
                            )}
                            onClick={() => onRowClick?.(row.original)}
                          >
                            {row.getVisibleCells().map((cell) => {
                              const isLeft = leftPinnedColumns.includes(
                                cell.column.id
                              );
                              const isRight = rightPinnedColumns.includes(
                                cell.column.id
                              );
                              const isLastLeftFixed =
                                cell.column.id === leftLastColumnId;
                              const isFirstRightFixed =
                                cell.column.id === rightFirstColumnId;
                              const meta =
                                (cell.column.columnDef.meta as ColumnMeta) ||
                                {};

                              return (
                                <td
                                  key={cell.id}
                                  className={cn(
                                    "px-4 py-2",
                                    meta.align === "center" && "text-center",
                                    meta.align === "right" && "text-right",
                                    meta.className,
                                    isLeft &&
                                      "sticky left-0 z-10 bg-white shadow-[1px_0_0_0_#e5e7eb]",
                                    isRight &&
                                      "sticky right-0 z-10 bg-white shadow-[-1px_0_0_0_#e5e7eb]",
                                    row.getIsSelected() && "bg-blue-50",
                                    isLastLeftFixed && "fixed-left-last",
                                    isFirstRightFixed && "fixed-right-first",
                                    isLeft && leftShadowClass,
                                    isRight && rightShadowClass,
                                    classNames.cell
                                  )}
                                  style={{
                                    minWidth: `${cell.column.getSize()}px`,
                                    position:
                                      isLeft || isRight ? "sticky" : undefined,
                                    left: isLeft
                                      ? `${cell.column.getStart()}px`
                                      : undefined,
                                    right: isRight ? 0 : undefined, // 修复右侧固定列定位问题
                                  }}
                                >
                                  {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                  )}
                                </td>
                              );
                            })}
                          </tr>
                        );
                      })}
                      {paddingBottom > 0 && (
                        <tr>
                          <td
                            style={{ height: `${paddingBottom}px` }}
                            colSpan={table.getAllColumns().length}
                          />
                        </tr>
                      )}
                    </>
                  ) : (
                    table.getRowModel().rows.map((row) => (
                      <tr
                        key={row.id}
                        className={cn(
                          "group h-[50px] border-b border-b-[#e1e5f2] transition-colors last:border-b-0",
                          onRowClick && "cursor-pointer hover:bg-gray-50",
                          row.getIsSelected() && "bg-blue-50",
                          classNames.row
                        )}
                        onClick={() => onRowClick?.(row.original)}
                      >
                        {row.getVisibleCells().map((cell) => {
                          const isLeft = leftPinnedColumns.includes(
                            cell.column.id
                          );
                          const isRight = rightPinnedColumns.includes(
                            cell.column.id
                          );
                          const isLastLeftFixed =
                            cell.column.id === leftLastColumnId;
                          const isFirstRightFixed =
                            cell.column.id === rightFirstColumnId;
                          const meta =
                            (cell.column.columnDef.meta as ColumnMeta) || {};

                          return (
                            <td
                              key={cell.id}
                              className={cn(
                                "px-4 py-2",
                                meta.align === "center" && "text-center",
                                meta.align === "right" && "text-right",
                                meta.className,
                                isLeft &&
                                  "sticky left-0 z-10 bg-white shadow-[1px_0_0_0_#e5e7eb] group-hover:bg-gray-50",
                                isRight &&
                                  "sticky right-0 z-10 bg-white shadow-[-1px_0_0_0_#e5e7eb] group-hover:bg-gray-50",
                                (row.getIsSelected() ||
                                  (singleSelection &&
                                    singleRowSelection === row.id)) &&
                                  "bg-blue-50",
                                isLastLeftFixed && "fixed-left-last",
                                isFirstRightFixed && "fixed-right-first",
                                isLeft && leftShadowClass,
                                isRight && rightShadowClass,
                                classNames.cell
                              )}
                              style={{
                                width: "max-content",
                                // width: `${cell.column.getSize()}px`,
                                minWidth: `${cell.column.getSize()}px`,
                                // maxWidth: `${cell.column.getSize()}px`,
                                position:
                                  isLeft || isRight ? "sticky" : undefined,
                                left: isLeft
                                  ? `${cell.column.getStart()}px`
                                  : undefined,
                                right: isRight ? 0 : undefined, // 修复右侧固定列定位问题
                              }}
                            >
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </td>
                          );
                        })}
                      </tr>
                    ))
                  )
                ) : (
                  <tr>
                    <td
                      colSpan={table.getAllColumns().length}
                      className={cn(
                        "h-24 text-center text-gray-500",
                        classNames.empty
                      )}
                    >
                      {empty}
                    </td>
                  </tr>
                )}
              </tbody>
            )}
          </table>
        </div>
      </ScrollArea>

      {/* 分页 */}
      {pagination && (
        <Pagination
          pageIndex={pageIndex}
          pageSize={pageSize}
          pageCount={pageCount}
          total={totalRows}
          onPageChange={(newPage) => {
            setPageIndex(newPage);
            pagination.onChange?.(newPage + 1);
          }}
          onPageSizeChange={
            pagination.pageSizeOptions
              ? (newSize) => {
                  setPageSize(newSize);
                  setPageIndex(0);
                  pagination.onChange?.(1, newSize);
                }
              : undefined
          }
          pageSizeOptions={pagination.pageSizeOptions ?? [10, 20, 50, 100]}
          className={classNames.pagination}
          buttonClassName={classNames.paginationButton}
          showTotal={
            pagination.showTotal === undefined ? true : pagination.showTotal
          }
          numberMode={pagination.numberMode}
        />
      )}
    </div>
  );
}
