"use client";

// import { useTaskContext } from "@/app/homework/[id]/_context/task-context";
import { DataTable, TableColumn } from "@/app/homework/_components/DataTable";
import { DrawerCard } from "@/components/common/drawer-card";
import { ClassResourceResponse } from "@/services/homework";
import { Sheet, SheetContent, SheetTitle } from "@/ui/sheet";
import { Button } from "@/ui/tch-button";
// import { toast } from "@/ui/toast";
import { cn } from "@/utils/utils";
// import { useMount, useRequest, useUpdateEffect } from "ahooks";
import { X } from "lucide-react";
import { useCallback, useMemo, useState } from "react";
// import { useAnswerResults } from "../../Results/store/answers";
import ResourcePreviewer from "@/components/resource/ResourcePreviewer";

interface DrawerContainerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  resourceList: Array<ClassResourceResponse>;
  onConfirm?: () => void;
}

export function DrawerContainer({
  open,
  onOpenChange,
  resourceList,
  // onConfirm,
}: DrawerContainerProps) {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [currentResource, setCurrentResource] = useState<{
    resourceId: string;
    fileType: string;
  } | null>(null);
  const handlePreviewClose = useCallback(() => {
    setPreviewOpen(false);
    setCurrentResource(null);
  }, []);
  const resourceIds = useMemo(() => {
    return currentResource?.resourceId ? [currentResource?.resourceId] : [];
  }, [currentResource]);
  // const mapFileType = (fileType: string) => {
  //   switch (fileType) {
  //     case "文档":
  //       return "document";
  //     case "图片":
  //       return "image";
  //     case "视频":
  //       return "video";
  //     case "音频":
  //       return "audio";
  //     default:
  //       return "unknown";
  //   }
  // };
  // const resourceType = useMemo(() => {
  //   return mapFileType(currentResource?.fileType || "");
  // }, [currentResource]);
  // 取消选择，重置临时状态并关闭抽屉
  const handleCancel = () => {
    onOpenChange(false);
  };

  const generateResourceBtn = (
    resourceInfo: Array<{
      resourceId: string;
      duration: number;
      fileType: string;
    }>
  ) => {
    const temp: { [key: string]: string[] } = {};
    for (let i = 0; i < resourceInfo.length; i++) {
      if (temp[resourceInfo[i].fileType]) {
        temp[resourceInfo[i].fileType].push(resourceInfo[i].resourceId);
      } else {
        temp[resourceInfo[i].fileType] = [resourceInfo[i].resourceId];
      }
    }
    return Object.keys(temp).map((key, index) => {
      return temp[key].map((id, idx) => (
        <Button
          key={`${index}_${idx}`}
          type="text"
          className="text-primary-1 h-auto cursor-pointer p-0 font-normal"
          onClick={(e) => {
            e.stopPropagation();
            setCurrentResource({ resourceId: id, fileType: key });
            // previewRecourse(record);
            setPreviewOpen(true);
          }}
        >
          {`${key}${idx + 1}`}
        </Button>
      ));
    });
  };

  // 定义表格列
  const columns: TableColumn<ClassResourceResponse>[] = [
    {
      title: "课程名称",
      field: "taskName",
      width: 60,
      align: "center",
      render: (value) => (
        <div className="max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap text-left text-lg font-medium">
          {value}
        </div>
      ),
    },
    {
      title: "完成进度",
      field: "progressRate",
      align: "center",
      width: 60,
      // render: (value) => `${value}%`,
      render: (value) => (
        <div className="inline-flex items-center justify-center gap-2">
          <div className="bg-fill-gray-2 h-2 w-24 rounded-full">
            <div
              className={`bg-primary-2 h-2 rounded-full`}
              style={{ width: `${Math.ceil(value * 100)}%` }}
            />
          </div>
          <span className={"text-gray-2 text-xs font-normal"}>
            {Math.ceil(value * 100)}%
          </span>
        </div>
      ),
    },
    {
      title: "平均用时",
      field: "averageDuration",
      align: "center",
      width: 60,
      render: (val) => {
        const value = Number(val / 1000);
        const hours = Math.floor(value / 3600);
        const minutes = Math.ceil((value % 3600) / 60);
        return (
          <div className="text-gray-1 font-normal">
            {hours > 0 ? `${hours}小时 ${minutes}分钟` : `${minutes}分钟`}
          </div>
        );
      },
    },
    {
      title: "查看资源",
      field: "operation",
      align: "center",
      // fixed: "right",
      width: 80,
      render: (_, record) => (
        <>
          <div className="flex min-w-[100px] max-w-[200px] flex-wrap gap-2">
            {generateResourceBtn(record.resourceInfo)}
          </div>
        </>
      ),
    },
  ];

  return (
    <>
      {/* 资源预览组件 */}
      <ResourcePreviewer
        className="z-100 pointer-events-auto"
        visible={previewOpen}
        onClose={handlePreviewClose}
        resourceIds={resourceIds}
      />
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetTitle>{null}</SheetTitle>
        <SheetContent
          side="right"
          className={cn(
            "flex flex-col gap-0 overflow-y-auto bg-[#F4F7FE] p-0 px-6 sm:max-w-[72vw]"
          )}
          closeable={false}
          onPointerDownOutside={(e) => {
            console.log("onPointerDown");
            e.preventDefault();
          }}
          onInteractOutside={(e) => {
            console.log("onInteractOutside");
            e.preventDefault();
          }}
        >
          <div className="flex items-center justify-between py-5">
            <div className="flex items-center gap-[0.625rem]">
              {/* <div
              className="flex cursor-pointer items-center justify-center"
              onClick={handleCancel}
            >
              <ChevronLeft className="h-5 w-5 text-[#444963]" />
            </div> */}
              <span className="text-xl font-semibold text-[#444963]">
                学习情况
              </span>
            </div>
            <div
              className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full"
              onClick={handleCancel}
            >
              <X className="h-5 w-5" />
            </div>
          </div>
          <div className="flex-1 overflow-y-auto pb-[4.5rem]">
            {
              <>
                <DrawerCard className="border-line-1 w-full overflow-hidden border">
                  <DataTable
                    localKey="courses-table"
                    data={resourceList}
                    columns={columns}
                    rowKey="resourceId"
                    //   toolbar={customToolbar}
                    showColumnSetting={false}
                    className="w-full rounded-[0.25rem_0.25rem_0_0] border-none bg-white"
                    classNames={{
                      container: "bg-white relative",
                      header: "!bg-white",
                      headerRow: "border-none text-gray-4 !bg-white",
                      headerCell: "text-gray-4 !font-normal bg-white text-xs",
                      cell: "text-sm py-3 text-sm",
                    }}
                  />
                </DrawerCard>
              </>
            }
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
