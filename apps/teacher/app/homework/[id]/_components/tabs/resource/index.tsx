"use client";
import { useTaskContext } from "@/app/homework/[id]/_context/task-context";
import { PageHeader } from "@/components/PageHeader";
import { ScrollArea } from "@/ui/scroll-area";
import { InputSearch } from "@/ui/searchInput";
import { Separator } from "@/ui/separator";
// import { umeng, UmengCategory, UmengHomeworkAction } from "@/utils";
import {
  ColumnSettingPopover,
  TableColumn,
} from "@/app/homework/_components/DataTable";
import {
  ClassResourceResponse,
  getClassResourceTaskReport,
  getTaskResourceDetail,
  TaskResourceStudent,
} from "@/services/homework";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/dropdown-menu";
import Avatar from "@/ui/tch-avatar";
import { toast } from "@/ui/toast";
import { cn } from "@/utils";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { SortingState, VisibilityState } from "@tanstack/react-table";
import { useMount, useRequest } from "ahooks";
import { format } from "date-fns";
import { ChevronDown } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useMemo, useState } from "react";
import { LazyDataTable } from "../Report";
import TableSkeleton from "../Report/components/table-skeleton";
import { DrawerContainer } from "./component/resource-select-drawer";
export default function Resource() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const assignId = searchParams.get("assignId");
  const source = searchParams.get("source");
  // 使用signal管理状态
  const searchTermSignal = useSignal("");
  // 添加排序状态
  const sortBySignal = useSignal<
    // | "studyScore"
    | "progress"
    // | "accuracyRate"
    // | "answerCount"
    | "duration"
    | undefined
  >(undefined);
  const sortTypeSignal = useSignal<"asc" | "desc" | undefined>(undefined);
  const {
    // loading,
    studentData,
    homeworkData,
    // taskData,
    // updateStudentData,
    // taskType,
    currentCourse,
    // useFetchHomeworkDetailRequest,
    // setStudentListMap,
    // showStudentDetail,
    assignId: assignIdSignal,
    // taskId,
    // classData,
    // selectedCourseList,
    getStudentAvatar,
    getStudentName,
    currentClass,
    setCurrentClass,
    classList,
    useFetchTaskDetailRequest,
  } = useTaskContext();
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    studentId: true,
    // tags: true,
    progress: true,
    // accuracyRate: true,
    // difficultyDegree: false,
    // incorrectNum: true,
    duration: true,
    // operation: true,
  });
  const data = useComputed(() => studentData.value);
  const publishTime = useComputed(() =>
    format((data.value.publishTime || 0) * 1000, "yyyy.MM.dd HH:mm")
  );
  const deadline = useComputed(() =>
    format((data.value.deadline || 0) * 1000, "yyyy.MM.dd HH:mm")
  );
  // const [isInit, setIsInit] = useState(false);
  // const {
  //   data: pollingData,
  //   loading: pollingLoading,
  //   run,
  //   cancel,
  // } = useFetchHomeworkDetailRequest();
  // const [classDataList, setClassDataList] = useState<
  //   Array<{
  //     taskId: number;
  //     assignId: number;
  //     classId: number;
  //     className: string;
  //   }>
  // >([]);
  const [resourceData, setResourceData] = useState<Array<TaskResourceStudent>>(
    []
  );
  const isClassOpen = useSignal(false);
  const { run: handleRunTaskDetail } = useFetchTaskDetailRequest();
  const [resourceList, setResourceList] = useState<
    Array<ClassResourceResponse>
  >([]);
  const { run: handleRunResource } = useRequest(
    async () => {
      const params = {
        assignIds: [assignIdSignal.value ? Number(assignIdSignal.value) : 0],
      };

      return getClassResourceTaskReport(params);
    },
    {
      manual: true,
      throttleWait: 1000,
      onError: (err) => {
        if (!err?.message) return;
        toast.error("获取资源失败", {
          description: `错误原因：${err.message}`,
        });
      },
      onSuccess: (data) => {
        setResourceList(data);
      },
    }
  );
  // const { run: teacherClassRun } = useRequest(
  //   async () => {
  //     const params = {
  //       taskId: taskId.value,
  //       classes: classList.value.map((cls) => {
  //         return {
  //           classId: cls.classId,
  //           className: cls.className,
  //         };
  //       }),
  //     };
  //     return getTeacherTaskClassList(params);
  //   },
  //   {
  //     manual: true,
  //     debounceWait: 500,
  //     onError: (err) => {
  //       toast.error("获取班级失败");
  //       console.log("获取班级数据失败", err);
  //     },
  //     onSuccess: (data: any) => {
  //       setClassDataList(data.data || []);
  //     },
  //   }
  // );
  const { loading: loadingResource, run: runResource } = useRequest(
    async () => {
      const params = {
        assignId: assignIdSignal.value ? Number(assignIdSignal.value) : 0,
      };
      return getTaskResourceDetail(params);
    },
    {
      manual: true,
      debounceWait: 500,
      onError: (err) => {
        toast.error("获取资源详情失败");
        console.log("获取资源数据失败", err);
      },
      onSuccess: (data) => {
        setResourceData(data[0].studentReport);
      },
    }
  );

  useMount(() => {
    // loading.value = true;
    assignIdSignal.value = assignId || "";
    // run();
    handleRunTaskDetail();
    runResource();
    handleRunResource();
    // teacherClassRun();
  });
  // useUnmount(() => {
  //   cancel();
  // });
  // useUpdateEffect(() => {
  //   if (classData.value.taskId && classData.value.assignId) {
  //     run();
  //   }
  // }, [classData.value, assignIdSignal.value]);
  // useUpdateEffect(() => {
  //   if (pollingData) {
  //     homeworkData.value = pollingData;
  //     setStudentListMap(
  //       pollingData.detail.studentReports as unknown as StudentBase[]
  //     );
  //   }
  // }, [pollingData]);

  // useUpdateEffect(() => {
  //   if (!isInit) {
  //     setIsInit(true);
  //     if (pollingLoading) {
  //       loading.value = true;
  //     } else {
  //       loading.value = false;
  //     }
  //   } else {
  //     loading.value = false;
  //   }
  // }, [pollingLoading]);

  // 过滤学生列表（基于搜索词）- 只在 report 页面时计算
  const filteredStudents = useComputed(() => {
    // 如果不在 report 页面，返回空数组避免不必要的计算
    return (
      homeworkData.value?.detail.studentReports
        ?.filter((student) =>
          getStudentName(student.studentId).includes(searchTermSignal.value)
        )
        .map((student) => ({
          ...student,
          studentName: getStudentName(student.studentId),
          avatar: getStudentAvatar(student.studentId),
        })) || []
    );
  });

  const handleBackNavigation = () => {
    if (source === "assign") {
      router.push("/assign");
    } else {
      router.push("/homework");
    }
    // router.back();
  };

  // 班级选择处理函数
  const handleSelectClass = useCallback(
    (classItem: { id: number; name: string }) => {
      // 只有当选择不同的班级时才更新
      if (currentClass.peek()?.id !== classItem.id) {
        setCurrentClass(classItem);

        // DONE: 埋点12 => `homework_list_report_class_click` 作业报告页中点切换班级
        // umeng.trackEvent(
        //   UmengCategory.HOMEWORK,
        //   UmengHomeworkAction.REPORT_CLASS_SELECT_CLICK,
        //   {
        //     eventName: "作业报告页中点切换班级",
        //   }
        // );
      }
    },
    [currentClass, setCurrentClass]
  );

  // const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // 获取班级列表
  // const classList =
  //   taskData.value?.homeworkData?.reports.map((report) => ({
  //     id: report.assignObject.id,
  //     name: report.assignObject.name,
  //     gradeName: report.assignObject.gradeName,
  //   })) || [];

  const classSelector = (
    <div className="flex items-center gap-3 text-sm">
      {/* 课程选择器 - 无论在哪种视图下都显示（如果有的话） */}
      <DropdownMenu
        onOpenChange={(open) => {
          isClassOpen.value = open;
        }}
      >
        <DropdownMenuTrigger asChild>
          <div
            className="flex cursor-pointer items-center rounded-full border border-[#e9ecf5] px-4 py-2"
            onClick={() => {
              // DONE: 埋点12 => `homework_list_report_class_click` 作业报告页中点切换班级
              // umeng.trackEvent(
              //   UmengCategory.HOMEWORK,
              //   UmengHomeworkAction.REPORT_CLASS_SELECT_CLICK,
              //   {
              //     eventName: "作业报告页中点切换班级",
              //   }
              // );
            }}
          >
            <span className={cn("text-gray-2 mr-1 font-medium")}>班级：</span>
            <span className={cn("text-gray-2 mr-1 font-medium")}>
              {currentClass.value?.name}
            </span>
            <ChevronDown
              className={cn(
                "text-gray-2 h-4 w-4",
                isClassOpen.value && "rotate-180"
              )}
            />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="max-h-80 w-24 overflow-y-auto rounded-xl border-none bg-white shadow-[0px_16px_56px_0px_rgba(16,18,25,0.08)]"
        >
          {classList.value.map((classItem) => (
            <DropdownMenuItem
              key={classItem.classId}
              className={`flex items-center gap-2 rounded-[0.625rem] px-4 py-3 text-sm leading-[150%] ${currentClass.value?.id === classItem.classId ? "bg-primary-6 text-primary-1" : "text-gray-2 font-normal"}`}
              onClick={() => {
                handleSelectClass({
                  id: classItem.classId,
                  name: classItem.className,
                });
                assignIdSignal.value = classItem.assignId.toString();
                handleRunResource();
                runResource();
              }}
            >
              {classItem.className}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
      <div
        className="flex cursor-pointer items-center rounded-full border border-[#e9ecf5] px-4 py-2"
        onClick={() => {
          setDrawerOpen(true);
        }}
      >
        <span className="text-gray-2 mr-1 font-medium">课程：</span>
        <span className="text-gray-2 mr-1 font-medium">
          {currentCourse.value?.name || "1节课"}
        </span>
        <ChevronDown className="h-4 w-4" />
      </div>
    </div>
  );
  const [drawerOpen, setDrawerOpen] = useState(false);

  const classStat = useComputed(() => {
    return {
      classProgress: homeworkData.value
        ? `${Math.ceil(homeworkData.value.detail.avgProgress * 100)}`
        : "0",
      averageAccuracy: homeworkData.value
        ? `${Math.floor(homeworkData.value.detail.avgAccuracy * 100)}`
        : "0",
      averageTime: homeworkData.value
        ? `${homeworkData.value.detail.avgCostTime} 分钟`
        : "0 分钟",
    };
  });

  // 资源页学生列表（基于搜索词）
  const filteredResourceStudents = useMemo(() => {
    if (!resourceData?.length) return [] as TaskResourceStudent[];
    const term = (searchTermSignal.value || "").trim();
    if (!term) return resourceData;
    return resourceData.filter((s) => s.studentName.includes(term));
  }, [resourceData, searchTermSignal.value]);

  // 处理排序变化
  const handleSortChange = useCallback(
    (field: string | undefined, direction: "asc" | "desc" | undefined) => {
      // 将字段名映射到API所需的排序字段
      let sortBy: // | "studyScore"
      | "progress"
        // | "accuracyRate"
        // | "answerCount"
        | "duration"
        | undefined = undefined;

      if (field) {
        // 根据列字段名映射为API所需的排序字段
        switch (field) {
          // case "studyScore":
          //   sortBy = "studyScore";
          //   break;
          case "progress":
            sortBy = "progress";
            break;
          // case "accuracyRate":
          //   sortBy = "accuracyRate";
          //   break;
          // case "incorrectNum": // 映射为answerCount
          //   sortBy = "answerCount";
          //   break;
          // case "costTime":
          //   sortBy = "costTime";
          //   break;
          case "duration": // 映射接口字段为 costTime
            sortBy = "duration";
            break;
          default:
            sortBy = undefined;
        }
      }

      // 更新排序状态信号
      sortBySignal.value = sortBy;
      sortTypeSignal.value = direction;

      console.log(`排序变更: ${sortBy} ${direction}`);
    },
    [sortBySignal, sortTypeSignal]
  );

  // 定义表格列 - 移除对 filteredStudents.value 的依赖
  const columns: TableColumn<TaskResourceStudent>[] = useMemo(
    () => [
      {
        title: "学生",
        field: "studentId",
        fixed: "left",
        width: 5.5 * 16,
        hideInColumnSetting: true, // 学生列不出现在编辑表格中
        render: (_, record) => {
          return (
            <div className="flex w-full items-center gap-2">
              <Avatar
                src={record.studentAvatar}
                alt={record.studentName}
                className="h-6 w-6 cursor-pointer rounded-full"
              />
              <span className="text-gray-1 whitespace-nowrap text-sm font-normal">
                {record.studentName}
              </span>
            </div>
          );
        },
      },
      {
        title: "完成进度",
        field: "progress",
        align: "left",
        sortable: true,
        width: 5.5 * 16,
        render: (_, record) => (
          <div className="flex inline-flex w-full items-center justify-start justify-center gap-2">
            <div className="bg-fill-gray-2 h-2 w-24 flex-1 rounded-full">
              <div
                className={`bg-primary-2 h-2 rounded-full`}
                style={{ width: `${Math.ceil(record.progress * 100)}%` }}
              />
            </div>
            <span className={"text-gray-2 text-xs font-normal"}>
              {Math.ceil(record.progress * 100)}%
            </span>
          </div>
        ),
      },
      // {
      //   title: "正确率",
      //   field: "accuracyRate",
      //   align: "left",
      //   sortable: true,
      //   width: 5.5 * 16,
      //   visible: false,
      //   // 向下取整不保留小数
      //   render: (value: number) => `${Math.floor(value * 100 || 0)}%`,
      // },
      // {
      //   title: "答题难度",
      //   field: "difficultyDegree",
      //   align: "left",
      //   width: 5.5 * 16,
      //   visible: false,
      //   render: (value) =>
      //     questionDifficultEnumManager.getLabelByValue(
      //       value as QUESTION_DIFFICULT
      //     ),
      // },
      // {
      //   title: "错题/答题",
      //   field: "incorrectNum",
      //   align: "left",
      //   sortable: true,
      //   width: 5.5 * 16,
      //   visible: false,
      //   render: (_, record) => `${record.incorrectNum} / ${record.answerNum}`,
      // },
      {
        title: "用时",
        field: "duration",
        align: "left",
        width: 5.5 * 16,
        sortable: true,
        // x小时 x 分钟，不够1小时显示x分钟
        render: (val) => {
          const value = Number(val / 1000);
          const hours = Math.floor(value / 3600);
          const minutes = Math.ceil((value % 3600) / 60);
          return hours > 0 ? `${hours}小时 ${minutes}分钟` : `${minutes}分钟`;
        },
      },
    ],
    [] // 移除 handleViewStudent，因为它会重新创建
  );

  // 自定义工具栏 - 使用 useMemo 缓存
  const customToolbar = useMemo(
    () => (
      <div className="flex w-full items-center justify-between gap-2">
        <div className="flex items-center gap-4">
          <div className="whitespace-nowrap text-base font-semibold leading-[150%] text-[#202131]">
            学生列表
          </div>

          <div className="relative w-full max-w-sm">
            <InputSearch
              placeholder="搜索学生姓名"
              className="h-7 w-[15rem]"
              debounce={true}
              wait={300}
              value={searchTermSignal.value}
              onChange={(e) => (searchTermSignal.value = e.target.value)}
            />
          </div>
        </div>
      </div>
    ),
    [searchTermSignal.value]
  );

  const handleSortingChange = useCallback(
    (sortingState: SortingState) => {
      if (sortingState.length === 0) {
        // 没有排序时清空排序状态
        handleSortChange(undefined, undefined);
      } else {
        // 从排序信息中获取字段和方向
        const [{ id, desc }] = sortingState;
        handleSortChange(id, desc ? "desc" : "asc");
      }
    },
    [handleSortChange]
  );

  // 延迟渲染状态
  const [shouldRenderTable, setShouldRenderTable] = useState(true);

  // 计算延迟时间：数据量越大，延迟越短（因为用户更期待看到数据）
  const getDelayTime = useCallback(() => {
    const dataLength = filteredStudents.value.length;
    if (dataLength === 0) return 200; // 无数据时延迟较长
    if (dataLength < 10) return 150; // 少量数据
    if (dataLength < 50) return 100; // 中等数据量
    return 50; // 大量数据时快速渲染
  }, [filteredStudents.value.length]);

  // 延迟渲染 DataTable 组件
  const memoizedDataTable = useMemo(() => {
    if (!shouldRenderTable) {
      return null;
    }

    return (
      <LazyDataTable
        localKey="homework-report-table"
        height={"calc(100vh-19rem)"}
        data={filteredResourceStudents}
        columns={columns as unknown as TableColumn<object>[]}
        rowKey="studentId"
        toolbar={customToolbar}
        className="!h-[calc(100vh-14rem)] border-none bg-white"
        classNames={{
          container:
            "bg-white relative py-4 px-5 rounded-[0.75rem] border border-line-1",
          header: "bg-fill-gray-2", // 移除sticky相关样式，使用属性控制
          headerRow: "border-none ",
          row: "hover:bg-gray-50 transition-colors",
          empty: "text-gray-500 italic",
          loading: "text-primary animate-pulse",
          cell: "text-[0.875rem] text-gray-1 font-normal leading-[150%]",
        }}
        onSortingChange={handleSortingChange}
        skeletonLoading={loadingResource}
        skeletonLength={3}
      />
    );
  }, [
    shouldRenderTable,
    filteredResourceStudents,
    columns,
    customToolbar,
    loadingResource,
    handleSortingChange,
  ]);

  return (
    <ScrollArea className="h-full [&>.scroll-area-viewport>div:first-child]:w-full [&>.scroll-area-viewport>div:first-child]:table-fixed">
      <div className={`bg-fill-light absolute h-full w-full`}>
        <div className="z-0 shrink-0 px-6">
          <PageHeader
            onBack={handleBackNavigation}
            needBack={true}
            className="pl-0"
          >
            <div className="flex flex-1 flex-col pl-4">
              {/* 顶部导航栏 - 三部分结构 */}
              <div className="h-17.5 flex items-center justify-between py-3">
                <div className="flex flex-1 items-center gap-4">
                  {/* 2. 标题部分 - 上下结构 */}
                  <div className="flex flex-col items-start justify-start">
                    <h1 className="text-gray-1 text-left text-[1.125rem] font-semibold leading-[150%]">
                      <div className="flex items-center">
                        {data.value.title}
                      </div>
                    </h1>
                    {/* 详细信息区域 - 放在标题下方 */}
                    <div className="flex justify-start">
                      <div className="text-gray-4 inline-flex items-center gap-2 text-[0.75rem] font-normal leading-[150%]">
                        <div className="flex items-center">
                          <span className="text-gray-4">发布时间:</span>
                          <span className="ml-2 font-normal">
                            {publishTime.value}
                          </span>
                        </div>
                        <Separator
                          orientation="vertical"
                          className="bg-line-2 !h-2 w-[1px]"
                        />

                        <div className="flex items-center">
                          <span className="text-gray-4">截止时间:</span>
                          <span className="ml-2 font-normal">
                            {deadline.value}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 3. 右侧按钮区域 */}

                <div className="flex items-center gap-3">{classSelector}</div>
              </div>
            </div>
          </PageHeader>
          <>
            {/* 课程选择抽屉 */}
            <DrawerContainer
              open={drawerOpen}
              onOpenChange={(open) => setDrawerOpen(open)}
              resourceList={resourceList}
            />
          </>
        </div>
        <div className="flex-1 p-4 will-change-transform">
          {!shouldRenderTable ? (
            <div className="border-line-1 relative flex h-[calc(100vh-14rem)] flex-col rounded-[0.75rem] border bg-white px-5 py-4">
              <div className="mb-4 flex justify-between">
                {customToolbar}
                <ColumnSettingPopover
                  columns={columns as unknown as TableColumn<unknown>[]}
                  localKey="homework-report-table"
                  columnVisibility={columnVisibility}
                  setColumnVisibility={setColumnVisibility}
                />
              </div>
              <TableSkeleton
                columns={columns as unknown as TableColumn[]}
                classNames={{
                  header: "bg-fill-gray-2",
                  headerRow: "border-none ",
                }}
              />
            </div>
          ) : (
            memoizedDataTable
          )}
        </div>
      </div>
    </ScrollArea>
  );
}
