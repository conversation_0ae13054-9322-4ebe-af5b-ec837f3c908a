"use client";

import ComingSoon from "@/components/ComingSoon";
import { useUmeng } from "@/hooks/useUmeng";
import { UmengCategory } from "@/utils";
import { useTaskContext } from "../../../_context/task-context";

export default function Ask() {
  const { viewMode } = useTaskContext();
  useUmeng(
    UmengCategory.HOMEWORK,
    viewMode.value === "student"
      ? "homework_list_report_student_detail_ask_tab"
      : "homework_list_report_ask_tab"
  );
  return (
    // {<div className="align-center pt-50 flex flex-col justify-center p-4">
    //   <ComingSoon />
    // </div>}
    <ComingSoon />
  );
}
