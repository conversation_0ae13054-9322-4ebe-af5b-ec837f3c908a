"use client";
import { But<PERSON> } from "@/ui/button";
import fetcher from "@repo/lib/utils/fetcher";

const NEXT_PUBLIC_API_HOST = process.env.NEXT_PUBLIC_API_HOST || "";

const handler = () => {
  fetcher(
    NEXT_PUBLIC_API_HOST + "/internal/api/v1/utils/classroom-id/validate",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        classroomId: "86976214480193239",
        type: "teacher",
      }),
    }
  );
};

export default function page() {
  return (
    <div className="flex size-full items-center justify-center">
      <Button onClick={handler}>测试</Button>
    </div>
  );
}
