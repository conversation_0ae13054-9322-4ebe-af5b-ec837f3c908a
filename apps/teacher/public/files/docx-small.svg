<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1047_7792)">
<path d="M4 5C4 3.34315 5.34315 2 7 2H15L20 7V19C20 20.6569 18.6569 22 17 22H7C5.34315 22 4 20.6569 4 19V5Z" fill="#2091FF"/>
<path d="M15 5.5V2L20 7H16.5C15 7 15 6.25 15 5.5Z" fill="white" fill-opacity="0.3"/>
<g filter="url(#filter0_d_1047_7792)">
<path d="M4.86732 12.46H6.03732C6.32066 12.46 6.58899 12.4917 6.84232 12.555C7.09899 12.6183 7.32232 12.7217 7.51232 12.865C7.70232 13.005 7.85232 13.1883 7.96232 13.415C8.07566 13.6417 8.13232 13.9167 8.13232 14.24C8.13232 14.5267 8.07732 14.78 7.96732 15C7.86066 15.2167 7.71566 15.4 7.53232 15.55C7.34899 15.6967 7.13732 15.8083 6.89732 15.885C6.65732 15.9617 6.40566 16 6.14232 16H4.86732V12.46ZM5.64732 15.28H6.05232C6.23232 15.28 6.39899 15.2617 6.55232 15.225C6.70899 15.1883 6.84399 15.1283 6.95732 15.045C7.07066 14.9583 7.15899 14.8467 7.22232 14.71C7.28899 14.57 7.32232 14.4 7.32232 14.2C7.32232 14.0267 7.28899 13.8767 7.22232 13.75C7.15899 13.62 7.07232 13.5133 6.96232 13.43C6.85232 13.3467 6.72232 13.285 6.57232 13.245C6.42566 13.2017 6.27066 13.18 6.10732 13.18H5.64732V15.28ZM8.47127 14.23C8.47127 13.9467 8.51794 13.69 8.61127 13.46C8.70794 13.23 8.84127 13.035 9.01127 12.875C9.18127 12.7117 9.38294 12.5867 9.61627 12.5C9.8496 12.4133 10.1046 12.37 10.3813 12.37C10.6579 12.37 10.9129 12.4133 11.1463 12.5C11.3796 12.5867 11.5813 12.7117 11.7513 12.875C11.9213 13.035 12.0529 13.23 12.1463 13.46C12.2429 13.69 12.2913 13.9467 12.2913 14.23C12.2913 14.5133 12.2429 14.77 12.1463 15C12.0529 15.23 11.9213 15.4267 11.7513 15.59C11.5813 15.75 11.3796 15.8733 11.1463 15.96C10.9129 16.0467 10.6579 16.09 10.3813 16.09C10.1046 16.09 9.8496 16.0467 9.61627 15.96C9.38294 15.8733 9.18127 15.75 9.01127 15.59C8.84127 15.4267 8.70794 15.23 8.61127 15C8.51794 14.77 8.47127 14.5133 8.47127 14.23ZM9.28127 14.23C9.28127 14.3967 9.30627 14.55 9.35627 14.69C9.4096 14.8267 9.4846 14.9467 9.58127 15.05C9.67794 15.15 9.79294 15.2283 9.92627 15.285C10.0629 15.3417 10.2146 15.37 10.3813 15.37C10.5479 15.37 10.6979 15.3417 10.8313 15.285C10.9679 15.2283 11.0846 15.15 11.1813 15.05C11.2779 14.9467 11.3513 14.8267 11.4013 14.69C11.4546 14.55 11.4813 14.3967 11.4813 14.23C11.4813 14.0667 11.4546 13.915 11.4013 13.775C11.3513 13.635 11.2779 13.515 11.1813 13.415C11.0846 13.3117 10.9679 13.2317 10.8313 13.175C10.6979 13.1183 10.5479 13.09 10.3813 13.09C10.2146 13.09 10.0629 13.1183 9.92627 13.175C9.79294 13.2317 9.67794 13.3117 9.58127 13.415C9.4846 13.515 9.4096 13.635 9.35627 13.775C9.30627 13.915 9.28127 14.0667 9.28127 14.23ZM15.1364 13.38C15.0598 13.2867 14.9648 13.215 14.8514 13.165C14.7414 13.115 14.6131 13.09 14.4664 13.09C14.3198 13.09 14.1831 13.1183 14.0564 13.175C13.9331 13.2317 13.8248 13.3117 13.7314 13.415C13.6414 13.515 13.5698 13.635 13.5164 13.775C13.4664 13.915 13.4414 14.0667 13.4414 14.23C13.4414 14.3967 13.4664 14.55 13.5164 14.69C13.5698 14.8267 13.6414 14.9467 13.7314 15.05C13.8214 15.15 13.9264 15.2283 14.0464 15.285C14.1698 15.3417 14.3014 15.37 14.4414 15.37C14.6014 15.37 14.7431 15.3367 14.8664 15.27C14.9898 15.2033 15.0914 15.11 15.1714 14.99L15.8214 15.475C15.6714 15.685 15.4814 15.84 15.2514 15.94C15.0214 16.04 14.7848 16.09 14.5414 16.09C14.2648 16.09 14.0098 16.0467 13.7764 15.96C13.5431 15.8733 13.3414 15.75 13.1714 15.59C13.0014 15.4267 12.8681 15.23 12.7714 15C12.6781 14.77 12.6314 14.5133 12.6314 14.23C12.6314 13.9467 12.6781 13.69 12.7714 13.46C12.8681 13.23 13.0014 13.035 13.1714 12.875C13.3414 12.7117 13.5431 12.5867 13.7764 12.5C14.0098 12.4133 14.2648 12.37 14.5414 12.37C14.6414 12.37 14.7448 12.38 14.8514 12.4C14.9614 12.4167 15.0681 12.445 15.1714 12.485C15.2781 12.525 15.3798 12.5783 15.4764 12.645C15.5731 12.7117 15.6598 12.7933 15.7364 12.89L15.1364 13.38ZM17.1243 14.15L15.9543 12.46H16.9343L17.6893 13.7L18.4143 12.46H19.3593L18.2143 14.135L19.4943 16H18.5143L17.6493 14.575L16.8093 16H15.8843L17.1243 14.15Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_1047_7792" x="3.86731" y="12.37" width="16.627" height="5.71997" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.506727 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1047_7792"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1047_7792" result="shape"/>
</filter>
<clipPath id="clip0_1047_7792">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
