<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 5C4 3.34315 5.34315 2 7 2H15L20 7V19C20 20.6569 18.6569 22 17 22H7C5.34315 22 4 20.6569 4 19V5Z" fill="#6574FC"/>
<path d="M15 5.5V2L20 7H16.5C15 7 15 6.25 15 5.5Z" fill="white" fill-opacity="0.3"/>
<g filter="url(#filter0_d_1047_7879)">
<path d="M8.08 13.855H8.325C8.395 13.855 8.46333 13.85 8.53 13.84C8.59667 13.83 8.65667 13.8117 8.71 13.785C8.76667 13.755 8.81167 13.715 8.845 13.665C8.87833 13.6117 8.895 13.5433 8.895 13.46C8.895 13.3533 8.85167 13.265 8.765 13.195C8.68167 13.125 8.57833 13.09 8.455 13.09C8.33833 13.09 8.24167 13.1217 8.165 13.185C8.08833 13.2483 8.03833 13.3267 8.015 13.42L7.19 13.25C7.23 13.0967 7.29 12.9667 7.37 12.86C7.45 12.75 7.545 12.6617 7.655 12.595C7.765 12.5283 7.88667 12.48 8.02 12.45C8.15667 12.4167 8.3 12.4 8.45 12.4C8.60667 12.4 8.75667 12.42 8.9 12.46C9.04667 12.4967 9.175 12.5567 9.285 12.64C9.395 12.72 9.48167 12.8217 9.545 12.945C9.61167 13.0683 9.645 13.215 9.645 13.385C9.645 13.5817 9.59333 13.75 9.49 13.89C9.38667 14.03 9.23667 14.12 9.04 14.16V14.175C9.14667 14.1883 9.24 14.22 9.32 14.27C9.40333 14.32 9.47333 14.3817 9.53 14.455C9.58667 14.5283 9.63 14.6117 9.66 14.705C9.69 14.7983 9.705 14.8967 9.705 15C9.705 15.1767 9.67 15.3317 9.6 15.465C9.53333 15.5983 9.44167 15.71 9.325 15.8C9.21167 15.8867 9.07667 15.9517 8.92 15.995C8.76667 16.0383 8.60333 16.06 8.43 16.06C8.11333 16.06 7.84 15.9867 7.61 15.84C7.38333 15.6933 7.23333 15.4617 7.16 15.145L7.95 14.96C7.97667 15.0867 8.03 15.1867 8.11 15.26C8.19333 15.3333 8.315 15.37 8.475 15.37C8.63833 15.37 8.75833 15.3283 8.835 15.245C8.915 15.1583 8.955 15.0483 8.955 14.915C8.955 14.815 8.93333 14.7383 8.89 14.685C8.85 14.6283 8.79667 14.5867 8.73 14.56C8.66333 14.5333 8.58833 14.5183 8.505 14.515C8.42167 14.5083 8.33833 14.505 8.255 14.505H8.08V13.855ZM13.524 15.75C13.294 15.87 13.054 15.9567 12.804 16.01C12.554 16.0633 12.299 16.09 12.039 16.09C11.7623 16.09 11.5073 16.0467 11.274 15.96C11.0407 15.8733 10.839 15.75 10.669 15.59C10.499 15.4267 10.3657 15.23 10.269 15C10.1757 14.77 10.129 14.5133 10.129 14.23C10.129 13.9467 10.1757 13.69 10.269 13.46C10.3657 13.23 10.499 13.035 10.669 12.875C10.839 12.7117 11.0407 12.5867 11.274 12.5C11.5073 12.4133 11.7623 12.37 12.039 12.37C12.3223 12.37 12.5823 12.405 12.819 12.475C13.059 12.5417 13.269 12.6583 13.449 12.825L12.899 13.425C12.7923 13.315 12.6723 13.2317 12.539 13.175C12.4057 13.1183 12.239 13.09 12.039 13.09C11.8723 13.09 11.7207 13.1183 11.584 13.175C11.4507 13.2317 11.3357 13.3117 11.239 13.415C11.1423 13.515 11.0673 13.635 11.014 13.775C10.964 13.915 10.939 14.0667 10.939 14.23C10.939 14.3967 10.964 14.55 11.014 14.69C11.0673 14.8267 11.1423 14.9467 11.239 15.05C11.3357 15.15 11.4507 15.2283 11.584 15.285C11.7207 15.3417 11.8723 15.37 12.039 15.37C12.209 15.37 12.3557 15.3517 12.479 15.315C12.6023 15.275 12.7007 15.235 12.774 15.195V14.62H12.139V13.9H13.524V15.75ZM14.2106 12.46H15.5256C15.7089 12.46 15.8823 12.4767 16.0456 12.51C16.2089 12.5433 16.3506 12.6017 16.4706 12.685C16.5939 12.765 16.6906 12.875 16.7606 13.015C16.8339 13.1517 16.8706 13.325 16.8706 13.535C16.8706 13.7417 16.8373 13.915 16.7706 14.055C16.7039 14.195 16.6123 14.3067 16.4956 14.39C16.3789 14.4733 16.2406 14.5333 16.0806 14.57C15.9206 14.6033 15.7473 14.62 15.5606 14.62H14.9906V16H14.2106V12.46ZM14.9906 13.96H15.5106C15.5806 13.96 15.6473 13.9533 15.7106 13.94C15.7773 13.9267 15.8356 13.905 15.8856 13.875C15.9389 13.8417 15.9806 13.7983 16.0106 13.745C16.0439 13.6883 16.0606 13.6183 16.0606 13.535C16.0606 13.445 16.0389 13.3733 15.9956 13.32C15.9556 13.2633 15.9023 13.22 15.8356 13.19C15.7723 13.16 15.7006 13.1417 15.6206 13.135C15.5406 13.125 15.4639 13.12 15.3906 13.12H14.9906V13.96Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_1047_7879" x="6.16003" y="12.37" width="11.7106" height="5.71997" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.196078 0 0 0 0 0.278431 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1047_7879"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1047_7879" result="shape"/>
</filter>
</defs>
</svg>
