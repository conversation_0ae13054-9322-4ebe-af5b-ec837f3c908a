<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_8537_39107)">
<path opacity="0.5" d="M1.33337 3.66651C1.33337 2.56194 2.2288 1.6665 3.33337 1.6665H5.54022C6.04969 1.6665 6.53995 1.86093 6.91095 2.2101L7.7558 3.00526C8.12679 3.35443 8.61706 3.54886 9.12653 3.54886H11.3334C12.4379 3.54886 13.3334 4.44429 13.3334 5.54886V11.3332C13.3334 12.4377 12.4379 13.3332 11.3334 13.3332H3.33337C2.2288 13.3332 1.33337 12.4377 1.33337 11.3332V3.66651Z" fill="white"/>
<g style="mix-blend-mode:multiply" opacity="0.3" filter="url(#filter0_f_8537_39107)">
<path d="M9.86675 12.6664L11.6667 5.99976L3.66675 6.33309L1.66675 11.6664L9.86675 12.6664Z" fill="#DB7505"/>
</g>
<path d="M3.68089 6.87743C3.87249 6.35051 4.37327 5.99976 4.93394 5.99976H14.0964C15.0217 5.99976 15.6657 6.91917 15.3495 7.78875L13.6525 12.4554C13.4609 12.9823 12.9601 13.3331 12.3995 13.3331H3.23697C2.31169 13.3331 1.66771 12.4137 1.98392 11.5441L3.68089 6.87743Z" fill="white"/>
</g>
<defs>
<filter id="filter0_f_8537_39107" x="-0.854085" y="3.47892" width="15.0417" height="11.7084" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.26042" result="effect1_foregroundBlur_8537_39107"/>
</filter>
<clipPath id="clip0_8537_39107">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
