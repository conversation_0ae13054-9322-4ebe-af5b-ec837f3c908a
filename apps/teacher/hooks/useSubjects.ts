import { JOB_TYPE, SUBJECT_OPTIONS } from "@/enums";
import { useMemo } from "react";
import { useApp } from "./useApp";

export default function useSubjects() {
  const { userInfo } = useApp();

  const subjectInfos = useMemo(() => {
    if (!userInfo) return [];

    const currentSchool = userInfo.schoolInfos.find(
      (item) => item.schoolID === userInfo.currentSchoolID
    );

    const subjects = [];

    for (let i = 0; i < userInfo.teacherJobInfos.length; i++) {
      const item = userInfo.teacherJobInfos[i];
      if (!item.jobType?.jobType || item.schoolID !== currentSchool?.schoolID)
        continue;

      // TODO: 这里的学科需要看看放开哪些！
      //   校长/年级主任/班主任 可以查看所有学科
      if (
        [
          JOB_TYPE.JOB_TYPE_PRINCIPAL,
          JOB_TYPE.JOB_TYPE_GRADE_HEAD,
          JOB_TYPE.JOB_TYPE_CLASS_TEACHER,
        ].indexOf(item.jobType.jobType) !== -1
      )
        return SUBJECT_OPTIONS.slice(0, 10).map((item) => ({
          subjectKey: item.value,
          subjectName: item.label,
        }));

      if (!item.jobSubject) continue;

      if (
        item.jobType.jobType === JOB_TYPE.JOB_TYPE_SUBJECT_HEAD ||
        item.jobType.jobType === JOB_TYPE.JOB_TYPE_SUBJECT_TEACHER
      ) {
        subjects.push({
          subjectKey: item.jobSubject.jobSubject,
          subjectName: item.jobSubject.name,
        });
      }
    }

    return subjects;
  }, [userInfo]);

  return subjectInfos;
}
