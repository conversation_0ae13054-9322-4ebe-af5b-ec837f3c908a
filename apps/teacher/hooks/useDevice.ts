import { ACCESS_CHANNEL } from "@/enums";
import React from "react";

export const DeviceContext = React.createContext<{
  screenSize: { width: number; height: number; statusBarHeight: number } | null;
  accessChannel: ACCESS_CHANNEL;
} | null>(null);

export function useDevice() {
  const context = React.useContext(DeviceContext);

  if (!context) {
    throw new Error("useDevice must be used within a DeviceProvider.");
  }

  return context;
}
