import { useApp } from "@/hooks";
import { UserSubjectItem } from "@/types/assign/home";
import { useComputed } from "@preact-signals/safe-react";

export default function useSubjectInfosByUserInfo() {
    const { userInfo } = useApp();

    const subjectInfos = useComputed<UserSubjectItem[]>(() => {
        if (!userInfo?.teacherJobInfos) return [];

        const res = new Map<number, UserSubjectItem>();
        for (let i = 0; i < userInfo.teacherJobInfos.length; i++) {
            const item = userInfo.teacherJobInfos[i];
            if (
                item.schoolID !== userInfo.currentSchoolID ||
                !item.jobSubject ||
                item.jobSubject.jobSubject === 0
            ) {
                continue;
            }

            res.set(item.jobSubject.jobSubject, {
                subjectKey: item.jobSubject.jobSubject,
                subjectName: item.jobSubject.name,
                // NOTE: 这个目前是写死的，只开布置课程，但是是在数据计算方法上控制的
                taskTypes: [10, 20, 30, 40],
            });
        }

        return [...res.values()].sort((a, b) => a.subjectKey - b.subjectKey);
    });

    return {
        subjectInfos,
    }
}