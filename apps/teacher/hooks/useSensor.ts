import {
  ACTIVE_HEARTBEAT_EVENT_NAME,
  ACTIVE_HEARTBEAT_INTERVAL,
} from "@/configs";
import { sensorsManager } from "@/libs";
import { useRequest } from "ahooks";

/**
 * 神策统计，自动触发老师活跃心跳
 */
export const useActiveHeartbeatSensor = () => {
  useRequest(
    async () => {
      sensorsManager.track(ACTIVE_HEARTBEAT_EVENT_NAME);
    },
    {
      pollingInterval: ACTIVE_HEARTBEAT_INTERVAL,
      pollingWhenHidden: false,
    }
  );
};
