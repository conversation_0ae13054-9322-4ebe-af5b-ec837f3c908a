import { AssignCard } from "@/components/assign/assign-card";
import {
  AssignDatePicker,
  DateTimeChangePayload,
  isInvalidTimeRange,
} from "@/components/assign/assign-date-picker";
import { AssignCourseTimeRange } from "@/types/assign/course";
import { cn } from "@/utils/utils";
import { Signal } from "@preact-signals/safe-react";
import { Fragment, memo } from "react";

export default memo(function AssignTimeRanges({
  assignTimeRanges,
  onDateTimeChange,
  getRecommendEndTime,
}: {
  assignTimeRanges: Signal<AssignCourseTimeRange[]>;
  onDateTimeChange: (timeRange: DateTimeChangePayload) => void;
  getRecommendEndTime: (timeRange: AssignCourseTimeRange) => number;
}) {
  return assignTimeRanges.value.length > 0 ? (
    <AssignCard className="bg-fill-gray-2 flex-1 rounded-xl py-4 outline-none">
      <div className="grid grid-cols-[minmax(4.5rem,max-content)_1fr_1fr] items-start gap-x-3 py-2">
        <div className="col-span-1 pr-3"></div>
        <div className="text-gray-4 col-span-1 text-xs font-normal leading-none">
          发布时间
        </div>
        <div className="text-gray-4 col-span-1 text-xs font-normal leading-none">
          要求完成时间
        </div>
        {assignTimeRanges.value.map((cls, index) => (
          <Fragment key={index}>
            <div
              className={cn(
                "min-h-7.75 col-span-1 mt-3 flex items-center pr-3 text-sm font-normal leading-tight text-slate-600",
                index === 0 && "mt-2"
              )}
            >
              {cls.classInfo.jobGradeName + cls.classInfo.name}
            </div>

            <div className={cn("col-span-1 mt-3", index === 0 && "mt-2")}>
              <div className="h-7.75">
                <AssignDatePicker
                  className="w-full"
                  isStart={true}
                  timeRange={cls}
                  timeRangeIndex={index}
                  onDateTimeChange={onDateTimeChange}
                />
              </div>
              {isInvalidTimeRange(cls) && (
                <div className="text-danger-2 mt-1 text-xs leading-normal">
                  要求完成时间不允许早于或等于发布时间
                </div>
              )}
            </div>

            <div className={cn("col-span-1 mt-3", index === 0 && "mt-2")}>
              <div className="h-7.75">
                <AssignDatePicker
                  className="w-full"
                  isStart={false}
                  timeRange={cls}
                  timeRangeIndex={index}
                  recommendEndTime={getRecommendEndTime(cls)}
                  onDateTimeChange={onDateTimeChange}
                />
              </div>
            </div>
          </Fragment>
        ))}
      </div>
    </AssignCard>
  ) : (
    <div />
  );
});
