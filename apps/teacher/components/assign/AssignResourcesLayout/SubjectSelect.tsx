import useSubjectInfosByUserInfo from "@/hooks/useSubjectInfosByUserInfo";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/ui/select";
import { Signal } from "@preact-signals/safe-react";
import { memo } from "react";

function SubjectSelect({
  value,
  onChange,
  className,
}: {
  value: Signal<number>;
  onChange: (value: number) => void;
  className?: string;
}) {
  const { subjectInfos } = useSubjectInfosByUserInfo();

  console.log("subjectInfos", subjectInfos.value, value);

  return (
    <Select
      defaultValue={String(value.peek())}
      onValueChange={(value) => onChange(Number(value))}
    >
      <SelectTrigger
        className={className}
        classNames={{ icon: "text-gray-3 text-sm w-3.5 h-3.5" }}
      >
        <SelectValue placeholder="选择学科" />
      </SelectTrigger>
      <SelectContent>
        {subjectInfos.value?.map((item) => (
          <SelectItem key={item.subjectKey} value={String(item.subjectKey)}>
            {item.subjectName}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export default memo(SubjectSelect);
