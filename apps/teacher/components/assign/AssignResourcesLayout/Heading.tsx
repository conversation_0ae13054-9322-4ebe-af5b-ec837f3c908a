import { cn } from "@/utils/utils";

interface HeadingProps {
  content: string;
  required?: boolean;
  className?: string;
}

export default function Heading({
  content,
  required = false,
  className,
}: HeadingProps) {
  return (
    <span
      className={cn(
        "w-17 min-w-17 text-right text-sm leading-normal text-slate-600",
        className
      )}
    >
      {required ? (
        <span className="text-danger-2 relative top-[0.125rem] mr-1 align-middle">
          *
        </span>
      ) : null}
      <span>{content}</span>
    </span>
  );
}
