import { AssignCard } from "@/components/assign/assign-card";
import { DateTimeChangePayload } from "@/components/assign/assign-date-picker";
import { TchCounterInput } from "@/components/common/tch-counter-input";
import { AssignCourseTimeRange } from "@/types/assign/course";
import { ClassStudentListItem } from "@/types/assign/student";
import { TargetJobClass } from "@/types/user";
import { Textarea } from "@/ui/textarea";
import { Signal } from "@preact-signals/safe-react";
import { AssignTargets } from "../assign-targets";
import AssignTimeRanges from "./AssignTimeRanges";
import Heading from "./Heading";
import Item from "./Item";
import SubjectSelect from "./SubjectSelect";

export type AssignResourcesFormProps = {
  subject: Signal<number>;
  assignName: Signal<string>;
  assignTimeRanges: Signal<AssignCourseTimeRange[]>;
  classList: TargetJobClass[];
  checkedClasses: Signal<TargetJobClass[]>;
  onSubjectChange: (subject: number) => void;
  toggleClass: (classId: TargetJobClass) => void;
  onDateTimeChange: (timeRange: DateTimeChangePayload) => void;
  onAssignNameChange: (assignName: string) => void;
  getRecommendEndTime: (timeRange: AssignCourseTimeRange) => number;
  teacherComment: Signal<string>;
  setTeacherComment: (comment: string) => void;
  checkedClassStudentList: ClassStudentListItem[];
};

export default function AssignResourcesForm({
  subject,
  checkedClasses,
  assignName,
  assignTimeRanges,
  classList,
  teacherComment,
  onSubjectChange,
  toggleClass,
  onDateTimeChange,
  onAssignNameChange,
  getRecommendEndTime,
  setTeacherComment,
  checkedClassStudentList,
}: AssignResourcesFormProps) {
  return (
    <AssignCard>
      <Item>
        <Heading content="布置学科" required />
        <SubjectSelect
          value={subject}
          onChange={onSubjectChange}
          className="h-9 w-32"
        />
      </Item>

      <Item>
        <Heading content="布置对象" required />
        <AssignTargets
          classList={classList}
          checkedClasses={checkedClasses}
          checkedClassStudentList={checkedClassStudentList}
          toggleClass={toggleClass}
        />
      </Item>

      <Item>
        <Heading content="任务名称" required />
        <TchCounterInput
          className="w-136 h-9"
          defaultValue={assignName.peek()}
          maxLength={30}
          onChange={(e) => onAssignNameChange(e.target.value)}
        />
      </Item>
      <Item className="items-start">
        <Heading content="任务时间" required />
        <AssignTimeRanges
          assignTimeRanges={assignTimeRanges}
          onDateTimeChange={onDateTimeChange}
          getRecommendEndTime={getRecommendEndTime}
        />
      </Item>

      <Item className="items-start">
        <Heading content="任务留言" className="leading-6" />
        <Textarea
          containerClassName="w-full"
          defaultValue={teacherComment.peek()}
          onChange={(e) => setTeacherComment(e.target.value)}
          maxLength={50}
          placeholder="说明您对任务的要求，留言将在学生端显示"
        />
      </Item>
    </AssignCard>
  );
}
