"use client";

import AssignResourceFooterPure from "@/app/assign/[subjectKey]/resource/_components/AssignResourceFooter/AssignResourceFooterPure";
import { getRecommendEndTime } from "@/app/assign/[subjectKey]/resource/store/useAssignSourceTimeRange";
import { useApp } from "@/hooks";
import { AssignCourseTimeRange } from "@/types/assign/course";
import { ResourceItem } from "@/types/resource";
import { ScrollArea } from "@/ui/scroll-area";
import { useComputed } from "@preact-signals/safe-react";
import { memo, useCallback } from "react";
import { AssignHeader } from "../assign-header";
import { AssignHeading } from "../assign-heading";
import AssignResourcesForm from "./AssignResourcesForm";
import useAssignResourcesModel from "./useAssignResourcesModel";

export type AssignResourcesLayoutProps = {
  resources: ResourceItem[];
  onBack: () => void;
  onConfirmAssign?: () => void;
};

export default memo(function AssignResourcesLayout({
  resources,
  onConfirmAssign,
  onBack,
}: AssignResourcesLayoutProps) {
  const {
    setSubject,
    setAssignName,
    subject,
    checkedClasses,
    assignName,
    assignTimeRanges,
    updateAssignTimeRange,
    toggleClass,
    classList,
    teacherComment,
    setTeacherComment,
    confirmAssign,
    isConfirmAssignLoading,
    checkedClassStudentList,
  } = useAssignResourcesModel(resources, onConfirmAssign);

  const handleGetRecommendEndTime = useCallback(
    (timeRange: AssignCourseTimeRange) => {
      return getRecommendEndTime(timeRange.startTime);
    },
    []
  );

  const { statusBarHeight } = useApp();

  const isNextButtonDisabled = useComputed(() => {
    return (
      isConfirmAssignLoading ||
      assignName.value.length === 0 ||
      checkedClasses.value.length === 0
    );
  });

  return (
    <>
      <AssignHeader title="布置资源任务" onBack={onBack} />
      <div className="flex h-full w-full flex-col px-6">
        <AssignHeading content="任务设置" className="mb-4" />
        <ScrollArea className="w-full flex-1 overflow-hidden">
          <div
            className="h-full w-full"
            style={{ height: `calc(100vh - 39.125rem - ${statusBarHeight}px)` }}
          >
            <AssignResourcesForm
              subject={subject}
              checkedClasses={checkedClasses}
              assignName={assignName}
              assignTimeRanges={assignTimeRanges}
              classList={classList}
              teacherComment={teacherComment}
              onSubjectChange={setSubject}
              toggleClass={toggleClass}
              onDateTimeChange={updateAssignTimeRange}
              onAssignNameChange={setAssignName}
              getRecommendEndTime={handleGetRecommendEndTime}
              setTeacherComment={setTeacherComment}
              checkedClassStudentList={checkedClassStudentList || []}
            />
          </div>
        </ScrollArea>
        <AssignResourceFooterPure
          resources={resources}
          nextButtonContent="完成并布置"
          nextButtonDisabled={isNextButtonDisabled.value}
          onCancel={onBack}
          onNext={confirmAssign}
        />
      </div>
    </>
  );
});
