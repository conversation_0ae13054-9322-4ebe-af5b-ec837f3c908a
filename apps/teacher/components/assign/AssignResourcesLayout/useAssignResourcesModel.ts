import useGetResourceStudentGroupsParams from "@/app/assign/[subjectKey]/resource/hooks/useGetResourceStudentGroupsParams";
import useAssignSourceTimeRange from "@/app/assign/[subjectKey]/resource/store/useAssignSourceTimeRange";
import useCheckAssignTarget from "@/app/assign/[subjectKey]/store/useCheckAssignTarget";
import { AssignResourceTypeEnum, AssignTaskTypeEnum } from "@/configs/assign";
import { useApp } from "@/hooks/useApp";
import useSubjectInfosByUserInfo from "@/hooks/useSubjectInfosByUserInfo";
import { assignTask } from "@/services/assign";
import { AssignResourceParams } from "@/types/assign/course";
import { ResourceItem } from "@/types/resource";
import { toast } from "@/ui/toast";
import { getTeacherClassListBySubjectKey } from "@/utils/assign/assign";
import { batch, useComputed, useSignal } from "@preact-signals/safe-react";
import { useRequest } from "ahooks";
import { useCallback } from "react";

function genInitialAssignName(subjectName: string) {
  const date = new Date();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${month}月${day}日${subjectName}资源任务`;
}

export default function useAssignResourcesModel(
  resources: ResourceItem[],
  onConfirmAssign?: () => void
) {
  const { userInfo } = useApp();
  const { subjectInfos } = useSubjectInfosByUserInfo();
  const subject = useSignal(subjectInfos.value[0]?.subjectKey || 0);

  const { classList, checkedClasses, checkedClassStudentList, toggleClass } =
    useCheckAssignTarget(subject.value, true);

  const teacherComment = useSignal("");
  const setTeacherComment = useCallback(
    (newComment: string) => {
      teacherComment.value = newComment;
    },
    [teacherComment]
  );

  const subjectName = useComputed(() => {
    return (
      subjectInfos.value.find((item) => item.subjectKey === subject.value)
        ?.subjectName || ""
    );
  });
  const defaultAssignName = useComputed(() => {
    return genInitialAssignName(subjectName.value);
  });
  const assignName = useSignal(defaultAssignName.value);

  const setAssignName = useCallback(
    (newName: string) => {
      assignName.value = newName;
    },
    [assignName]
  );

  const { assignTimeRanges, updateAssignTimeRange } =
    useAssignSourceTimeRange(checkedClasses);

  const setSubject = useCallback(
    (newSubject: number) => {
      batch(() => {
        if (assignName.value === defaultAssignName.value) {
          const newSubjectName =
            subjectInfos.value.find((item) => item.subjectKey === newSubject)
              ?.subjectName || "";
          assignName.value = genInitialAssignName(newSubjectName);
        }
        subject.value = newSubject;
        const newClassList = getTeacherClassListBySubjectKey(
          newSubject,
          userInfo
        );
        checkedClasses.value = [newClassList[0]];
      });
    },
    [
      assignName,
      checkedClasses,
      defaultAssignName.value,
      subject,
      subjectInfos.value,
      userInfo,
    ]
  );

  const { getResourceStudentGroupsParams } =
    useGetResourceStudentGroupsParams(assignTimeRanges);
  const { run: confirmAssign, loading: isConfirmAssignLoading } = useRequest(
    async () => {
      const params: AssignResourceParams = {
        subject: subject.value,
        taskType: AssignTaskTypeEnum.TASK_TYPE_RESOURCE,
        taskName: assignName.value,
        teacherComment: teacherComment.value,
        resources: [
          {
            resourceId: resources[0].resourceId!,
            resourceType: AssignResourceTypeEnum.RESOURCE_RESOURCE,
            resourceSubIDs: resources.map((e) => e.resourceId!),
          },
        ],

        studentGroups: getResourceStudentGroupsParams(),
      };
      await assignTask(params);
      toast.success("布置成功");

      onConfirmAssign?.();
    },
    {
      manual: true,
      cacheKey: `confirmAssign-standalone-${subject.value}`,
    }
  );

  return {
    subject,
    setSubject,

    assignName,
    setAssignName,

    classList,
    checkedClasses,
    checkedClassStudentList,
    toggleClass,

    teacherComment,
    setTeacherComment,

    assignTimeRanges,
    updateAssignTimeRange,

    confirmAssign,
    isConfirmAssignLoading,
  };
}
