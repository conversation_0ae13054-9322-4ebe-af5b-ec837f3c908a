import { useAssignCourseContext } from "@/app/assign/[subjectKey]/course/store";
import { getRecommendEndTime } from "@/app/assign/[subjectKey]/course/store/useAssignStep";
import { AssignCourseTimeRange } from "@/types/assign/course";
import { Textarea } from "@/ui/textarea";
import { cn } from "@/utils/utils";
import { Fragment, useRef } from "react";
import { AssignCard } from "./assign-card";
import { AssignDatePicker, DateTimeChangePayload } from "./assign-date-picker";
import { AssignHeading } from "./assign-heading";

interface AssignCourseTimeProps {
  style?: React.CSSProperties;
  className?: string;
}

export function AssignCourseTime({
  style = {},
  className = "",
}: AssignCourseTimeProps) {
  const {
    assignName,
    assignTimeRanges,
    updateAssignTimeRange,
    totalClassTimes,
    invalidTimeRanges,
    teacherComment,
    updateTeacherComment,
  } = useAssignCourseContext();

  const recommendEndTime = (range: AssignCourseTimeRange) => {
    return getRecommendEndTime(range.startTime, totalClassTimes.value);
  };

  const onDateTimeChange = (payload: DateTimeChangePayload) => {
    updateAssignTimeRange(payload);
  };

  const isStuReady = useRef(false);

  return (
    <div className={cn("flex flex-col gap-4", className)} style={style}>
      <AssignHeading content="3.任务设置" />
      <AssignCard className="flex-1 rounded-[1.25rem]">
        <div className="mb-4 flex items-center gap-6">
          <span className="w-17 text-right text-sm leading-normal text-slate-600">
            任务名称
          </span>
          <AssignHeading content={assignName.value} />
        </div>
        <div className="mb-4 flex items-start gap-6">
          <span className="w-17 text-right text-sm leading-normal text-slate-600">
            <span className="text-danger-2 mr-1 align-middle">*</span>
            <span>任务时间</span>
          </span>
          <AssignCard className="bg-fill-gray-2 flex-1 rounded-xl py-4 outline-none">
            {/* <div className="text-gray-4 grid grid-cols-[minmax(4.5rem,max-content)_1fr_1fr] gap-3 text-xs font-normal leading-none">
              <div className="col-span-1"></div>
              <div className="col-span-1">发布时间</div>
              <div className="col-span-1">要求完成时间</div>
            </div> */}
            <div className="grid grid-cols-[minmax(4.5rem,max-content)_1fr_1fr] items-start gap-x-3 py-2">
              <div className="col-span-1 pr-3"></div>
              <div className="text-gray-4 col-span-1 text-xs font-normal leading-none">
                发布时间
              </div>
              <div className="text-gray-4 col-span-1 text-xs font-normal leading-none">
                要求完成时间
              </div>
              {assignTimeRanges.value.map((cls, index) => (
                <Fragment key={index}>
                  <div
                    className={cn(
                      "min-h-7.75 col-span-1 mt-3 flex items-center pr-3 text-sm font-normal leading-tight text-slate-600",
                      index === 0 && "mt-2"
                    )}
                  >
                    {cls.classInfo.jobGradeName + cls.classInfo.name}
                  </div>

                  <div className={cn("col-span-1 mt-3", index === 0 && "mt-2")}>
                    <div className="h-7.75">
                      <AssignDatePicker
                        className="w-full"
                        isStart={true}
                        timeRange={cls}
                        timeRangeIndex={index}
                        onDateTimeChange={onDateTimeChange}
                      />
                    </div>
                    {invalidTimeRanges.value.includes(cls) && (
                      <div className="text-danger-2 mt-1 text-xs leading-normal">
                        要求完成时间不允许早于或等于发布时间
                      </div>
                    )}
                  </div>

                  <div className={cn("col-span-1 mt-3", index === 0 && "mt-2")}>
                    <div className="h-7.75">
                      <AssignDatePicker
                        className="w-full"
                        isStart={false}
                        timeRange={cls}
                        timeRangeIndex={index}
                        recommendEndTime={recommendEndTime(cls)}
                        onDateTimeChange={onDateTimeChange}
                      />
                    </div>
                  </div>
                </Fragment>
              ))}
            </div>
          </AssignCard>
        </div>

        {isStuReady.current ? (
          <div className="flex items-start gap-6">
            <span className="w-17 text-right text-sm leading-normal text-slate-600">
              <span>任务留言</span>
            </span>
            <Textarea
              className="h-27"
              containerClassName="w-full"
              value={teacherComment.value}
              onChange={(e) => {
                updateTeacherComment(e.target.value);
              }}
              placeholder="说明您对任务的要求，留言将在学生端显示"
              maxLength={50}
            />
          </div>
        ) : null}
      </AssignCard>
    </div>
  );
}
