import ResourcePreviewerLayout, {
  ResourcePreviewerLayoutProps,
} from "@repo/core/components/resource-preview/layout";

import { useApp } from "@/hooks";
import {
  getResourceCommonPreview,
  getResourceDocumentRefreshToken,
} from "@/services/resource";
import { cn } from "@/utils/utils";
import { useRequest } from "ahooks";
import { useState } from "react";
import { createPortal } from "react-dom";

export type ResourcePreviewerProps = Omit<
  ResourcePreviewerLayoutProps,
  | "resourcesData"
  | "isFetchingResourcesData"
  | "getResourceDocumentRefreshToken"
> & {
  resourceIds: string[];
};

export default function ResourcePreviewer(props: ResourcePreviewerProps) {
  const { resourceIds, closeButtonStyle, className, ...rest } = props;

  const { statusBarHeight } = useApp();

  const [documentResourceInfo, setDocumentResourceInfo] = useState<{
    accessToken: string;
    refreshToken: string;
  } | null>(null);

  const { data: resourcesData, loading: isFetchingResourcesData } = useRequest(
    async () => {
      if (!resourceIds.filter(Boolean).length) return undefined;

      const res = await Promise.all(
        resourceIds.map((resourceId) => getResourceCommonPreview(resourceId))
      );

      if (res[0]?.webOffice) {
        setDocumentResourceInfo(res[0].webOffice);
      } else {
        setDocumentResourceInfo(null);
      }

      return res;
    },
    {
      refreshDeps: [resourceIds.join(",")],
      ready: !!resourceIds.filter(Boolean).length,
      cacheKey: `resource-previewer-${resourceIds.join(",")}`,
    }
  );

  const { runAsync } = useRequest(
    async () => {
      if (!documentResourceInfo) throw new Error("No document resource info");

      const res = await getResourceDocumentRefreshToken(
        documentResourceInfo.accessToken,
        documentResourceInfo.refreshToken
      );

      setDocumentResourceInfo({
        ...documentResourceInfo,
        ...res,
      });

      return {
        token: res.accessToken,
        timeout:
          Date.parse(res.accessTokenExpiredTime) - Date.now() - 5 * 60 * 1000,
      };
    },
    {
      manual: true,
      ready: documentResourceInfo?.refreshToken !== undefined,
      cacheKey: `resource-previewer-${documentResourceInfo?.accessToken}`,
    }
  );

  return createPortal(
    <ResourcePreviewerLayout
      closeButtonStyle={{
        ...closeButtonStyle,
        top: `${statusBarHeight + 8}px`,
      }}
      className={cn("z-51 pointer-events-auto fixed left-0 top-0", className)}
      {...rest}
      resourcesData={resourcesData}
      isFetchingResourcesData={isFetchingResourcesData}
      getResourceDocumentRefreshToken={runAsync}
    />,
    document.body
  );
}
