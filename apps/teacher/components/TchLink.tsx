import loadingData from "@/public/lottie/loading.json";
import { cn } from "@/utils";
import { useDebounce } from "ahooks";
import Lottie from "lottie-react";
import Link, { useLinkStatus } from "next/link";
import { useImperativeHandle, useRef, type Ref } from "react";

export function LoadingIndicator({
  ref,
  className,
}: {
  ref: Ref<{ pending: boolean }>;
  className?: string;
}) {
  const { pending } = useLinkStatus();
  const debouncePending = useDebounce(pending);

  useImperativeHandle(ref, () => {
    return { pending };
  });

  return debouncePending ? (
    <Lottie
      className={cn("h-3.75 inline-block w-5", className)}
      animationData={loadingData}
      loop
    />
  ) : null;
}

export default function TchLink({
  children,
  loadingIconClassName,
  ...props
}: React.ComponentProps<typeof Link> & { loadingIconClassName?: string }) {
  const ref = useRef<{ pending: boolean }>(null);

  return (
    <Link
      {...props}
      onNavigate={(e) => {
        if (ref.current?.pending) {
          e.preventDefault();
          return;
        }

        props.onNavigate?.(e);
      }}
    >
      {children}
      <LoadingIndicator className={loadingIconClassName} ref={ref} />
    </Link>
  );
}
