import { memo, useMemo } from "react";
import MdPreview from "../MdPreview";
import { QaContentType } from "../type";
import QuestionOptionsDisplay from "./QuestionOptions";

export const QuestionContent = memo(function QuestionContent({
  qaContent,
  ossHost,
  levels,
}: {
  qaContent: QaContentType;
  ossHost?: string;
  levels?: (string | number)[];
}) {
  const isParentQuestion = useMemo(
    () => qaContent.subQuestionList && qaContent.subQuestionList.length > 0,
    [qaContent.subQuestionList]
  );

  return (
    <div className="w-full" style={{ lineHeight: "2.5" }}>
      <MdPreview
        content={qaContent.content}
        ossHost={ossHost}
        questionId={qaContent.questionId}
      />
      {isParentQuestion === true ? (
        <div className="flex flex-col gap-4">
          {qaContent.subQuestionList?.map((question, index) => (
            <div
              key={question.questionId}
              className="border-line-3 flex items-start gap-[0.5rem] border-t border-dashed"
            >
              <div className="my-0 flex min-w-8 shrink-0 items-center justify-center rounded-2xl px-1">
                <h3 className="text-gray-2 font-medium">{index + 1}.</h3>
              </div>
              <QuestionContent
                qaContent={question}
                ossHost={ossHost}
                levels={levels}
              />
            </div>
          ))}
        </div>
      ) : (
        <QuestionOptionsDisplay qaContent={qaContent} ossHost={ossHost} />
      )}
    </div>
  );
});
