@import "@repo/ui/globals.css";
@theme {
  /* 学生端屏幕比例 */
  --aspect-stu: 10/6;

  /* 文字大小 */
  --text-xxs: 0.625rem;
  --text-xxs--line-height: calc(1 / 0.625);

  /* 橘色 */
  --color-orange-1: #FF902E;
  --color-orange-2: #FFEDDF;
  --color-orange-text: #CC6204;

  /* 红色 */
  --color-red-1: #F66042;
  --color-red-2: #FFE5DF;
  --color-red-text: #D1320A;

  /* 绿色 */
  --color-green-1: #84D64B;
  --color-green-2: #DFF2D1;
  --color-green-text: #449908;

  /* 灰色 */
  --color-gray-bg: #F4F3F2;
  --color-gray-text: rgba(51, 48, 45, 0.7);

  /* 白色 */
  --color-white-bg: #FFFFFF;
  --color-white-hover: #F9FAFB;
  --color-white-border: rgba(51, 46, 41, 0.06);

  /* 文字颜色 */
  --color-text-1: rgba(51, 48, 45, 0.95); /* Text 1 */
  --color-text-2: rgba(51, 48, 45, 0.85); /* Text 2 */
  --color-text-3: rgba(51, 48, 45, 0.7);  /* Text 3 */
  --color-text-4: rgba(51, 48, 45, 0.55); /* Text 4 */
  --color-text-5: rgba(51, 48, 45, 0.4);  /* Text 5 */

  /* 填充色 */
  --color-fill-1: #1F1D1B;        /* Fill 1 - 100% */
  --color-fill-2: rgba(31, 29, 27, 0.6);  /* Fill 2 - 60% */
  --color-fill-3: rgba(31, 29, 27, 0.2);  /* Fill 3 - 20% */
  --color-fill-4: rgba(31, 29, 27, 0.05); /* Fill 4 - 5% */

  /* 背景色 */
  --color-fill-beige: #FAEAD7;  /* Beige */
  --color-fill-gray: #F7F6F5;   /* Gray */
  --color-fill-white: #FFFFFF;  /* White */

  /* 分割线颜色 */
  --color-divider-1: rgba(51, 46, 41, 0.12);
  --color-divider-2: rgba(51, 46, 41, 0.06);

  /* 按钮阴影颜色 */
  --btn-orange-shadow: #F27813;
  --btn-red-shadow: #D94123;
  --btn-green-shadow: rgba(115, 178, 71, 0.3);
  --btn-gray-shadow: rgba(51, 46, 41, 0.2);
  --btn-white-shadow: rgba(51, 46, 41, 0.2);

  --btn-shadow-orange-primary: #F27813;
  --btn-shadow-orange-secondary: #F1CFB5;
  --btn-shadow-red-primary: #E36544;
  --btn-shadow-red-secondary: #F4D3CC;
  --btn-shadow-green-primary: #73B247;
  --btn-shadow-green-secondary: #D0E7C0;
  --btn-shadow-white: var(--color-divider-1);
  --btn-shadow-gray: #E0DEDC;

  /* 分隔线颜色 */
  --Divider-Divider1: rgba(51, 46, 41, 0.12);
  --Divider-Divider2: rgba(51, 46, 41, 0.06);

  /* 文稿组件内使用的颜色 */
  --color-stone-600:#5C5757;
  --color-stone-700: #4A292A;
  --color-stone-900: #221407;
  --color-amber-200: #FFD080;
  --color-yellow-950: #4D2F13;
  --color-dim-orange: #CC6204;
  --color-main-orange: #FFA666;
  --color-pink-100: #F7EBF0;
}


/* 覆盖 shadcn-ui 默认 css变量 */
:root {
  /* 背景 */
  --background: var(--color-bg-white);
  --foreground: var(--color-text-3);

  /* 卡片 */
  --card: var(--color-bg-white);
  --card-foreground: var(--color-text-3);

  /* 弹出框 */
  --popover: var(--color-bg-white);
  --popover-foreground: var(--color-text-3);

  /* 主色 */
  --primary: var(--color-orange-1);
  --primary-foreground: white;

  /* 次要色 */
  --secondary: white;
  --secondary-foreground: var(--color-orange-1);

  /* 圆角 */
  --radius: 0.625rem;
}

@layer components {
  .typography {
    p {
      font-size: 1rem;
      line-height: 1.75;
      margin: 1rem 0;
    }

    h1 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    h2 {
      font-size: 1.25rem;
      font-weight: 600;
      margin-top: 1.5rem;
      margin-bottom: 0.75rem;
    }

    h3 {
      font-size: 1.125rem;
      font-weight: 600;
      margin-top: 1rem;
      margin-bottom: 0.5rem;
    }

    ul, ol {
      margin: 1rem 0;
      padding-left: 1.5rem;
      line-height: 1.75;
    }

    li {
      margin: 0.25rem 0;
    }

    a {
      text-decoration: underline;
    }
  }
}
