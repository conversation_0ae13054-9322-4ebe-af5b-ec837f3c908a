import IconSidebarChecked from "@repo/core/assets/course/sidebar-checked.svg";
import IconSidebarCurrent from "@repo/core/assets/course/sidebar-current.svg";
import IconSidebarExercise from "@repo/core/assets/course/sidebar-exercise.svg";
import IconSidebarFlag from "@repo/core/assets/course/sidebar-flag.svg";
import IconSidebarLocked from "@repo/core/assets/course/sidebar-locked.svg";
import { WidgetStatus, WidgetType } from "@repo/core/types/data/course";
import { cn } from "@repo/ui/lib/utils";
import { ComponentProps, FC, useMemo } from "react";

interface CourseSummaryProps extends ComponentProps<"div"> {
  index: number;
  title: string;
  status: WidgetStatus;
  isCurrent: boolean;
  isLast: boolean;
  onSelect?: () => void;
  type: WidgetType;
}

export const CourseSummary: FC<CourseSummaryProps> = ({
  title,
  status,
  isCurrent,
  isLast,
  onSelect,
  type,
}) => {
  const isLocked = status === "locked";
  const isCompleted = status === "completed";

  const Icon = useMemo(() => {
    if (isLast) return IconSidebarFlag;
    if (isCompleted) return IconSidebarChecked;
    if (isCurrent) return IconSidebarCurrent;

    return IconSidebarCurrent;
  }, [isCurrent, isCompleted, isLast]);

  return (
    <div
      className={cn("flex select-none flex-row")}
      onClick={(e) => {
        e.stopPropagation();
        if (isLocked) return;
        onSelect?.();
      }}
    >
      <div
        className={cn(
          "relative flex w-5 flex-col items-center justify-start",
          isLocked && "opacity-30"
        )}
      >
        <div className={cn("z-1 absolute top-3")}>
          <Icon className="text-main-orange" />
        </div>
        <div
          className={cn(
            "border-0.5 border-main-orange translate-y-4 border",
            isLast ? "h-1" : "h-full",
            isLocked && "border-dashed"
          )}
        />
      </div>
      <div
        className={cn(
          "flex h-full min-h-16 w-full flex-row gap-1 p-3",
          isCurrent && "rounded-lg bg-[#FFA666]/10"
        )}
      >
        {type === "exercise" && (
          <IconSidebarExercise
            className={cn(
              "size-5",
              isCurrent ? "text-dim-orange" : "text-text-1",
              isLocked && "opacity-55"
            )}
          />
        )}
        <h2
          className={cn(
            "flex-1 text-base font-bold leading-snug",
            isCurrent ? "text-dim-orange" : "text-text-1",
            isLocked && "opacity-55"
          )}
        >
          {title}
        </h2>
        {isLocked && <IconSidebarLocked />}
      </div>
    </div>
  );
};
