import Image from "next/image";
import {
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
} from "react";
import BackBtn from "../_components/BackBtn";
import ForwardBtn from "../_components/ForwardBtn";
import PlayControl from "../_components/PlayControl";
import PlayRateSelector from "../_components/PlayRateSelector";
import { SeekBar } from "../_components/SeekBar";
import SoundWave from "../assets/soundWave.png";
import { usePlayStatus } from "../hooks/usePlayStatus";

export default memo(function AudioPlayer({
  src,
  title,
  onPlay,
  onPause,
  onCanPlay,
  audioRef,
}: {
  src: string;
  title?: string;
  onPlay?: () => void;
  onPause?: () => void;
  onCanPlay?: () => void;
  audioRef?: React.RefObject<HTMLAudioElement | null>;
}) {
  const internalAudioRef = useRef<HTMLAudioElement | null>(null);
  const {
    currentTime,
    playRate,
    duration,
    isPlaying,
    canPlay,
    handlePlay,
    handlePause,
    handleCanPlay,
    handleSeekTo,
    handleChangePlayRate,
    reset,
    handleTimeUpdate,
  } = usePlayStatus(internalAudioRef, {
    onPlay,
    onPause,
    onCanPlay,
  });

  useImperativeHandle<HTMLAudioElement | null, HTMLAudioElement | null>(
    audioRef,
    () => internalAudioRef.current
  );

  useEffect(() => {
    reset();
  }, [reset, src]);

  const handleBack = useCallback(() => {
    handleSeekTo(currentTime.value - 10);
  }, [handleSeekTo, currentTime]);
  const handleForward = useCallback(() => {
    handleSeekTo(currentTime.value + 10);
  }, [handleSeekTo, currentTime]);

  return (
    <div className="flex h-full w-full flex-1 flex-col overflow-hidden bg-[#F7F6F5] p-8 pt-2">
      {title !== undefined && (
        <div className="h-9.5 leading-9.5 text-center text-[0.8125rem] font-medium text-[#33302D8C]">
          {title}
        </div>
      )}
      <div className="flex flex-1 items-center justify-center overflow-hidden">
        <Image src={SoundWave} alt="sound" className="object-contain p-20" />
      </div>
      <div className="flex-0 flex h-12 gap-3">
        <BackBtn canPlay={canPlay} onClick={handleBack} />
        <PlayControl
          isPlaying={isPlaying}
          onPlay={handlePlay}
          onPause={handlePause}
          canPlay={canPlay}
        />
        <ForwardBtn canPlay={canPlay} onClick={handleForward} />
        <SeekBar
          currentTime={currentTime}
          duration={duration}
          seekTo={handleSeekTo}
          theme="dark"
        />
        <PlayRateSelector
          playRate={playRate}
          handleChangePlayRate={handleChangePlayRate}
        />
      </div>
      <audio
        key={src}
        ref={internalAudioRef}
        className="hidden"
        src={src}
        onCanPlay={handleCanPlay}
        onTimeUpdate={handleTimeUpdate}
        onPlay={handlePlay}
        onPause={handlePause}
      />
    </div>
  );
});
