/**
 * 最大触摸时间
 */
export const maxTouchTime = 200;

/**
 * 默认动画速度
 */
export const defaultSpeed = 400;

/**
 * 默认动画函数
 */
export const defaultEasing = 'cubic-bezier(0.25, 0.8, 0.25, 1)';

/**
 * 最大滑动切换图片距离
 */
export const maxMoveOffset = 40;

/**
 * 图片的间隔
 */
export const horizontalOffset = 20;

/**
 * 最小初始响应距离
 */
export const minStartTouchOffset = 20;

/**
 * 默认背景透明度
 */
export const defaultOpacity = 1;

/**
 * 最小缩放度
 */
export const minScale = 1;

/**
 * 最大缩放度（若图片足够大，则会超出）
 */
export const maxScale = 6;

/**
 * 最小长图模式比例
 */
export const longModeRatio = 3;

/**
 * 缩放弹性缓冲
 */
export const scaleBuffer = 0.2;

/**
 * 最大等待动画时间
 */
export const maxWaitAnimationTime = 250;
