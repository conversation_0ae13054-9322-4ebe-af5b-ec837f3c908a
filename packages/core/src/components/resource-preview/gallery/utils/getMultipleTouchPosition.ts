import type React from "react";

/**
 * 从 Touch 事件中获取两个触控中心位置
 */
export default function getMultipleTouchPosition(
  evt: TouchEvent | React.TouchEvent
): [clientX: number, clientY: number, touchLength: number] {
  const touches =
    (evt as TouchEvent).touches ?? (evt as React.TouchEvent).touches;
  if (!touches || touches.length === 0) {
    return [0, 0, 0];
  }
  const { clientX, clientY } = touches[0] ?? { clientX: 0, clientY: 0 };
  if (touches.length >= 2) {
    const { clientX: nextClientX, clientY: nextClientY } = touches[1] ?? {
      clientX: 0,
      clientY: 0,
    };
    return [
      (clientX + nextClientX) / 2,
      (clientY + nextClientY) / 2,
      Math.sqrt((nextClientX - clientX) ** 2 + (nextClientY - clientY) ** 2),
    ];
  }
  return [clientX, clientY, 0];
}
