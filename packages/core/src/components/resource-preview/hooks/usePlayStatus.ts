import { batch, useSignal } from "@preact-signals/safe-react";
import { useCallback, useEffect } from "react";

export const usePlayStatus = (
  ref: React.RefObject<HTMLAudioElement | HTMLVideoElement | null | undefined>,
  config?: {
    onPlay?: () => void;
    onPause?: () => void;
    onCanPlay?: () => void;
  }
) => {
  const isPlaying = useSignal(false);
  const canPlay = useSignal(false);
  const currentTime = useSignal(0);
  const duration = useSignal(0);
  const playRate = useSignal(1);
  const isPlayingBeforeDocumentHidden = useSignal(false);
  const { onPlay, onPause, onCanPlay } = config || {};

  const reset = useCallback(() => {
    isPlaying.value = false;
    canPlay.value = false;
    currentTime.value = 0;
    duration.value = 0;
    playRate.value = 1;
    isPlayingBeforeDocumentHidden.value = false;
    if (ref.current) {
      ref.current.currentTime = 0;
      ref.current.pause();
    }
  }, [
    canPlay,
    currentTime,
    duration,
    isPlaying,
    isPlayingBeforeDocumentHidden,
    playRate,
    ref,
  ]);

  const handlePlay = useCallback(() => {
    if (ref.current) {
      isPlaying.value = true;
      currentTime.value = ref.current.currentTime;
      ref.current.play();
      onPlay?.();
    }
  }, [currentTime, isPlaying, ref, onPlay]);

  const handlePause = useCallback(() => {
    if (ref.current) {
      isPlaying.value = false;
      currentTime.value = ref.current.currentTime;
      ref.current.pause();
      onPause?.();
    }
  }, [currentTime, isPlaying, ref, onPause]);

  const handleSeekTo = useCallback(
    (newTime: number) => {
      if (ref.current) {
        const clampedTime = Math.min(Math.max(newTime, 0), duration.value);
        currentTime.value = clampedTime;
        ref.current.currentTime = clampedTime;
      }
    },
    [currentTime, duration, ref]
  );

  const handleCanPlay = useCallback(() => {
    canPlay.value = true;
    onCanPlay?.();
    if (ref.current) {
      duration.value = ref.current.duration;
    }
  }, [canPlay, duration, ref, onCanPlay]);

  const handleChangePlayRate = useCallback(
    (rate: number) => {
      playRate.value = rate;
      if (ref.current) {
        ref.current.playbackRate = rate;
      }
    },
    [ref, playRate]
  );

  const handleTimeUpdate = useCallback(() => {
    if (ref.current) {
      currentTime.value = ref.current.currentTime;
    }
  }, [currentTime, ref]);

  useEffect(() => {
    const handleVisibilitychange = () => {
      if (document.visibilityState === "hidden") {
        batch(() => {
          isPlayingBeforeDocumentHidden.value = isPlaying.peek();
          isPlaying.value = false;
        });
      } else {
        isPlaying.value = isPlayingBeforeDocumentHidden.peek();
      }
    };
    document.addEventListener("visibilitychange", handleVisibilitychange);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilitychange);
    };
  }, [isPlaying, isPlayingBeforeDocumentHidden]);

  return {
    isPlaying,
    canPlay,
    currentTime,
    duration,
    playRate,
    handlePlay,
    handlePause,
    handleSeekTo,
    handleTimeUpdate,
    handleCanPlay,
    handleChangePlayRate,
    reset,
  };
};
