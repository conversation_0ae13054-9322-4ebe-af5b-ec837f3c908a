import { useCallback, useEffect, useRef, useState } from "react";

export type EasyRequestOptions<TParams extends unknown[], TData> = {
  data: TData | undefined;
  loading: boolean;
  error: Error | undefined;
  runAsync: (...params: TParams) => Promise<TData>;
};

type Request<TParams extends unknown[], TData> = (
  ...params: TParams
) => Promise<TData>;

export default function useEasyRequest<TParams extends unknown[], TData>(
  request: Request<TParams, TData>,
  //   暂时只支持ready\refreshDeps\cacheKey\manual
  options?: {
    ready?: boolean;
    refreshDeps?: unknown[];
    cacheKey?: string;
    manual?: boolean;
    defaultParams?: TParams;
  }
): EasyRequestOptions<TParams, TData> {
  const [{ data, loading, error, params }, setState] = useState<{
    data: TData | undefined;
    loading: boolean;
    error: Error | undefined;
    params: TParams | undefined;
  }>({
    data: undefined,
    loading: false,
    error: undefined,
    params: undefined,
  });

  const countRef = useRef(0);

  const requestRef = useRef(request);
  requestRef.current = request;

  const runAsync = useCallback(async (...params: TParams) => {
    countRef.current++;
    const currentCount = countRef.current;
    setState((prev) => ({ ...prev, loading: true, error: undefined }));
    try {
      const res = await requestRef.current(...params);
      if (currentCount === countRef.current) {
        setState(() => ({
          data: res,
          loading: false,
          error: undefined,
          params,
        }));
      }
      return res;
    } catch (error) {
      if (currentCount === countRef.current) {
        setState(() => ({
          data: undefined,
          loading: false,
          error: error as Error,
          params,
        }));
        throw error;
      } else {
        return Promise.reject(error);
      }
    }
  }, []);

  const mountedRef = useRef(false);

  useEffect(() => {
    if (mountedRef.current) {
      return;
    }
    if (options?.ready !== false && options?.manual !== true) {
      const calcParams = params || options?.defaultParams;
      if (calcParams) {
        runAsync(...calcParams);
      }
    }
  }, [
    options?.defaultParams,
    options?.manual,
    options?.ready,
    params,
    runAsync,
  ]);

  const hasAutoRun = useRef(false);
  useEffect(() => {
    if (!mountedRef.current || hasAutoRun.current) {
      return;
    }
    if (options?.manual !== true) {
      hasAutoRun.current = true;
      const calcParams =
        params || options?.defaultParams || ([] as unknown as TParams); // stupid ts
      runAsync(...calcParams)
        .then(() => {
          hasAutoRun.current = false;
        })
        .catch(() => {
          hasAutoRun.current = false;
        });
    }
  }, [...(options?.refreshDeps || [])]); // 这里就是只需要监听refreshDeps，其他的options变了也不关注

  useEffect(() => {
    mountedRef.current = true;
  }, []);

  return {
    data,
    loading,
    error,
    runAsync,
  };
}
