import { useSignal } from "@preact-signals/safe-react";
import { cn } from "@repo/ui/lib/utils";
import { FC, useCallback } from "react";
import { TranslucentGlassButton } from "../_components/GlassButton";
import { SeekBar } from "../_components/SeekBar";
import IconSpin from "../assets/animate-spin.svg";
import IconFastBackward from "../assets/back-white.svg";
import IconFastForward from "../assets/forward-white.svg";
import IconPause from "../assets/pause-white.svg";
import IconPlay from "../assets/play-white.svg";
import RectangleBottom from "../assets/rectangle-bottom.svg";
import { useVideoViewContext } from "./video-view-context";

const formatRate = (rate: number) => {
  return `${Number.isInteger(rate) ? rate.toFixed(1) : rate}x`;
};

const RateSelector: FC<{
  onClose?: () => void;
  className?: string;
}> = ({ onClose, className }) => {
  const rates = [0.75, 1.0, 1.25, 1.5, 1.75, 2.0];

  const { playRate } = useVideoViewContext();

  const handleRateChange = (rate: number) => {
    playRate.value = rate;
    onClose?.();
  };

  return (
    <div className={cn("absolute -left-1 bottom-16 w-max", className)}>
      <ul className="flex w-20 flex-col gap-2 rounded-xl bg-[#1F1D1B99] from-white to-[#FEFDFA] p-1 shadow-[-4px_0px_8px_0px_rgba(64,43,26,0.05)] backdrop-blur-[5px]">
        {rates.map((rate, index) => (
          <ol
            key={index}
            onClick={() => handleRateChange(rate)}
            className={cn(
              "flex h-10 w-full select-none items-center justify-center rounded-lg p-2 text-base font-bold leading-tight text-white transition-colors",
              rate === playRate.value ? "bg-primary-1" : ""
            )}
          >
            {formatRate(rate)}
          </ol>
        ))}
      </ul>
      <RectangleBottom className="absolute -bottom-1.5 left-1/2 -translate-x-1/2" />
    </div>
  );
};

export const VideoControllerView: FC = () => {
  const {
    isPlaying,
    forwardTo,
    togglePlay,
    playRate,
    showPlayerControls,
    canPlay,
    currentTime,
    duration,
    seekTo,
  } = useVideoViewContext();
  const showPlayRateSelector = useSignal(false);
  const handleForwardTo = useCallback(
    (seconds: number) => {
      forwardTo(seconds);
      // trackEventWithLessonId("doc_seek_10s_click");
    },
    [forwardTo]
  );

  // const durationSignal = useComputed(() => duration);

  if (!showPlayerControls) {
    return null;
  }
  return (
    <div className="absolute bottom-8 left-0 flex h-12 w-full flex-row gap-3 px-8">
      <div className="relative flex flex-row items-center">
        <TranslucentGlassButton
          className="w-18 h-12 bg-[#1F1D1B99] font-bold text-white"
          onClick={() =>
            (showPlayRateSelector.value = !showPlayRateSelector.value)
          }
        >
          {formatRate(playRate.value)}
        </TranslucentGlassButton>
        {showPlayRateSelector.value && (
          <RateSelector onClose={() => (showPlayRateSelector.value = false)} />
        )}
      </div>
      <div className="flex-1">
        <SeekBar
          currentTime={currentTime}
          duration={duration}
          seekTo={seekTo}
          theme="dark"
        />
      </div>
      <div className="flex flex-row items-center gap-3">
        <TranslucentGlassButton
          className="size-12 bg-[#1F1D1B99]"
          onClick={() => handleForwardTo(-10)}
          icon={<IconFastBackward />}
        />
        <TranslucentGlassButton
          className="w-18 h-12 bg-[#1F1D1B99]"
          onClick={togglePlay}
          icon={
            !canPlay ? (
              <IconSpin className="size-6 animate-spin" />
            ) : isPlaying ? (
              <IconPause />
            ) : (
              <IconPlay />
            )
          }
        />
        <TranslucentGlassButton
          className="size-12 bg-[#1F1D1B99]"
          onClick={() => handleForwardTo(10)}
          icon={<IconFastForward />}
        />
      </div>
    </div>
  );
};
