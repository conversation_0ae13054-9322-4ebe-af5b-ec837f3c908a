import {
  ReadonlySignal,
  Signal,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";

import { VolcenginePlayer } from "@repo/core/components/volcengine-video/volcengine-video";
import {
  createContext,
  FC,
  useCallback,
  useContext,
  useImperativeHandle,
} from "react";

type VideoViewContextType = {
  title?: string;
  url: string;
  refVolcenginePlayer: Signal<VolcenginePlayer | null>;
  canPlay: boolean;
  isPlaying: boolean;
  forwardTo: (seconds: number) => void;
  seekTo: (seconds: number) => void;
  togglePlay: () => void;
  togglePlayerControls: () => void;
  playRate: Signal<number>;
  set3XPlayRate: () => void;
  resetPlayRate: () => void;
  currentTime: ReadonlySignal<number>;
  showPlayerControls: boolean;
  duration: Signal<number>;
  userId?: string;
};

const VideoViewContext = createContext<VideoViewContextType>(
  {} as VideoViewContextType
);

export const useVideoViewContext = () => {
  return useContext(VideoViewContext);
};

interface VideoViewContextProviderProps {
  children: React.ReactNode;
  src: string;
  title?: string;
  defaultPlayRate?: number;
  volcenginePlayerRef?: React.RefObject<VolcenginePlayer | null>;
  userId?: string;
  onPlay?: () => void;
  onPause?: () => void;
  onError?: (error?: unknown) => void;
  onCanPlay?: () => void;
}

export const VideoViewContextProvider: FC<VideoViewContextProviderProps> = ({
  src,
  title,
  defaultPlayRate,
  volcenginePlayerRef,
  children,
  userId,
  onPlay,
  onPause,
  onError,
  onCanPlay,
}) => {
  const refVolcenginePlayer = useSignal<VolcenginePlayer | null>(null);
  // TODO: test this
  useImperativeHandle<VolcenginePlayer | null, VolcenginePlayer | null>(
    volcenginePlayerRef,
    () => refVolcenginePlayer.peek()
  );
  const canPlay = useSignal(false);
  const isPlaying = useSignal(false);
  const playRateBeforeLongPress = useSignal(1);
  const currentTime = useSignal(0);
  const duration = useSignal(0);

  const playRate = useSignal(defaultPlayRate ?? 1);

  const handlePlay = useCallback(() => {
    isPlaying.value = true;
    onPlay?.();
  }, [isPlaying, onPlay]);
  const handlePause = useCallback(() => {
    isPlaying.value = false;
    onPause?.();
  }, [isPlaying, onPause]);

  const forwardTo = useCallback(
    (seconds: number) => {
      if (!refVolcenginePlayer.value) {
        return;
      }
      const player = refVolcenginePlayer.value;
      player.seek(player.currentTime + seconds);
      handlePlay();
    },
    [refVolcenginePlayer.value, handlePlay]
  );
  const seekTo = useCallback(
    (seconds: number) => {
      if (!refVolcenginePlayer.value) {
        return;
      }
      const player = refVolcenginePlayer.value;
      player.seek(seconds);
      handlePlay();
    },
    [refVolcenginePlayer.value, handlePlay]
  );

  const togglePlay = useCallback(() => {
    isPlaying.value = !isPlaying.peek();
    if (isPlaying.value) {
      handlePlay();
    } else {
      handlePause();
    }
  }, [isPlaying, handlePlay, handlePause]);

  const set3XPlayRate = useCallback(() => {
    if (playRate.value === 3) return;
    playRateBeforeLongPress.value = playRate.value;
    playRate.value = 3;
  }, [playRate, playRateBeforeLongPress]);

  const resetPlayRate = useCallback(() => {
    if (playRate.value === playRateBeforeLongPress.value) return;
    playRate.value = playRateBeforeLongPress.value;
  }, [playRate, playRateBeforeLongPress]);

  useSignalEffect(() => {
    const player = refVolcenginePlayer.value;
    if (!player) return;

    const handleWaiting = () => {
      canPlay.value = false;
    };
    const handlePlaying = () => {
      canPlay.value = true;
    };
    const handleCanplay = () => {
      canPlay.value = true;
      onCanPlay?.();
    };
    const handleError = (error: unknown) => {
      canPlay.value = false;
      onError?.(error);
    };
    const handleTimeUpdate = () => {
      currentTime.value = player.currentTime;
    };

    const handleDurationChange = (e: unknown) => {
      const { originalEvent } = e as {
        originalEvent: { target: { duration: number } };
      };
      duration.value = originalEvent?.target?.duration ?? 0;
    };

    player.addEventListener("waiting", handleWaiting);
    player.addEventListener("playing", handlePlaying);
    player.addEventListener("canplay", handleCanplay);
    player.addEventListener("error", handleError);
    player.addEventListener("timeupdate", handleTimeUpdate);
    player.addEventListener("durationchange", handleDurationChange);
    return () => {
      player.removeEventListener("waiting", handleWaiting);
      player.removeEventListener("playing", handlePlaying);
      player.removeEventListener("canplay", handleCanplay);
      player.removeEventListener("error", handleError);
      player.removeEventListener("timeupdate", handleTimeUpdate);
      player.removeEventListener("durationchange", handleDurationChange);
    };
  });

  useSignalEffect(() => {
    if (refVolcenginePlayer.value === null) return;

    if (isPlaying.value && canPlay.value) {
      refVolcenginePlayer.value?.play();
    } else {
      refVolcenginePlayer.value?.pause();
    }
  });

  const showPlayerControls = useSignal(true);

  const togglePlayerControls = useCallback(() => {
    showPlayerControls.value = !showPlayerControls.peek();
  }, [showPlayerControls]);

  const value = {
    title,
    url: src,
    refVolcenginePlayer,
    canPlay: canPlay.value,
    isPlaying: isPlaying.value,
    forwardTo,
    seekTo,
    togglePlay,
    togglePlayerControls,
    set3XPlayRate,
    resetPlayRate,
    showPlayerControls: showPlayerControls.value,
    playRate,
    currentTime,
    userId,
    duration: duration,
  };

  return <VideoViewContext value={value}>{children}</VideoViewContext>;
};
