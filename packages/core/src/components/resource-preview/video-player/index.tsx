import { cn } from "@repo/ui/lib/utils";
import type { VolcenginePlayer } from "../../volcengine-video/volcengine-video";
import { VideoControllerView } from "./video-controller-view";
import { VideoPlayerView } from "./video-player-view";
import { VideoViewContextProvider } from "./video-view-context";

import { memo } from "react";
import "./overwrite.css";
import VideoTitle from "./video-title";

export type VideoPlayerProps = {
  src: string;
  title?: string;
  defaultPlayRate?: number;
  userId?: string;
  onPlay?: () => void;
  onPause?: () => void;
  onError?: (error?: unknown) => void;
  onCanPlay?: () => void;
  refVolcenginePlayer?: React.RefObject<VolcenginePlayer | null>;
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
};

export default memo(function VideoPlayer({
  className,
  style,
  title,
  ...props
}: VideoPlayerProps) {
  return (
    <div className={cn("relative flex", className)} style={style}>
      <VideoViewContextProvider {...props}>
        <VideoTitle title={title} />
        <VideoPlayerView className="flex h-full w-full items-center justify-center" />
        <VideoControllerView />
      </VideoViewContextProvider>
    </div>
  );
});
