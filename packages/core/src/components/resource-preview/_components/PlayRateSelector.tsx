import { Signal, useSignal } from "@preact-signals/safe-react";
import { cn } from "@repo/ui/lib/utils";
import { TranslucentGlassButton } from "./GlassButton";

import { useClickAway } from "ahooks";
import { useRef } from "react";
import RectangleBottom from "../assets/rectangle-bottom.svg";

const rates = [0.75, 1.0, 1.25, 1.5, 1.75, 2.0];

const formatRate = (rate: number) => {
  if (Number.isInteger(rate)) {
    return `${rate}.0x`;
  }
  return `${rate}x`;
};

export default function RateSelector({
  playRate,
  handleChangePlayRate,
}: {
  playRate: Signal<number>;
  handleChangePlayRate: (rate: number) => void;
}) {
  const showPlayRateSelector = useSignal(false);

  const handleRateChange = (rate: number) => {
    handleChangePlayRate(rate);
    showPlayRateSelector.value = false;
  };

  const ref = useRef<HTMLDivElement>(null);
  useClickAway(() => {
    showPlayRateSelector.value = false;
  }, ref);

  return (
    <div className="relative">
      <TranslucentGlassButton
        className="w-18 min-w-18 h-12 bg-[#1F1D1B99] font-bold text-white"
        onClick={() =>
          (showPlayRateSelector.value = !showPlayRateSelector.value)
        }
      >
        {formatRate(playRate.value)}
      </TranslucentGlassButton>

      {showPlayRateSelector.value ? (
        <div ref={ref} className="z-1 absolute -left-1 bottom-16 w-max">
          <ul className="flex w-20 flex-col gap-2 rounded-xl bg-[#1F1D1B99]/80 from-white to-[#FEFDFA] p-1 shadow-[-4px_0px_8px_0px_rgba(64,43,26,0.05)] backdrop-blur-[5px]">
            {rates.map((rate, index) => (
              <ol
                key={index}
                onClick={() => handleRateChange(rate)}
                className={cn(
                  "flex h-10 w-full cursor-pointer select-none items-center justify-center rounded-lg p-2 text-base font-bold leading-tight transition-colors hover:opacity-80",
                  rate === playRate.value
                    ? "bg-[#FF7B59] text-white"
                    : "text-white"
                )}
              >
                {formatRate(rate)}
              </ol>
            ))}
          </ul>
          <RectangleBottom className="absolute -bottom-1.5 left-1/2 -translate-x-1/2" />
        </div>
      ) : null}
    </div>
  );
}
