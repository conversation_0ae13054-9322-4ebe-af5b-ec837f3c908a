import { Signal } from "@preact-signals/safe-react";
import IconSpin from "../assets/animate-spin.svg";
import { TranslucentGlassButton } from "./GlassButton";

import IconPause from "../assets/pause-white.svg";
import IconPlay from "../assets/play-white.svg";
export default function PlayControl({
  isPlaying,
  onPlay,
  onPause,
  canPlay,
}: {
  isPlaying: Signal<boolean>;
  onPlay?: () => void;
  onPause?: () => void;
  canPlay: Signal<boolean>;
}) {
  return (
    <TranslucentGlassButton
      className="w-18 h-12 bg-[#1F1D1B99]"
      onClick={isPlaying.value ? onPause : onPlay}
      disabled={!canPlay.value}
      icon={
        !canPlay.value ? (
          <IconSpin className="size-6 animate-spin" />
        ) : isPlaying.value ? (
          <IconPause />
        ) : (
          <IconPlay />
        )
      }
    />
  );
}
