import { cn } from "@repo/ui/lib/utils";
import { ComponentProps, FC, ReactNode } from "react";

// 8.15
// Same as @/app/components/guide/guide-buttons.tsx TranslucentGlassButton
export const TranslucentGlassButton: FC<
  { icon?: ReactNode } & ComponentProps<"button">
> = ({ icon, children, className, ...props }) => {
  return (
    <button
      className={cn(
        "inline-flex h-11 cursor-pointer items-center justify-center gap-1 rounded-xl bg-white px-3 text-zinc-800/90 opacity-80 shadow-[0px_4px_16px_0px_rgba(35,42,64,0.05)] outline-1 outline-offset-[-1px] outline-white",
        className
      )}
      {...props}
    >
      {icon !== undefined ? <div className="size-auto">{icon}</div> : null}
      {children !== undefined ? (
        <div className="text-base font-bold leading-tight">{children}</div>
      ) : null}
    </button>
  );
};
