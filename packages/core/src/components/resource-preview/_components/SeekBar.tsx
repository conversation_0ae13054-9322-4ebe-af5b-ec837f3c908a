import { Signal, useComputed } from "@preact-signals/safe-react";
import { formatTime } from "@repo/lib/utils/time";
import { Progress } from "@repo/ui/components/progress";
import { cn } from "@repo/ui/lib/utils";
import { FC, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { interpolate } from "remotion";
import { useThrottledCallback } from "use-debounce";
import Rectangle from "../assets/Rectangle.svg";

type Size = {
  width: number;
  height: number;
  left: number;
  top: number;
};

// If a pane has been moved, it will cause a layout shift without
// the window having been resized. Those UI elements can call this API to
// force an update
const useElementSize = (
  ref: React.RefObject<HTMLElement | null>
): Size | null => {
  const [size, setSize] = useState<Size | null>(() => {
    if (!ref.current) {
      return null;
    }

    const rect = ref.current.getClientRects();
    if (!rect[0]) {
      return null;
    }

    return {
      width: rect[0].width as number,
      height: rect[0].height as number,
      left: rect[0].x as number,
      top: rect[0].y as number,
    };
  });

  const observer = useMemo(() => {
    if (typeof ResizeObserver === "undefined") {
      return null;
    }

    return new ResizeObserver((entries) => {
      if (!entries[0]) {
        return;
      }
      const { target } = entries[0];
      const newSize = target.getClientRects();

      if (!newSize?.[0]) {
        setSize(null);
        return;
      }

      const { width } = newSize[0];

      const { height } = newSize[0];

      setSize({
        width,
        height,
        left: newSize[0].x,
        top: newSize[0].y,
      });
    });
  }, []);

  const updateSize = useCallback(() => {
    if (!ref.current) {
      return;
    }

    const rect = ref.current.getClientRects();
    if (!rect[0]) {
      setSize(null);
      return;
    }

    setSize((prevState) => {
      if (!rect[0]) {
        return prevState;
      }

      const isSame =
        prevState !== null &&
        prevState.width === rect[0].width &&
        prevState.height === rect[0].height &&
        prevState.left === rect[0].x &&
        prevState.top === rect[0].y;
      if (isSame) {
        return prevState;
      }

      return {
        width: rect[0].width as number,
        height: rect[0].height as number,
        left: rect[0].x as number,
        top: rect[0].y as number,
        windowSize: {
          height: window.innerHeight,
          width: window.innerWidth,
        },
      };
    });
  }, [ref]);

  useEffect(() => {
    if (!observer) {
      return;
    }

    const { current } = ref;
    if (current) {
      observer.observe(current);
    }

    return (): void => {
      if (current) {
        observer.unobserve(current);
      }
    };
  }, [observer, ref, updateSize]);

  useEffect(() => {
    window.addEventListener("resize", updateSize);

    return () => {
      window.removeEventListener("resize", updateSize);
    };
  }, [updateSize]);

  return useMemo(() => {
    if (!size) {
      return null;
    }

    return { ...size, refresh: updateSize };
  }, [size, updateSize]);
};

const calTimeFromX = (clientX: number, duration: number, width: number) => {
  const pos = clientX;

  const time = Math.round(
    interpolate(pos, [0, width], [0, Math.max(duration, 0)], {
      extrapolateLeft: "clamp",
      extrapolateRight: "clamp",
    })
  );
  return time;
};

const findBodyInWhichDivIsLocated = (div: HTMLElement) => {
  let current = div;

  while (current.parentElement) {
    current = current.parentElement;
  }

  return current;
};

export const SeekBar: FC<{
  currentTime: Signal<number>;
  duration: Signal<number>;
  seekTo: (time: number) => void;
  className?: string;
  theme?: "dark" | "light";
}> = ({ currentTime, duration, seekTo, className, theme = "light" }) => {
  // const isDragging = useSignal(false);
  const [isDragging, setIsDragging] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  const size = useElementSize(ref);
  const progress = useComputed(() => {
    return (currentTime.value / duration.value) * 100;
  });

  const time = useComputed(() => {
    return `${formatTime(currentTime.value)} / ${formatTime(duration.value)}`;
  });

  const width = size?.width ?? 0;

  const throttledSeekTo = useThrottledCallback(seekTo, 100);

  const onPointerDown = useCallback(
    (e: React.TouchEvent<HTMLDivElement>) => {
      const touch = e.touches[0];
      if (!touch) {
        return;
      }
      setIsDragging(true);

      if (!ref.current) {
        return;
      }

      const posLeft = ref.current.getBoundingClientRect().left;

      const _time = calTimeFromX(
        touch.clientX - posLeft,
        duration.value,
        width
      );
      throttledSeekTo(_time);
    },
    [duration, width, throttledSeekTo]
  );

  const onMouseDown = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      setIsDragging(true);
      const { clientX } = e;
      if (!ref.current) {
        return;
      }
      const { left } = ref.current.getBoundingClientRect();
      const _time = calTimeFromX(clientX - left, duration.value, width);
      throttledSeekTo(_time);
    },
    [duration, width, throttledSeekTo, setIsDragging]
  );

  const onMouseMove = useCallback(
    function (e: MouseEvent) {
      if (!isDragging) {
        return;
      }
      const { clientX } = e;
      if (!ref.current) {
        return;
      }
      const { left } = ref.current.getBoundingClientRect();
      const _time = calTimeFromX(clientX - left, duration.value, width);
      throttledSeekTo(_time);
    },
    [isDragging, duration.value, width, throttledSeekTo]
  );

  const onPointerMove = useCallback(
    (e: TouchEvent) => {
      if (!size) {
        return;
      }
      if (!isDragging) {
        return;
      }
      const touch = e.touches[0];
      if (!touch) {
        return;
      }

      if (!ref.current) {
        return;
      }

      const posLeft = ref.current.getBoundingClientRect().left;
      if (!posLeft) {
        return;
      }

      const _time = calTimeFromX(
        touch.clientX - posLeft,
        duration.value,
        size.width
      );
      throttledSeekTo(_time);
    },
    [duration, size, throttledSeekTo, isDragging]
  );

  const handleDragStop = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    const body = findBodyInWhichDivIsLocated(ref.current as HTMLElement);

    body.addEventListener("touchend", handleDragStop, { capture: true });
    body.addEventListener("touchmove", onPointerMove, { capture: true });
    body.addEventListener("mouseup", handleDragStop);
    if (isDragging) {
      body.addEventListener("mousemove", onMouseMove);
    }
    return () => {
      body.removeEventListener("touchend", handleDragStop);
      body.removeEventListener("touchmove", onPointerMove);
      body.removeEventListener("mouseup", handleDragStop);
      body.removeEventListener("mousemove", onMouseMove);
    };
  }, [isDragging, handleDragStop, onPointerMove, onMouseMove]);

  return (
    <div
      className={cn(
        "flex h-12 w-full flex-row items-center justify-center gap-4 rounded-xl px-5",
        theme === "dark" ? "bg-[#1f1d1b7a]" : "bg-[#1F1D1B0D]",
        className
      )}
    >
      <div className="relative flex-1 cursor-pointer">
        {isDragging && (
          <div
            className="bg-red absolute -top-20 flex flex-col items-center justify-end"
            style={{
              left: `${progress.value}%`,
              transform: "translateX(-50%)",
            }}
          >
            <div
              className={cn(
                "flex w-max flex-row items-center justify-center rounded-lg px-3 py-2 text-center text-2xl font-normal text-white",
                theme === "dark"
                  ? "bg-[#1F1D1B99] text-[#BDB4AD]"
                  : "bg-stone-900/60"
              )}
            >
              {time.value}
            </div>
            <Rectangle
              className={cn(theme === "dark" ? "fill-white" : "fill-stone-900")}
            />
          </div>
        )}
        <Progress
          ref={ref}
          onMouseDown={onMouseDown}
          onTouchStart={onPointerDown}
          className={cn(
            "h-2 w-full rounded-sm *:rounded-sm",
            // *:bg-zinc-800/90 bg-zinc-800/20
            theme === "dark"
              ? "bg-[#ffffff33] *:bg-white"
              : "bg-zinc-800/20 *:bg-zinc-800/90"
          )}
          indicatorClassName={cn(
            theme === "dark" ? "bg-white" : "bg-zinc-800/90"
          )}
          value={progress.value}
          max={100}
        />
      </div>
      <div
        className={cn(
          "text-xs font-medium leading-none",
          theme === "dark" ? "text-[#BDB4AD]" : "text-[#33302D8C]"
        )}
      >
        {time.value}
      </div>
    </div>
  );
};
