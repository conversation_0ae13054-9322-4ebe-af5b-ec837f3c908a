import { Signal } from "@preact-signals/safe-react";
import BackSvg from "../assets/back-white.svg";
import { TranslucentGlassButton } from "./GlassButton";

export default function BackBtn({
  canPlay,
  onClick,
}: {
  canPlay: Signal<boolean>;
  onClick?: () => void;
}) {
  return (
    <TranslucentGlassButton
      className="size-12 bg-[#1F1D1B99]"
      disabled={!canPlay.value}
      onClick={onClick}
    >
      <BackSvg />
    </TranslucentGlassButton>
  );
}
