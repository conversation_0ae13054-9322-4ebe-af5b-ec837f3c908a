import { Signal } from "@preact-signals/safe-react";
import IconFastForward from "../assets/forward-white.svg";
import { TranslucentGlassButton } from "./GlassButton";

export default function ForwardBtn({
  canPlay,
  onClick,
}: {
  canPlay: Signal<boolean>;
  onClick?: () => void;
}) {
  return (
    <TranslucentGlassButton
      className="size-12 bg-[#1F1D1B99]"
      disabled={!canPlay.value}
      onClick={onClick}
    >
      <IconFastForward />
    </TranslucentGlassButton>
  );
}
