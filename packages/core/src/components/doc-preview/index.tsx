"use client";

import { cn } from "@repo/ui/lib/utils";
import { memo, useCallback, useEffect, useRef } from "react";
import { IMMInstance, WebTokenInfo } from "./type";
import useMobileAdapt from "./useMobileAdapt";

export type WebOfficePreviewProps = {
  tokenInfo?: WebTokenInfo;
  scriptLoaded: boolean;
  refreshTokenRequest?: () => Promise<{
    token: string;
    timeout: number; // ms
  }>;
  officeInstanceRef?: React.RefObject<IMMInstance | null>;

  onPreview?: (instance: IMMInstance) => void;

  // 功能增强
  enableMobileAdapt?: boolean;
  enableJumpToPPTFirstSlide?: boolean;

  className?: string;
  style?: React.CSSProperties;
};

// NOTE：这个组件受限于阿里SDK本身，不支持同一个页面内多个实例，如有需要，可参考：https://help.aliyun.com/zh/imm/user-guide/preview-or-edit-multi-docs
function WebOfficePreview({
  className,

  tokenInfo,
  scriptLoaded,
  refreshTokenRequest,

  officeInstanceRef,
  onPreview,

  // PPT默认会记录上次预览的位置
  enableJumpToPPTFirstSlide = false,
  // 是否开启移动端自适应，根据UA判断是否是移动端，如果已经修改了UA，则不用开启
  enableMobileAdapt = false,
}: WebOfficePreviewProps) {
  const ref = useRef<HTMLDivElement>(null);
  const instanceRef = useRef<IMMInstance | null>(null);

  // 移动端适配，如果已经修改了UA，则可以禁用
  const adaptMobile = useMobileAdapt(enableMobileAdapt);

  const jumpToPPTFirstSlide = useCallback(
    async (instance: IMMInstance) => {
      if (!enableJumpToPPTFirstSlide) {
        return;
      }
      await instance.ready();
      const app = instance.Application;
      await app.ActivePresentation?.SlideShowWindow?.View?.GotoSlide(1);
    },
    [enableJumpToPPTFirstSlide]
  );

  const handlePreview = useCallback(
    async (instance: IMMInstance) => {
      instance.ApiEvent.AddApiEventListener("fileOpen", () => {
        onPreview?.(instance);
      });

      await instance.ready();
    },
    [onPreview]
  );

  useEffect(() => {
    if (tokenInfo && scriptLoaded && window.aliyun && ref.current) {
      const { accessToken, webofficeURL } = tokenInfo;
      const weboffice = window.aliyun.config({
        url: webofficeURL,
        mount: ref.current,
        mode: "simple",
        refreshToken: refreshTokenRequest,
      });

      instanceRef.current?.destroy?.();

      // 如果有刷新token的函数，就提前5分钟刷新，否则就不传
      const timeout = refreshTokenRequest
        ? Date.parse(tokenInfo.accessTokenExpiredTime) -
          Date.now() -
          5 * 60 * 1000
        : undefined;

      if (officeInstanceRef) {
        officeInstanceRef.current = weboffice;
      }

      instanceRef.current = weboffice;
      handlePreview(weboffice);

      weboffice.setToken({
        token: accessToken,
        timeout,
      });

      // PPT默认记录上次预览的位置，但大家共享一个账号，一进来指不定跳到哪里，所以需要跳到第一页
      jumpToPPTFirstSlide(weboffice);

      adaptMobile(weboffice);
    }
    return () => {
      if (instanceRef.current) {
        instanceRef.current.destroy?.();
        instanceRef.current.iframe?.remove?.();
        instanceRef.current = null;
      }
    };
  }, [
    tokenInfo,
    scriptLoaded,
    refreshTokenRequest,
    adaptMobile,
    officeInstanceRef,
    jumpToPPTFirstSlide,
    handlePreview,
  ]);

  return (
    <div ref={ref} className={cn("relative h-full w-full flex-1", className)} />
  );
}

export default memo(WebOfficePreview);
