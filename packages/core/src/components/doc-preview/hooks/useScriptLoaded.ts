import { useSignal } from "@preact-signals/safe-react";
import { useCallback } from "react";

export type ScriptStatus = "loading" | "loaded" | "failed";

export type ScriptLoadedCB = () => void;
export type ScriptLoadErrorCB = (error: Error) => void;

export default function useScriptLoaded(
  onScriptLoaded?: ScriptLoadedCB,
  onScriptLoadError?: ScriptLoadErrorCB
) {
  const status = useSignal<ScriptStatus>("loading");

  const handleScriptLoad = useCallback(() => {
    status.value = "loaded";
    onScriptLoaded?.();
  }, [status, onScriptLoaded]);

  const handleScriptLoadError = useCallback(
    (error: Error) => {
      status.value = "failed";
      onScriptLoadError?.(error);
    },
    [status, onScriptLoadError]
  );

  return {
    status,
    handleScriptLoad,
    handleScriptLoadError,
  };
}
