"use client";

import { CourseSummary } from "@repo/core/course/course-summary";
import { FC } from "react";
import { genDefaultTitle, usePreviewCtx } from "../_helper";

export const Sider: FC = () => {
  const { active, setActive, widgets } = usePreviewCtx();

  const goto = (idx: number) => {
    setActive(idx);
  };

  return (
    <div className="h-[calc(100vh-80px)] w-80 overflow-y-scroll p-2">
      {widgets.map((summary, idx) => {
        const title = genDefaultTitle(summary);
        const showTitle =
          title.length > 20 ? title.slice(0, 20) + "..." : title;
        return (
          <div
            className="cursor-pointer"
            key={idx}
            title={title}
            style={{ display: summary.hidden === 1 ? "none" : "block" }}
          >
            <CourseSummary
              onSelect={() => goto(idx)}
              key={idx}
              title={showTitle}
              status={summary.status}
              isCurrent={idx === active}
              isLast={idx === widgets.length - 1}
              index={idx}
              type={summary.type}
            />
          </div>
        );
      })}
    </div>
  );
};
