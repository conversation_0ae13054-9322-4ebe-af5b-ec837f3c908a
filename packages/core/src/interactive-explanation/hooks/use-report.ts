import { AiExplanationRecord } from "@repo/core/types";
import { useCallback } from "react";
import { useReportInteractiveExplanation } from "../model";

export const useReport = () => {
  const { reportInteractiveExplanation } = useReportInteractiveExplanation(); // 上报进度的函数

  const reportProgress = useCallback(
    async ({
      studySessionId,
      questionId,
      updatedRecord,
    }: {
      studySessionId: number;
      questionId: string;
      updatedRecord: AiExplanationRecord;
    }) => {
      if (!studySessionId) {
        return;
      }
      const payload = {
        questionId,
        studySessionId,
        version: updatedRecord.version,
        record: {
          stepByStepGuideCurrentProgress:
            updatedRecord.stepByStepGuideCurrentProgress,
          currentProgress: updatedRecord.currentProgress,
          stepByStepGuide: updatedRecord.stepByStepGuide.map((step) => ({
            isAnswer: step.isAnswer,
          })),
        },
      };

      console.log("🚀 准备上报进度:", payload);

      try {
        const result = await reportInteractiveExplanation(payload);
        console.log("✅ 进度上报成功:", result);
      } catch (error) {
        console.error("❌ 进度上报失败:", error);
      }
    },
    [reportInteractiveExplanation]
  ); // 当前阶段映射
  return {
    reportProgress,
  };
};
