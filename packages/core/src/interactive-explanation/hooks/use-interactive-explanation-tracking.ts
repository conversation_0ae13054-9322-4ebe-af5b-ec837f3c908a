/**
 * 互动讲题埋点 Hook (最终手动版)
 * 导出的函数拥有稳定的引用，内部逻辑清晰，彻底解决重复调用问题。
 */
import { StudyType } from "@repo/core/enums";
import { AiExplanationRecord } from "@repo/core/types";
import { trackEvent } from "@repo/core/utils/stu/device";
import { usePathname } from "next/navigation";
import { useCallback, useEffect, useRef } from "react";
import { StageType } from "../types";

// ===== 类型定义 (保持不变) =====
type StageMapping = StageType[];
export interface UseInteractiveExplanationTrackingParams {
  lessonId?: string | number;
  questionId: string | number;
  studySessionId?: number;
  currentRecord: AiExplanationRecord;
  stageMapping: StageMapping;
  preview?: boolean;
  studyType: StudyType;
}
// ... 其他类型定义保持不变

export const INTERACTIVE_EXPLANATION_EVENTS = {
  ENTER: "interactive_explanation_enter",
  EXIT: "interactive_explanation_exit",
  STEP_VIEW: "interactive_explanation_step_view",
  STEP_JUMP: "interactive_explanation_step_jump",
  STEP_EXIT: "interactive_explanation_step_exit",
} as const;
export type InteractiveExplanationEventName =
  (typeof INTERACTIVE_EXPLANATION_EVENTS)[keyof typeof INTERACTIVE_EXPLANATION_EVENTS];
export type EnterExitParams = { source: string };
export type StepViewParams = {
  step_type: StageType;
  step_index: number; // 步骤索引
  stay_time: number;
  // 仅在逐步讲解阶段有效
  sub_step_index?: number;
};
// ✅ **简化跳转参数**
export type StepJumpParams = {
  from_step_type: StageType;
  to_step_type: StageType;
  jump_type: "navigation" | "next_button";
  // 仅在逐步讲解内部跳转时有效
  from_sub_step_index?: number;
  to_sub_step_index?: number;
};
export type StepExitParams = {
  step_type: StageType;
  step_index: number;
  sub_step_index?: number;
};
export type EventParamsMap = {
  [INTERACTIVE_EXPLANATION_EVENTS.ENTER]: EnterExitParams;
  [INTERACTIVE_EXPLANATION_EVENTS.EXIT]: EnterExitParams;
  [INTERACTIVE_EXPLANATION_EVENTS.STEP_VIEW]: StepViewParams;
  [INTERACTIVE_EXPLANATION_EVENTS.STEP_JUMP]: StepJumpParams;
  [INTERACTIVE_EXPLANATION_EVENTS.STEP_EXIT]: StepExitParams;
};
interface TrackingState {
  step_type: StageType;
  step_index: number; // 步骤索引
  sub_step_index?: number; // 小步骤索引（仅在逐步讲解阶段有效）
}

export const getInteractiveExplanationSource = (
  pathname: string,
  studyType: StudyType,
  preview?: boolean
): string => {
  if (pathname.includes("/course")) {
    return preview ? "ai_course_review" : "ai_course_active";
  }
  if (
    pathname.includes("/exercise-preview") ||
    pathname.includes("/exercise")
  ) {
    if (studyType === StudyType.AI_COURSE) {
      return "ai_course_report";
    }
    if (studyType === StudyType.EXPAND_EXERCISE) {
      return "expand_exercise_report";
    }
    if (studyType === StudyType.REINFORCEMENT_EXERCISE) {
      return "reinforcement_exercise_report";
    }
  }
  return "other";
};

type EnterTrackingParams = {
  preview?: boolean;
  questionId: string | number;
  studySessionId?: number;
  studyType: StudyType;
  lessonId: string | number;
  pathname: string;
  trackEvent?: (eventID: string, map: Record<string, unknown>) => void;
};
// TODO: 二期题型重构完成后删除此方法
export const enterTracking = ({
  preview,
  questionId,
  studySessionId,
  studyType,
  lessonId,
  pathname,
  trackEvent,
}: EnterTrackingParams) => {
  trackEvent?.(INTERACTIVE_EXPLANATION_EVENTS.ENTER, {
    source: getInteractiveExplanationSource(pathname, studyType, preview),
    lesson_id: lessonId ? String(lessonId) : undefined,
    question_id: String(questionId),
    study_session_id: studySessionId ? String(studySessionId) : undefined,
    study_type: studyType,
  });
};

// ===== 主要 Hook (全新手动实现) =====
export const useInteractiveExplanationTracking = ({
  lessonId,
  questionId,
  studySessionId,
  currentRecord,
  stageMapping,
  preview = false,
  studyType,
}: UseInteractiveExplanationTrackingParams) => {
  const pathname = usePathname();
  const isTrackingActiveRef = useRef(false);
  const viewStartTimeRef = useRef<number | null>(null);
  const lastKnownStateRef = useRef<TrackingState | null>(null);

  const source = getInteractiveExplanationSource(pathname, studyType, preview);
  // 将所有外部依赖项存入 ref，确保 useCallback 依赖项为空数组 []
  const depsRef = useRef({
    lessonId,
    questionId,
    studySessionId,
    preview,
    pathname,
    stageMapping,
  });
  useEffect(() => {
    depsRef.current = {
      lessonId,
      questionId,
      studySessionId,
      preview,
      pathname,
      stageMapping,
    };
  });

  const getTrackingState = useCallback(
    (record: AiExplanationRecord): TrackingState => {
      const { stageMapping } = depsRef.current;
      const stage =
        stageMapping[record.currentProgress] ?? StageType.ProblemAnalysis;
      const isStepGuide = stage === StageType.StepByStepGuide;

      const state: TrackingState = {
        step_type: stage,
        step_index: isStepGuide
          ? record.stepByStepGuideCurrentProgress
          : record.currentProgress,
      };

      // 仅在逐步讲解阶段添加 sub_step_index
      if (isStepGuide) {
        state.sub_step_index = record.stepByStepGuideCurrentProgress;
      }

      return state;
    },
    []
  );

  const trackEventFunc = useCallback(
    <T extends InteractiveExplanationEventName>(
      eventName: T,
      additionalParams?: EventParamsMap[T]
    ) => {
      const { lessonId, questionId, studySessionId, preview } = depsRef.current;
      const baseParams = {
        lesson_id: lessonId ? String(lessonId) : undefined,
        question_id: String(questionId),
        study_session_id: studySessionId ? String(studySessionId) : undefined,
        is_from_report: String(preview),
        source,
        study_type: studyType,
        ...additionalParams,
      };
      trackEvent?.(eventName, baseParams);
      console.log(
        `[InteractiveExplanationTracking] 📊 ${eventName}`,
        baseParams
      );
    },
    []
  );

  // ✅ **Step 2: Create a dedicated, exported function for jump tracking**
  // ✅ **简化的跳转埋点函数 - 直接在用户操作时调用**
  const trackJump = useCallback(
    (
      jumpType: "navigation" | "next_button",
      targetRecord: AiExplanationRecord
    ) => {
      if (!isTrackingActiveRef.current) return;

      if (viewStartTimeRef.current && lastKnownStateRef.current) {
        const now = Date.now();
        const stay_time = now - (viewStartTimeRef.current ?? 0); // 毫秒
        const previousTrackingState = lastKnownStateRef.current;
        const targetTrackingState = getTrackingState(targetRecord);

        console.log("🔥 trackJump triggered", {
          from: previousTrackingState,
          to: targetTrackingState,
          jumpType,
          stay_time,
        });

        // 发送当前步骤的浏览时长
        trackEventFunc(INTERACTIVE_EXPLANATION_EVENTS.STEP_VIEW, {
          ...previousTrackingState,
          step_type: previousTrackingState!.step_type,
          step_index: previousTrackingState!.step_index,
          stay_time,
        });

        // 发送跳转事件
        const jumpParams: StepJumpParams = {
          from_step_type: previousTrackingState!.step_type,
          to_step_type: targetTrackingState.step_type,
          jump_type: jumpType,
        };

        // 如果来源是逐步讲解，记录 from_sub_step_index
        if (previousTrackingState?.step_type === StageType.StepByStepGuide) {
          jumpParams.from_sub_step_index = previousTrackingState.sub_step_index;
        }

        // 如果目标是逐步讲解，记录 to_sub_step_index
        if (targetTrackingState.step_type === StageType.StepByStepGuide) {
          jumpParams.to_sub_step_index = targetTrackingState.sub_step_index;
        }

        trackEventFunc(INTERACTIVE_EXPLANATION_EVENTS.STEP_JUMP, jumpParams);

        // 更新状态为新的位置，重新开始计时
        viewStartTimeRef.current = now;
        lastKnownStateRef.current = targetTrackingState;
      }
    },
    [getTrackingState, trackEventFunc]
  );

  // 手动调用的“进入”函数，只计时，不埋点，入口埋点在互动讲解的按钮那里
  const trackEnter = useCallback(() => {
    if (isTrackingActiveRef.current) return;
    isTrackingActiveRef.current = true;

    const now = Date.now();
    const initialState = getTrackingState(currentRecord);

    // trackEventFunc(INTERACTIVE_EXPLANATION_EVENTS.ENTER);

    viewStartTimeRef.current = now;
    lastKnownStateRef.current = initialState;

    trackEvent?.(INTERACTIVE_EXPLANATION_EVENTS.ENTER, {
      source: getInteractiveExplanationSource(pathname, studyType, preview),
      lesson_id: lessonId ? String(lessonId) : undefined,
      question_id: String(questionId),
      study_session_id: studySessionId ? String(studySessionId) : undefined,
      study_type: studyType,
    });
    console.log("🔥 trackEnter called", initialState);
  }, [
    currentRecord,
    getTrackingState,
    questionId,
    studySessionId,
    lessonId,
    pathname,
    studyType,
    preview,
  ]);

  // 手动调用的“退出”函数
  const trackExit = useCallback(() => {
    if (!isTrackingActiveRef.current) return;

    if (viewStartTimeRef.current && lastKnownStateRef.current) {
      const now = Date.now();
      const stay_time = now - (viewStartTimeRef.current ?? 0); // 毫秒
      const lastState = lastKnownStateRef.current;

      trackEventFunc(INTERACTIVE_EXPLANATION_EVENTS.STEP_VIEW, {
        ...lastState,
        stay_time,
      });
      trackEventFunc(INTERACTIVE_EXPLANATION_EVENTS.STEP_EXIT, {
        ...lastState,
      });
      trackEventFunc(INTERACTIVE_EXPLANATION_EVENTS.EXIT);
    }

    isTrackingActiveRef.current = false;
    viewStartTimeRef.current = null;
    lastKnownStateRef.current = null;
  }, [trackEventFunc]);

  // ✅ **Step 3: Return the new trackJump function**
  return { trackEnter, trackExit, trackJump };
};

export default useInteractiveExplanationTracking;
