"use client";

import { cn } from "@repo/ui/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useInteractiveExplanationContext } from "../context";

export interface SimpleDelayedContentProps {
  items: React.ReactNode[];
  firstItemDelay?: number; // 首个 item 的延迟(ms)
  staggerDelay?: number; // 相邻 item 的间隔(ms)
  className?: string;
  stageKey?: string; // 阶段标识（用于“是否首次访问”）
  onAnimationComplete?: (options: { delay?: number }) => void;
}

export const SimpleDelayedContent: React.FC<SimpleDelayedContentProps> = ({
  items,
  firstItemDelay = 500,
  staggerDelay = 1000,
  className,
  stageKey = "default",
  onAnimationComplete,
}) => {
  const { visitedStages, isStageVisited } = useInteractiveExplanationContext();
  /** 用索引控制可见性（驱动 UI） */
  const [visible, setVisible] = useState<Set<number>>(new Set());

  /** 始终拿到最新 size，但不触发重渲染（避免把 size 放进依赖导致重置计时） */
  const visibleSizeRef = useRef(0);
  useEffect(() => {
    visibleSizeRef.current = visible.size;
  }, [visible.size]);

  /** 是否首次访问 */
  const isFirstVisitRef = useRef(!isStageVisited(stageKey));

  /** 过滤掉 null 等空位，保持渲染稳定 */
  const showItems = useMemo(() => items.filter((it) => it != null), [items]);
  const targetCount = showItems.length;

  /**
   * rAF 调度：按“计划时刻”推进（firstItemDelay -> +staggerDelay * n）。
   * 即便掉帧，一帧内会补齐该出现的数量，但节奏不会“越拖越慢”。
   */
  const handleAnimation = useCallback(() => {
    if (visibleSizeRef.current === targetCount) return () => {};

    let rafId: number | null = null;
    const startedAt = performance.now();
    let shown = 0; // 已点亮数量
    let nextDueAt = startedAt + firstItemDelay; // 下一次应点亮的“计划时刻”

    const tick = () => {
      const now = performance.now();

      // 可能一帧补多个：直到“当前时间 >= 计划时刻”
      let due = shown;
      while (due < targetCount && now >= nextDueAt) {
        due += 1;
        nextDueAt += staggerDelay;
      }

      if (due > shown) {
        shown = due;
        setVisible((prev) => {
          if (prev.size >= due) return prev;
          const next = new Set(prev);
          for (let i = prev.size; i < due; i++) next.add(i);
          return next;
        });
      }

      if (shown >= targetCount) {
        // 等最后一个 item 的过渡结束后再标记与回调（过渡 0.6s）
        if (stageKey) visitedStages.value.add(stageKey);
        onAnimationComplete?.({ delay: 600 });
        return;
      }

      rafId = requestAnimationFrame(tick);
    };

    rafId = requestAnimationFrame(tick);

    return () => {
      if (rafId != null) cancelAnimationFrame(rafId);
    };
  }, [
    firstItemDelay,
    staggerDelay,
    targetCount,
    stageKey,
    onAnimationComplete,
  ]);

  useEffect(() => {
    // 非首次访问：直接展示全部，且立即回调（delay: 0）
    if (!isFirstVisitRef.current) {
      const all = new Set<number>(showItems.map((_, i) => i));
      setVisible(all);
      if (stageKey) visitedStages.value.add(stageKey);
      onAnimationComplete?.({ delay: 0 });
      return;
    }

    // 首次访问：启动动画
    const stop = handleAnimation();
    return () => {
      stop && stop();
    };
  }, [handleAnimation, showItems, stageKey, onAnimationComplete]);

  return (
    <div className={cn("simple-delayed-content space-y-4", className)}>
      <AnimatePresence>
        {showItems.map((item, index) =>
          visible.has(index) ? (
            <motion.div
              key={`${stageKey}-${index}`}
              layout
              initial={{
                opacity: 0,
                boxShadow:
                  "0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -4px rgba(0, 0, 0, 0.05)",
                borderRadius: "0.5rem",
              }}
              animate={{ opacity: 1, boxShadow: "none", borderRadius: "none" }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            >
              {item}
            </motion.div>
          ) : null
        )}
      </AnimatePresence>
    </div>
  );
};

export default SimpleDelayedContent;
