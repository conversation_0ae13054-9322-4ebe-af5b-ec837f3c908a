"use client";

import React, { useRef, useEffect } from "react";
import { cn } from "@repo/ui/lib/utils";

interface StepByStepNavigationProps {
  currentStep: number;
  onStepChange: (step: number) => void;
  className?: string;
  steps?: Array<{
    stepTitle: string;
  }>; // 从外部传入的步骤数据
}

interface StepItem {
  id: number;
  title: string;
}

export const StepByStepNavigation: React.FC<StepByStepNavigationProps> = ({
  currentStep,
  onStepChange,
  className,
  steps: stepsData,
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const activeItemRef = useRef<HTMLDivElement>(null);

  // 将传入的步骤数据转换为组件需要的格式
  const steps: StepItem[] = stepsData
    ? stepsData.map((step, index) => ({
        id: index + 1,
        title: step.stepTitle,
      }))
    : [];

  // 自动滚动到激活项居中
  const scrollToActiveItem = () => {
    if (!scrollContainerRef.current || !activeItemRef.current) return;

    const container = scrollContainerRef.current;
    const activeItem = activeItemRef.current;

    const containerWidth = container.clientWidth;
    const itemOffsetLeft = activeItem.offsetLeft;
    const itemWidth = activeItem.clientWidth;

    // 计算目标滚动位置：让激活项居中
    const targetScrollLeft =
      itemOffsetLeft - containerWidth / 2 + itemWidth / 2;

    // 平滑滚动到目标位置
    container.scrollTo({
      left: targetScrollLeft,
      behavior: "smooth",
    });
  };

  // 当currentStep变化时，自动滚动到激活项
  useEffect(() => {
    scrollToActiveItem();
  }, [currentStep]);

  return (
    <div
      className={cn(
        "step-by-step-navigation mx-auto w-full overflow-hidden",
        className
      )}
    >
      {/* 主导航容器 */}
      <div
        className="relative h-[50px] w-full overflow-x-auto overflow-y-hidden"
        ref={scrollContainerRef}
        style={{
          scrollbarWidth: "none", // Firefox
          msOverflowStyle: "none", // IE/Edge
        }}
      >
        {/* 横向滚动容器 */}
        <div className="step-navigation-scroll-container absolute left-0 top-[14px] h-[36px] w-full">
          {/* 添加内联样式来隐藏webkit滚动条 */}
          <style
            dangerouslySetInnerHTML={{
              __html: `
              .step-navigation-scroll-container::-webkit-scrollbar {
                display: none;
              }
            `,
            }}
          />

          {/* 步骤容器 - 横向布局 */}
          <div
            className="step-navigation-container flex h-full select-none items-center justify-center"
            style={{ minWidth: "max-content" }}
          >
            {steps.map((step) => {
              const isActive = step.id === currentStep;

              return (
                <div
                  key={step.id}
                  ref={isActive ? activeItemRef : null}
                  className={cn(
                    "step-navigation-item relative mx-2 flex h-[36px] flex-shrink-0 cursor-pointer select-none items-center justify-center gap-[6px] px-4 py-2",
                    isActive && [
                      "rounded-[18px] bg-[#FFEDE0]",
                      "[&:last-child_.active-item-line]:w-[130%]",
                      "[&:last-child_.active-item-line-left]:!w-[80%]",
                      "[&:last-child_.active-item-line-left]:!translate-x-[-40%]",
                      "[&:first-child_.active-item-line]:w-[130%]",
                      "[&:first-child_.active-item-line-right]:!w-[80%]",
                      "[&:first-child_.active-item-line-right]:!translate-x-[40%]",
                    ]
                  )}
                  onClick={() => onStepChange(step.id)}
                >
                  {isActive && (
                    <div className="active-item-line absolute top-[-0.5rem] z-50 h-[1px] w-[180%] translate-y-[-100%] select-none">
                      <div
                        className={cn(
                          "active-item-line-left absolute left-0 top-0 h-[1px] w-1/2 rounded-full"
                        )}
                        style={{
                          background:
                            "linear-gradient(90deg, #EAEAEA 0%, #FF964A 100%)",
                          zIndex: 50,
                        }}
                      ></div>
                      <div className="z-51 absolute -top-2 left-[50%] flex h-4 w-4 translate-x-[-50%] select-none items-center justify-center bg-white">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                        >
                          <path
                            d="M6.1582 2.68359C6.89955 1.07101 9.27578 1.12472 9.9082 2.84473L10.7803 5.21875L13.1553 6.0918C14.9307 6.7446 14.9307 9.25539 13.1553 9.9082L10.7803 10.7803L9.9082 13.1553C9.2554 14.9307 6.74461 14.9307 6.0918 13.1553L5.21875 10.7803L2.84473 9.9082C1.06929 9.2554 1.06929 6.7446 2.84473 6.0918L5.21875 5.21875L6.0918 2.84473L6.1582 2.68359Z"
                            fill="#FF964A"
                            stroke="white"
                            strokeWidth="3"
                          />
                        </svg>
                      </div>
                      <div
                        className={cn(
                          "active-item-line-right absolute right-0 top-0 h-[1px] w-1/2 rounded-full"
                        )}
                        style={{
                          background:
                            "linear-gradient(90deg, #FF964A 0%, #EAEAEA 100%)",
                          zIndex: 50,
                        }}
                      ></div>
                    </div>
                  )}

                  {!isActive && (
                    <div
                      className={cn(
                        "absolute left-0 top-[-0.5rem] z-0 h-[1px] w-[130%] translate-x-[-15%] translate-y-[-100%] select-none rounded-full bg-[#EAEAEA]"
                      )}
                    >
                      <div className="absolute left-0 top-0 flex h-full w-full items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 14 14"
                          fill="none"
                        >
                          <path
                            d="M7.87214 2.88246C9.87928 2.1393 11.8608 4.12082 11.1177 6.12797L11.0341 6.32892L10.724 6.99943L11.0341 7.67131C12.0239 9.81093 9.81084 12.024 7.6712 11.0342L7 10.7235L6.3288 11.0342C4.18918 12.024 1.97611 9.81095 2.9659 7.67131L3.27595 6.99943L2.9659 6.32892C1.97612 4.1893 4.18916 1.97622 6.3288 2.96602L7 3.27538L7.6712 2.96602L7.87214 2.88246Z"
                            fill="#EAEAEA"
                            stroke="white"
                            strokeWidth="4"
                          />
                        </svg>
                      </div>
                    </div>
                  )}

                  {/* 步骤编号 */}
                  <span
                    className={cn(
                      "step-number select-none text-[15px] font-bold leading-[1.25]",
                      isActive ? "text-[#E6710A]" : "text-[rgba(51,48,45,0.7)]"
                    )}
                    style={{
                      fontFamily: "DIN Alternate, sans-serif",
                    }}
                  >
                    {step.id}
                  </span>

                  {/* 步骤标题 */}
                  <span
                    className={cn(
                      "step-title relative select-none whitespace-nowrap text-[15px] leading-[1.25]",
                      isActive
                        ? "font-medium text-[#E6710A]"
                        : "font-normal text-[rgba(51,48,45,0.7)]"
                    )}
                    style={{
                      fontFamily: "Resource Han Rounded SC, sans-serif",
                    }}
                  >
                    {step.title}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StepByStepNavigation;
