import ArrowRightIcon from "@repo/core/public/assets/interactive-explanation/arrow-right.svg";
import StarIcon from "@repo/core/public/assets/interactive-explanation/star-icon.svg";

interface EntryButtonProps {
  onEnter: () => void;
}
export const EntryButton = ({ onEnter }: EntryButtonProps) => {
  return (
    <div className="interactive-explanation-entry mb-4 flex h-12 w-full items-center gap-1 rounded-[2.25rem] bg-white py-2 pl-4 pr-2 shadow-[0px_12px_40px_0px_rgba(64,43,26,0.05)]">
      {/* 左侧内容区域 */}
      <div className="flex min-w-0 flex-1 items-center gap-1">
        {/* 星形图标 */}
        <StarIcon className="mr-1 h-4 w-4 flex-shrink-0" />

        {/* 文本内容 */}
        <div className="flex min-w-0 items-center gap-[0.0625rem]">
          <span className="text-text-2 whitespace-nowrap text-[1.0625rem] font-normal leading-[1.25em]">
            解析没看懂？一步一步学明白
          </span>
        </div>
      </div>

      {/* 右侧按钮 */}
      <button
        onClick={() => {
          onEnter();
        }}
        className="interactive-explanation-button flex h-8 flex-shrink-0 items-center justify-center gap-1 rounded-[1.75rem] border border-[#FFA666] bg-[rgba(255,166,102,0.15)] px-3 py-[0.625rem] pl-3 pr-2 transition-colors duration-200 hover:bg-[rgba(255,166,102,0.25)]"
      >
        <span className="whitespace-nowrap text-xs font-medium leading-[1.25em] text-[#CC6204]">
          逐步讲解
        </span>
        <div className="flex h-[0.875rem] w-[0.875rem] items-center justify-center">
          <ArrowRightIcon className="h-full w-full" />
        </div>
      </button>
    </div>
  );
};
