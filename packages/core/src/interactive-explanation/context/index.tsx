"use client";
import { Signal, useSignal } from "@preact-signals/safe-react";
import { StudyType } from "@repo/core/enums";
import { AiExplanation, AiExplanationRecord } from "@repo/core/types";
import { createContext, useCallback, useContext, useEffect } from "react";
import useInteractiveExplanationVM from "../viewmodels";

type InteractiveExplanationContextProviderProps = {
  children: React.ReactNode;
  questionId: string;
  studySessionId: number;
  preview: boolean;
  studyType: StudyType;
  initialData: AiExplanation;
  initialRecord?: AiExplanationRecord;
  onProgressChange?: (record: AiExplanationRecord) => void;
  onAnswer?: (stepIndex: number, isCorrect: boolean) => void;
  onClose?: () => void;
};
type InteractiveExplanationContextType = ReturnType<
  typeof useInteractiveExplanationVM
> &
  InteractiveExplanationContextProviderProps & {
    visitedStages: Signal<Set<string>>;
    clearVisitedStages: () => void;
    isStageVisited: (stageKey: string) => boolean;
  };
export const interactiveExplanationContext =
  createContext<InteractiveExplanationContextType>(
    {} as InteractiveExplanationContextType
  );

export const useInteractiveExplanationContext = () => {
  return useContext(interactiveExplanationContext);
};

export const InteractiveExplanationProvider = ({
  children,
  questionId,
  studySessionId,
  preview,
  studyType,
  initialData,
  initialRecord,
  onProgressChange,
  onAnswer,
}: InteractiveExplanationContextProviderProps) => {
  const vm = useInteractiveExplanationVM({
    questionId,
    studySessionId,
    preview,
    studyType,
    initialData,
    initialRecord,
    onProgressChange,
    onAnswer,
  });

  const { showNextBtn } = vm;

  /** 记录已访问的阶段（跨实例、全局可用） */
  const visitedStages = useSignal<Set<string>>(new Set<string>());

  const clearVisitedStages = useCallback(() => {
    visitedStages.value = new Set();
  }, [visitedStages]);

  const isStageVisited = (stageKey: string) =>
    visitedStages.value.has(stageKey);

  useEffect(() => {
    // 如果questionId变化，则清空已访问的阶段
    visitedStages.value = new Set<string>();
    showNextBtn.value = false;
    return () => {
      clearVisitedStages();
    };
  }, [questionId, visitedStages, clearVisitedStages, showNextBtn]);

  return (
    <interactiveExplanationContext.Provider
      value={{
        ...vm,

        children,
        questionId,
        studySessionId,
        preview,
        studyType,
        initialData,
        initialRecord,
        onProgressChange,
        onAnswer,
        visitedStages,
        clearVisitedStages,
        isStageVisited,
      }}
    >
      {children}
    </interactiveExplanationContext.Provider>
  );
};
