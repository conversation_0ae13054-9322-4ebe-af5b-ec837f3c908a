import { signal, useSignal } from "@preact-signals/safe-react";
import { StudyType } from "@repo/core/enums";
import { AiExplanation, AiExplanationRecord } from "@repo/core/types";
import { useCallback, useEffect, useMemo, useState } from "react";
import useInteractiveExplanationTracking from "../hooks/use-interactive-explanation-tracking";
import { useReport } from "../hooks/use-report";
import { StageType } from "../types";

const createDefaultRecord = (
  initialData: AiExplanation
): AiExplanationRecord => {
  const stepCount = initialData?.stepByStepGuide?.steps.length ?? 0;
  return {
    currentProgress: 0,
    examPointAnalysisGuideText: undefined,
    problemAnalysisGuideText: undefined,
    solutionSummaryGuideText: undefined,
    stepByStepGuide: Array.from({ length: stepCount }, () => ({
      guideText: undefined,
      isAnswer: false,
    })),
    stepByStepGuideCurrentProgress: 0,
    version: 1,
  };
};

interface UseInteractiveExplanationVMProps {
  questionId: string;
  studySessionId: number;
  preview: boolean;
  studyType: StudyType;
  initialData: AiExplanation;
  initialRecord?: AiExplanationRecord;
  onProgressChange?: (record: AiExplanationRecord) => void;
  onAnswer?: (stepIndex: number, isCorrect: boolean) => void;
}
// 基于 signal 的记录管理, 切换题目不会清理记录数据，实现本地缓存互动解题记录
const recordsSignal = signal<Record<string, AiExplanationRecord>>({});
export const useInteractiveExplanationVM = ({
  questionId,
  studySessionId,
  preview,
  studyType,
  initialData,
  initialRecord,
  onProgressChange,
  onAnswer,
}: UseInteractiveExplanationVMProps) => {
  const [isVisible, setIsVisible] = useState(false);
  // 创建默认记录结构（不包含guideText）

  // 当前记录状态
  const currentRecord =
    recordsSignal.value[questionId] ??
    initialRecord ??
    createDefaultRecord(initialData); // 内容区域的key，用于强制重新渲染到顶部

  const contentKey = `${questionId}-${currentRecord.currentProgress}-${currentRecord.stepByStepGuideCurrentProgress}`; // 进度上报hook

  const showNextBtn = useSignal(false);
  const { reportProgress } = useReport();

  const stageMapping = useMemo(
    () => [
      StageType.ProblemAnalysis,
      StageType.ExamPointAnalysis,
      StageType.StepByStepGuide,
      StageType.SolutionSummary,
    ],
    []
  );

  // 自动化埋点Hook
  const { trackEnter, trackExit, trackJump } =
    useInteractiveExplanationTracking({
      questionId,
      studySessionId,
      currentRecord,
      stageMapping,
      preview,
      studyType,
    });

  const currentStage =
    stageMapping[currentRecord.currentProgress ?? 0] ??
    StageType.ProblemAnalysis; // 构造符合组件接口的数据结构

  const handleStageChange = useCallback(
    (stage: StageType) => {
      showNextBtn.value = false;
      const stageIndex = stageMapping.indexOf(stage);
      if (stageIndex !== -1 && stageIndex !== currentRecord.currentProgress) {
        const newRecord = {
          ...currentRecord,
          currentProgress: stageIndex,
        };
        recordsSignal.value = {
          ...recordsSignal.value,
          [questionId]: newRecord,
        };
        trackJump("navigation", newRecord);
        onProgressChange?.(newRecord); // 上报进度
        reportProgress({
          studySessionId,
          questionId,
          updatedRecord: newRecord,
        });
      }
    },
    [
      showNextBtn,
      currentRecord,
      stageMapping,
      onProgressChange,
      reportProgress,
      questionId,
      trackJump,
      studySessionId,
    ]
  ); // 步骤进度处理（逐步讲解阶段）

  const handleStepProgress = useCallback(
    (stepIndex: number) => {
      if (currentRecord.currentProgress === 2) {
        // 逐步讲解阶段
        const newRecord = {
          ...currentRecord,
          stepByStepGuideCurrentProgress: stepIndex,
        };
        recordsSignal.value = {
          ...recordsSignal.value,
          [questionId]: newRecord,
        };
        trackJump("navigation", newRecord);
        onProgressChange?.(newRecord); // 上报进度
        reportProgress({
          studySessionId,
          questionId,
          updatedRecord: newRecord,
        });
      }
    },
    [currentRecord, onProgressChange, reportProgress, questionId, trackJump]
  ); // 答题处理

  const handleAnswer = useCallback(
    (stepIndex: number, isCorrect: boolean) => {
      if (currentRecord.currentProgress === 2) {
        // 逐步讲解阶段
        const newStepByStepGuide = [...currentRecord.stepByStepGuide];
        if (newStepByStepGuide[stepIndex]) {
          newStepByStepGuide[stepIndex] = {
            ...newStepByStepGuide[stepIndex],
            isAnswer: true,
          };
        }

        const newRecord = {
          ...currentRecord,
          stepByStepGuide: newStepByStepGuide,
        };
        recordsSignal.value = {
          ...recordsSignal.value,
          [questionId]: newRecord,
        };
        onProgressChange?.(newRecord);
        onAnswer?.(stepIndex, isCorrect);

        // 答题正确后重置解析动画状态，等待解析内容动画完成
        if (isCorrect) {
          reportProgress({
            studySessionId,
            questionId,
            updatedRecord: newRecord,
          });
        }
      }
    },
    [currentRecord, onProgressChange, onAnswer, reportProgress, questionId]
  ); // 下一步处理

  const handleNext = useCallback(() => {
    showNextBtn.value = false;
    // 如果当前在逐步讲解阶段，优先控制二级导航
    if (currentRecord.currentProgress === 2) {
      // 逐步讲解阶段
      const totalSteps = initialData?.stepByStepGuide?.steps.length ?? 0;
      const currentStepIndex = currentRecord.stepByStepGuideCurrentProgress; // 如果还有下一步，则进入下一步

      if (currentStepIndex < totalSteps - 1) {
        const newRecord = {
          ...currentRecord,
          stepByStepGuideCurrentProgress: currentStepIndex + 1,
        };

        recordsSignal.value = {
          ...recordsSignal.value,
          [questionId]: newRecord,
        };
        trackJump("next_button", newRecord);
        onProgressChange?.(newRecord); // 上报进度
        reportProgress({
          studySessionId,
          questionId,
          updatedRecord: newRecord,
        });
        return;
      } // 如果已经是最后一步，则跳转到下一个一级阶段
    }

    if (currentRecord.currentProgress < stageMapping.length - 1) {
      const newRecord = {
        ...currentRecord,
        currentProgress: currentRecord.currentProgress + 1,
      };

      recordsSignal.value = {
        ...recordsSignal.value,
        [questionId]: newRecord,
      };
      trackJump("next_button", newRecord);
      onProgressChange?.(newRecord); // 上报进度
      reportProgress({
        studySessionId,
        questionId,
        updatedRecord: newRecord,
      });
    }
  }, [
    currentRecord,
    stageMapping.length,
    onProgressChange,
    initialData?.stepByStepGuide?.steps.length,
    reportProgress,
    questionId,
    trackJump,
    showNextBtn,
    studySessionId,
  ]); // 监听外部数据变化

  useEffect(() => {
    if (!recordsSignal.value[questionId]) {
      recordsSignal.value = {
        ...recordsSignal.value,
        [questionId]: initialRecord ?? createDefaultRecord(initialData),
      };
    }
  }, [initialRecord, initialData, questionId]); // 动画加载状态管理

  const handleAnimationComplete = (options: { delay?: number }) => {
    setTimeout(() => {
      showNextBtn.value = true;
    }, options.delay || 0);
  };

  return {
    currentStage,
    isVisible,
    currentRecord,
    contentKey,
    showNextBtn,
    handleStageChange,
    handleStepProgress,
    handleAnswer,
    handleNext,
    handleAnimationComplete,
    trackEnter,
    trackExit,
    setIsVisible,
  };
};

export default useInteractiveExplanationVM;
