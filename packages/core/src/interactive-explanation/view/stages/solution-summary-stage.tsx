"use client";

import FinalAnswerIcon from "@repo/core/public/assets/interactive-explanation/final-answer.svg";
import { AiExplanation } from "@repo/core/types";
import { cn } from "@repo/ui/lib/utils";
import React from "react";
import ContentCard from "../../components/content-card";
import SimpleDelayedContent, {
  SimpleDelayedContentProps,
} from "../../components/delayed-render-container";
import GuideText from "../../components/guide-text";

interface SolutionSummaryStageProps {
  guideText: string;
  data: AiExplanation["solutionSummary"];
  className?: string;
  onAnimationComplete?: SimpleDelayedContentProps["onAnimationComplete"];
}

export const SolutionSummaryStage: React.FC<SolutionSummaryStageProps> = ({
  guideText,
  data,
  className,
  onAnimationComplete,
}) => {
  // ✨ 1. Define the UI elements as an array of items for animation.
  const items = [
    guideText ? <GuideText text={guideText} /> : null,
    <ContentCard
      content={data.solutionIdea}
      listType="ordered"
      className="mb-8"
      key="solution-idea"
    />,
    <ContentCard
      title="最终答案"
      content={data.finalAnswer}
      icon={<FinalAnswerIcon />}
      key="final-answer"
    />,
  ];

  // ✨ 2. Render the SimpleDelayedContent component with the prepared data.
  return (
    <SimpleDelayedContent
      items={items}
      stageKey="solution-summary"
      className={cn("solution-summary-stage mt-6", className)}
      onAnimationComplete={onAnimationComplete}
    />
  );
};

export default SolutionSummaryStage;
