"use client";

import KeyInfoIcon from "@repo/core/public/assets/interactive-explanation/key-info.svg";
import { AiExplanation } from "@repo/core/types";
import { cn } from "@repo/ui/lib/utils";
import React from "react";
import ContentCard from "../../components/content-card";
import SimpleDelayedContent, {
  SimpleDelayedContentProps,
} from "../../components/delayed-render-container";
import GuideText from "../../components/guide-text";

interface ProblemAnalysisStageProps {
  guideText: string;
  data: AiExplanation["problemAnalysis"];
  className?: string;
  onAnimationComplete?: SimpleDelayedContentProps["onAnimationComplete"];
}

export const ProblemAnalysisStage: React.FC<ProblemAnalysisStageProps> = ({
  guideText,
  data,
  className,
  onAnimationComplete,
}) => {
  // ✨ 1. Define the UI elements as an array of items for animation.
  const items = [
    guideText ? <GuideText text={guideText} /> : null,
    <ContentCard
      content={data.questionRequirement}
      className="mb-6"
      key="question-requirement"
    />,
    <ContentCard
      title="关键信息"
      content={data.essentialInfo}
      icon={<KeyInfoIcon />}
      key="key-info"
    />,
  ];

  console.log("ProblemAnalysisStage", items);

  // ✨ 2. Render the SimpleDelayedContent component with the prepared data.
  return (
    <SimpleDelayedContent
      items={items}
      stageKey="problem-analysis"
      className={cn("problem-analysis-stage mt-6 gap-y-6", className)}
      onAnimationComplete={onAnimationComplete}
    />
  );
};

export default ProblemAnalysisStage;
