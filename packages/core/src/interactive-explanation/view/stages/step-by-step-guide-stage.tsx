"use client";

import ExplanationIcon from "@repo/core/public/assets/interactive-explanation/explanation.svg";
import { AiExplanation, AiExplanationRecord } from "@repo/core/types";
import { cn } from "@repo/ui/lib/utils";
import React, { useCallback, useMemo } from "react";
import ContentCard from "../../components/content-card";
import SimpleDelayedContent, {
  SimpleDelayedContentProps,
} from "../../components/delayed-render-container";
import GuideText from "../../components/guide-text";
import PText from "../../components/p-text";
import StepByStepNavigation from "../../components/step-by-step-navigation";

interface StepByStepGuideStageProps {
  data?: AiExplanation["stepByStepGuide"];
  className?: string;
  record?: AiExplanationRecord;
  onStepProgress?: (stepIndex: number) => void;
  onAnswer?: (stepIndex: number, isCorrect: boolean) => void;
  // 保持向后兼容
  onAnimationComplete?: SimpleDelayedContentProps["onAnimationComplete"];
}

export const StepByStepGuideStage: React.FC<StepByStepGuideStageProps> = ({
  data,
  className,
  record,
  onStepProgress,
  onAnswer,
  onAnimationComplete, // 保持向后兼容
}) => {
  const currentStep = useMemo(
    () =>
      record?.stepByStepGuideCurrentProgress
        ? record.stepByStepGuideCurrentProgress + 1
        : 1,
    [record]
  );

  const stepData = useMemo(
    () => data?.steps[currentStep - 1],
    [data, currentStep]
  );

  // 从record中获取已完成的步骤
  const completedSteps = useMemo(
    () =>
      record?.stepByStepGuide.reduce(
        (acc, step, index) => {
          if (step.isAnswer) {
            acc[index + 1] = true;
          }
          return acc;
        },
        {} as Record<number, boolean>
      ) || {},
    [record]
  );

  const handleStepChange = (step: number) => {
    // 通知父组件步骤变更
    onStepProgress?.(step - 1); // 转换为0基索引
  };

  const handleCorrectAnswer = useCallback(
    (stepIndex: number) => {
      onAnswer?.(stepIndex - 1, true);
    },
    [onAnswer]
  ); // Dependency on onAnswer

  const stepRecord = useMemo(
    () => record?.stepByStepGuide[currentStep - 1],
    [record, currentStep]
  );
  const isAnswered = useMemo(() => stepRecord?.isAnswer || false, [stepRecord]);
  const guideText = useMemo(() => stepRecord?.guideText, [stepRecord]);
  // ✨ Dynamically build the list of items to be animated.
  const animatedItems = useMemo(() => {
    if (!data?.steps || data.steps.length === 0 || !record) {
      return [];
    }
    if (!stepData) {
      return [];
    }

    const stepId = `step-${currentStep}`;

    // Define the list of UI nodes to be rendered sequentially.
    const items = [
      guideText ? <GuideText text={guideText} /> : null,
      <>
        <PText text="请你想一想" />
        <ContentCard
          mode="question"
          answerConfig={{
            questionData: {
              questionStem: `${stepData.thinkAboutIt.questionStem}`,
              questionOptionList: stepData.thinkAboutIt.questionOptionList,
              correctAnswers:
                stepData.thinkAboutIt.questionAnswer.answerOptionList?.map?.(
                  (a) => a.optionKey || ""
                ),
            },
            onCorrectAnswer: () => handleCorrectAnswer(currentStep),
            initialIsCorrect: isAnswered ? true : null,
            initialSelectedOption: isAnswered
              ? stepData.thinkAboutIt.questionAnswer.answerOptionList[0]
                  ?.optionKey || ""
              : "",
            stepId: stepId,
          }}
        />
      </>,
    ];

    return items;
  }, [
    data,
    record,
    currentStep,
    handleCorrectAnswer,
    stepData,
    isAnswered,
    guideText,
  ]);

  const animatedAnalysisItems = useMemo(() => {
    console.log("stepData", stepData);

    return [
      <ContentCard
        icon={<ExplanationIcon />}
        title="解析"
        content={stepData?.thinkAboutIt.questionExplanation}
        key="analysis"
      />,
      <>
        <PText text="一起算一算" />
        <ContentCard content={stepData?.calculateTogether} />
      </>,
    ];
  }, [stepData]);

  return (
    <div className={cn("step-by-step-guide-stage", className)}>
      {/* The top navigation remains static and is not part of the animation. */}
      <div className="mb-12 px-6 pt-0 step-navigation-wrapper">
        <StepByStepNavigation
          currentStep={currentStep}
          onStepChange={handleStepChange}
          steps={data?.steps}
        />
      </div>

      <div className="px-6 step-content-wrapper">
        <SimpleDelayedContent
          items={animatedItems}
          stageKey={`step-guide-${currentStep}`}
        />
        {completedSteps[currentStep] && (
          <SimpleDelayedContent
            className="mt-6"
            items={animatedAnalysisItems}
            stageKey={`step-guide-analysis-${currentStep}`}
            onAnimationComplete={onAnimationComplete}
          />
        )}
      </div>
    </div>
  );
};

export default StepByStepGuideStage;
