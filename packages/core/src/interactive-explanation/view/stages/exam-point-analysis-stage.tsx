"use client";

import SolutionBreakthroughIcon from "@repo/core/public/assets/interactive-explanation/breakthrough.svg";
import ExamPointIcon from "@repo/core/public/assets/interactive-explanation/exam-point.svg";
import TargetIcon from "@repo/core/public/assets/interactive-explanation/target.svg";
import { cn } from "@repo/ui/lib/utils";
import React from "react";
import ContentCard from "../../components/content-card";
import SimpleDelayedContent, {
  SimpleDelayedContentProps,
} from "../../components/delayed-render-container";
import GuideText from "../../components/guide-text";
import PText from "../../components/p-text";

interface ExamPointAnalysisData {
  guideText?: string;
  target: string;
  examPoint: string;
  solutionBreakthrough: string;
}

interface ExamPointAnalysisStageProps {
  guideText: string;
  data: ExamPointAnalysisData;
  className?: string;
  onAnimationComplete?: SimpleDelayedContentProps["onAnimationComplete"];
}

export const ExamPointAnalysisStage: React.FC<ExamPointAnalysisStageProps> = ({
  guideText,
  data,
  className,
  onAnimationComplete,
}) => {
  // 🔥 修复：将引导语从 items 中分离，确保与题目分析保持一致的加载时序
  const items = [
    guideText ? <GuideText text={guideText} /> : null,

    <>
      <PText text="首先，我们需要明确目标：" className="mb-4 mt-8" />
      <ContentCard
        listType="ordered"
        title="目标"
        content={data.target}
        icon={<TargetIcon />}
        key="target"
      />
    </>,

    <>
      <PText text="其次，我们需得知核心考点：" className="mb-4 mt-8" />
      <ContentCard
        listType="ordered"
        title="考点"
        content={data.examPoint}
        icon={<ExamPointIcon />}
        key="exam-point"
      />
    </>,

    <>
      <PText
        text="目标和考点都清楚了，我们来找找这道题的解题突破口："
        className="mb-4 mt-8"
      />
      <ContentCard
        title="解题突破口"
        content={data.solutionBreakthrough}
        icon={<SolutionBreakthroughIcon />}
        key="solution-breakthrough"
      />
    </>,
  ];

  return (
    <SimpleDelayedContent
      items={items}
      stageKey="exam-point-analysis"
      className={cn("exam-point-analysis-stage mt-6 gap-y-6", className)}
      onAnimationComplete={onAnimationComplete}
    />
  );
};

export default ExamPointAnalysisStage;
