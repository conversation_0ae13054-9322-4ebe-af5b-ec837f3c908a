"use client";
import {
  createContext,
  FC,
  ReactNode,
  RefObject,
  useCallback,
  useContext,
  useMemo,
  useRef,
} from "react";

import { Signal, useComputed, useSignal } from "@preact-signals/safe-react";
import { MergedReference, Reference } from "@repo/core/types/data/comment";
import {
  GuideMode,
  GuideTheme,
  GuideWidgetData,
} from "@repo/core/types/data/widget-guide";
import { useGuideThemeViewmodel } from "../viewmodels/guide-theme-viewmodel";

type SelectedLine = {
  id: string;
  frame: number;
  left: number;
  top: number;
};

type GuideContextType = {
  client?: "stu" | "aipt" | "tch" | "";
  guideMode: GuideMode;
  title?: string;
  index: number;
  totalGuideCount: number;
  data: GuideWidgetData;
  theme: GuideTheme;
  showSubtitle: boolean;
  selectedLineId: string;
  selectedLine: Signal<SelectedLine | null>;
  onLineSelected: (frame: number) => void;
  handleLineClick: (
    left: number,
    top: number,
    lineId: string,
    frame: number
  ) => void;
  refContainer: RefObject<HTMLDivElement | null>;
  selectable: boolean;

  commentRef?: Signal<HTMLDivElement | null>;
  lineIdInRange?: string;
  referenceList?: Reference[];
  onClickReference?: (
    reference: MergedReference[string][string][number]
  ) => void;

  needScrollFlip: boolean;
  onScrollFlip?: (index: number) => void;
};

const GuideContext = createContext<GuideContextType>({} as GuideContextType);

const useGuideContext = () => useContext(GuideContext);

interface GuideProviderProps {
  client?: "stu" | "aipt" | "tch" | "";
  guideMode?: GuideMode;
  title?: string;
  index?: number;
  totalGuideCount?: number;
  data: GuideWidgetData;
  theme?: GuideTheme;
  showSubtitle?: boolean;
  refContainer?: RefObject<HTMLDivElement | null>;
  onLineClick?: (frame: number) => void;
  children: ReactNode;
  selectable?: boolean;
  onScrollFlip?: (index: number) => void;
  commentRef?: Signal<HTMLDivElement | null>;
  lineIdInRange?: string;
  referenceList?: Reference[];
  onClickReference?: (
    reference: MergedReference[string][string][number]
  ) => void;
}

const GuideProvider: FC<GuideProviderProps> = ({ children, ...props }) => {
  const {
    client = "",
    guideMode = GuideMode.follow,
    title,
    index = 0,
    totalGuideCount = 0,
    data,
    theme: themeConfig,
    showSubtitle,
    onLineClick,
    selectable = true,
    commentRef,
    lineIdInRange,
    referenceList,
    onClickReference,
    refContainer,
    onScrollFlip,
  } = props;

  const theme = useGuideThemeViewmodel(themeConfig);
  const selectedLine = useSignal<SelectedLine | null>(null);
  const selectedLineId = useComputed(() => selectedLine.value?.id ?? "");
  const defaultRefContainer = useRef<HTMLDivElement | null>(null);

  const needScrollFlip = useMemo(() => {
    return onScrollFlip !== undefined;
  }, [onScrollFlip]);

  const handleLineClick = useCallback(
    (left: number, top: number, id: string, frame: number) => {
      if (!selectable) return;
      selectedLine.value = {
        id,
        frame,
        left,
        top,
      };
    },
    [selectable, selectedLine]
  );

  const onLineSelected = useCallback(
    (frame: number) => {
      onLineClick?.(frame);
    },
    [onLineClick]
  );

  const value = {
    client,
    guideMode,
    title,
    index,
    totalGuideCount,
    data,
    theme,
    showSubtitle: showSubtitle ?? true,
    selectedLine,
    selectedLineId: selectedLineId.value,
    onLineSelected,
    handleLineClick,
    refContainer: refContainer ?? defaultRefContainer,
    selectable,
    needScrollFlip,
    onScrollFlip,
    commentRef,
    lineIdInRange,
    referenceList,
    onClickReference,
  };
  return <GuideContext value={value}>{children}</GuideContext>;
};

export {
  GuideContext,
  GuideProvider,
  useGuideContext,
  type GuideContextType,
  type GuideProviderProps,
};
