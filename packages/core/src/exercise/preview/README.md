# Exercise Preview 组件

练习题目预览组件，支持学生端、教师端、产课工具使用，提供完整的题目预览和答题状态展示功能。

## ✨ 核心特性

- 🎯 **双端兼容** - 学生端和教师端通用
- 📝 **多种模式** - 单题预览 / 题目列表预览
- 🔍 **智能定位** - 根据题目ID自动跳转
- 📊 **状态展示** - 已答题/未答题状态显示
- 🎨 **样式隔离** - Teacher端可选样式隔离

## 🚀 快速开始

### 学生端使用

```tsx
import { ExerciseView } from '@repo/core/exercise/preview/view/exercise-view';

<ExerciseView
  questionList={questionList}
  studyType={StudyType.REINFORCEMENT_EXERCISE}
  onBack={() => window.history.back()}
/>
```

### Teacher端使用（样式隔离）

```tsx
import { COMPLETE_EXERCISE_VARIABLES } from '@repo/core/exercise/theme';

<ExerciseView
  questionList={questionList}
  customVariables={COMPLETE_EXERCISE_VARIABLES}  // 🔥 样式隔离
  onBack={() => window.history.back()}
/>
```

## 📋 主要属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `questionList` | `PreviewQuestionData[]` | - | 题目列表（列表模式） |
| `questionData` | `Question` | - | 单个题目（单题模式） |
| `questionId` | `string` | - | 根据ID定位到指定题目 |
| `customVariables` | `Record<string, string>` | - | CSS变量对象（Teacher端样式隔离） |
| `progressDisplayMode` | `'bar' \| 'number'` | `'number'` | 进度显示模式 |
| `showExplanations` | `boolean` | `true` | 是否显示解析 |
| `onBack` | `() => void` | - | 返回按钮回调 |

## 📖 使用示例

### 题目ID定位

```tsx
<ExerciseView
  questionList={questionList}
  questionId="q3_fill_blank"  // 自动定位到指定题目
/>
```

### 单题预览

```tsx
<ExerciseView
  questionData={singleQuestion}
  showExplanations={true}
/>
```

### 进度显示模式

```tsx
// 数字模式（默认）- 显示 "1 / 5"
<ExerciseView progressDisplayMode="number" />

// 进度条模式 - 显示动态进度条
<ExerciseView progressDisplayMode="bar" />
```

### 错题本功能

```tsx
function ExerciseWithWrongBook() {
  const [wrongQuestionBook, setWrongQuestionBook] = useState<Set<string>>(new Set());

  const handleToggleWrongQuestionBook = async (questionId: string, isCurrentlyInBook: boolean) => {
    if (isCurrentlyInBook) {
      setWrongQuestionBook(prev => {
        const newSet = new Set(prev);
        newSet.delete(questionId);
        return newSet;
      });
    } else {
      setWrongQuestionBook(prev => new Set([...prev, questionId]));
    }
  };

  return (
    <ExerciseView
      questionList={questionList}
      onToggleWrongQuestionBook={handleToggleWrongQuestionBook}
      isInWrongQuestionBook={(questionId) => wrongQuestionBook.has(questionId)}
    />
  );
}
```



## ⚙️ 配置要求

### SVG 图标支持

需要安装 `@svgr/webpack` 并在 `next.config.ts` 中配置 SVG 处理：

```bash
npm install -D @svgr/webpack
```

```typescript
// next.config.ts
const nextConfig = {
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });
    return config;
  },
};
```

### 样式隔离机制

**学生端**：无需配置，使用全局样式
**Teacher端**：传入 `customVariables` 实现样式隔离

```tsx
import { COMPLETE_EXERCISE_VARIABLES } from '@repo/core/exercise/theme';

// Teacher端使用 - 样式隔离
<ExerciseView
  questionList={questionList}
  customVariables={COMPLETE_EXERCISE_VARIABLES}  // 🔥 样式隔离
/>
```

**机制说明：**
- 默认：CSS变量挂载到 `document.body`（学生端）
- 传入变量：CSS变量挂载到局部容器（Teacher端）

### Tailwind 配置

确保 Tailwind 扫描组件文件：

```typescript
// tailwind.config.ts
export default {
  content: [
    "../../packages/core/src/**/*.{js,ts,jsx,tsx,mdx}",
  ],
};
```

## ❓ 常见问题

**Q: SVG 图标不显示？**
A: 安装 `@svgr/webpack` 并配置 `next.config.ts`

**Q: 如何隐藏解析？**
A: 设置 `showExplanations={false}`

**Q: 如何定位到指定题目？**
A: 使用 `questionId` 属性

**Q: Teacher端样式污染？**
A: 传入 `customVariables={COMPLETE_EXERCISE_VARIABLES}`