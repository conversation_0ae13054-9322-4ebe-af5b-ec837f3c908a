'use client';

import { Suspense } from 'react';
import { ExerciseView } from './view/exercise-view';
import { StudyType } from '@repo/core/enums';
import { PreviewQuestionData } from '@repo/core/exercise/model/types';

// 模拟题目列表数据 - 5种题型，每道题都有 studentAnswer 数据
const mockPreviewQuestionList: PreviewQuestionData[] = [
  {
    // 第1题：单选题
    questionInfo: {
      questionId: "q1_single_choice",
      questionIndex: 1,
      questionType: 1, // 单选题
      questionContent: {
        questionOrder: 1,
        questionScore: 1,
        questionOriginName: "2022秋•房山区期末",
        questionStem: "把集合 $\\{x | x^2 - 4x + 3 = 0\\}$ 用列举法表示，正确的是（ ）．",
        questionOptionList: [],
      },
      questionDifficulty: 2,  
      questionAnswer: { answerOptionList: [{ optionKey: "A", optionVal: "$\\{1, 3\\}$" }] },
      questionExplanation: "解方程 $x^2 - 4x + 3 = 0$ 得 $x=1$ 或 $x=3$，用列举法表示方程的解集为 $\\{1,3\\}$．",
      hasNextQuestion: true,
      progressInfo: {
        completedQuestions: 0,
        currentProgress: 20,
        totalQuestions: 5,
      },
    },
    studentAnswer: {
      questionId: "q1_single_choice",
      answerContents: [{
        index: 1,
        type: 1, // 文本
        content: ["A"], // 选择了 A 选项
        questionId: "q1_single_choice",
      }],
      answerType: 1, // 文本
      answerDuration: 45, // 用时 45 秒
      answerResult: 1, // 正确
      evaluationType: 1, // 系统判题
      answerStats: [{
        questionId: "q1_single_choice",
        answerStat: [
          { optionKey: "A", rate: "0.68" }, // 68% 选择 A
          { optionKey: "B", rate: "0.18" }, // 18% 选择 B
          { optionKey: "C", rate: "0.08" }, // 8% 选择 C
          { optionKey: "D", rate: "0.06" }, // 6% 选择 D
        ]
      }],
    },
  },
  {
    // 第2题：多选题
    questionInfo: {
      questionId: "q2_multiple_choice",
      questionIndex: 2,
      questionType: 2, // 多选题
      questionContent: {
        questionOrder: 2,
        questionScore: 1,
        questionOriginName: "2022秋•房山区期末",
        questionStem: "下列哪些是常见的编程语言？",
        questionOptionList: [],
      },
      questionDifficulty: 1,
      questionAnswer: {
        answerOptionList: [
          { optionKey: "A", optionVal: "Python" },
          { optionKey: "B", optionVal: "Java" },
          { optionKey: "C", optionVal: "C++" },
          { optionKey: "E", optionVal: "JavaScript" },
        ]
      },
      questionExplanation: "Python、Java、C++ 和 JavaScript 都是常见的编程语言，而 HTML 是标记语言。",
      hasNextQuestion: true,
      progressInfo: {
        completedQuestions: 1,
        currentProgress: 40,
        totalQuestions: 5,
      },
    },
    studentAnswer: {
      questionId: "q2_multiple_choice",
      answerContents: [{
        index: 1,
        type: 1, // 文本
        content: ["A", "B", "C"], // 选择了 A、B、C 选项（漏选了E）
        questionId: "q2_multiple_choice",
      }],
      answerType: 1, // 文本
      answerDuration: 62, // 用时 62 秒
      answerResult: 3, // 部分正确
      evaluationType: 1, // 系统判题
      answerStats: [{
        questionId: "q2_multiple_choice",
        answerStat: [
          { optionKey: "A", rate: "0.85" }, // 85% 选择 Python
          { optionKey: "B", rate: "0.78" }, // 78% 选择 Java
          { optionKey: "C", rate: "0.72" }, // 72% 选择 C++
          { optionKey: "D", rate: "0.25" }, // 25% 错选 HTML
          { optionKey: "E", rate: "0.65" }, // 65% 选择 JavaScript
        ]
      }],
    },
  },
  {
    // 第3题：填空题
    questionInfo: {
      questionId: "q3_fill_blank",
      questionIndex: 3,
      questionType: 3, // 填空题
      questionContent: {
        questionOrder: 3,
        questionScore: 1,
        questionOriginName: "2022秋•房山区期末",
        questionStem: '<p>计算下列各题：</p><p>（1）$3x + 5 = 14$，则 $x = $ <span data-tiptype="question-blank_filling" data-index="1"></span></p><p>（2）若 $2^a = 8$，则 $a = $ <span data-tiptype="question-blank_filling" data-index="2"></span></p>',
        questionOptionList: [],
      },
      questionDifficulty: 2,
      questionAnswer: {
        answerOptionList: [
          { optionKey: "blank_1", optionVal: "3" },
          { optionKey: "blank_2", optionVal: "3" },
        ]
      },
      questionExplanation: "（1）$3x + 5 = 14$，移项得 $3x = 9$，所以 $x = 3$<br/>（2）$2^a = 8 = 2^3$，所以 $a = 3$",
      hasNextQuestion: true,
      progressInfo: {
        completedQuestions: 2,
        currentProgress: 60,
        totalQuestions: 5,
      },
    },
    studentAnswer: {
      questionId: "q3_fill_blank",
      answerContents: [{
        index: 1,
        type: 1, // 文本
        content: ["3", "3"], // 填空答案
        questionId: "q3_fill_blank",
      }],
      answerType: 1, // 文本
      answerDuration: 89, // 用时 89 秒
      answerResult: 1, // 正确
      evaluationType: 1, // 系统判题
      answerStats: [], // 填空题没有选择分布统计
    },
  },
  {
    // 第4题：英语填空题
    questionInfo: {
      questionId: "q4_english_fill",
      questionIndex: 4,
      questionType: 3, // 英语填空题
      questionContent: {
        questionOrder: 4,
        questionScore: 1,
        questionOriginName: "2022秋•房山区期末",
        questionStem: '<p>Complete the sentences with the correct form of the words in brackets.</p><p>1. She <span data-tiptype="question-blank_filling" data-index="1"></span> (study) English for three years.</p><p>2. The book <span data-tiptype="question-blank_filling" data-index="2"></span> (write) by a famous author.</p><p>3. If it <span data-tiptype="question-blank_filling" data-index="3"></span> (rain) tomorrow, we will stay at home.</p>',
        questionOptionList: [],
      },
      questionDifficulty: 2,
      questionAnswer: {
        answerOptionList: [
          { optionKey: "blank_1", optionVal: "has studied" },
          { optionKey: "blank_2", optionVal: "was written" },
          { optionKey: "blank_3", optionVal: "rains" },
        ]
      },
      questionExplanation: "1. 现在完成时：has studied（她学英语三年了）<br/>2. 被动语态：was written（这本书是由一位著名作者写的）<br/>3. 条件状语从句：rains（如果明天下雨，我们就待在家里）",
      hasNextQuestion: true,
      progressInfo: {
        completedQuestions: 3,
        currentProgress: 80,
        totalQuestions: 5,
      },
    },
    studentAnswer: {
      questionId: "q4_english_fill",
      answerContents: [{
        index: 1,
        type: 1, // 文本
        content: ["has studied", "was written", "rains"], // 英语填空答案
        questionId: "q4_english_fill",
      }],
      answerType: 1, // 文本
      answerDuration: 156, // 用时 156 秒
      answerResult: 1, // 正确
      evaluationType: 1, // 系统判题
      answerStats: [], // 英语填空题没有选择分布统计
    },
  },
  {
    // 第5题：解答题
    questionInfo: {
      questionId: "q5_essay",
      questionIndex: 5,
      questionType: 5, // 解答题
        questionContent: {
        questionOrder: 5,
        questionScore: 1,
        questionOriginName: "2022秋•房山区期末",
        questionStem: "请解释牛顿第二定律，并举例说明其在日常生活中的应用。",
        questionOptionList: [],
      },
      questionDifficulty: 3,
      questionAnswer: {
        answerOptionList: [
          {
            optionKey: "essay_answer",
            optionVal: "牛顿第二定律表述为：物体加速度的大小与作用力成正比，与物体质量成反比，加速度的方向与作用力的方向相同。日常生活中的应用例如：推购物车时，同样的力作用下，购物车里物品越多（质量越大），购物车加速度越小。",
          },
        ]
      },
      questionExplanation: "牛顿第二定律是经典力学中的基本定律之一，描述了力、质量和加速度之间的关系。",
      hasNextQuestion: false,
      progressInfo: {
        completedQuestions: 4,
        currentProgress: 100,
        totalQuestions: 5,
      },
    },
    studentAnswer: {
      questionId: "q5_essay",
      answerContents: [{
        index: 1,
        type: 1, // 文本
        content: ["牛顿第二定律说明了力、质量和加速度的关系，即F=ma。在生活中，比如踢足球时，用力越大球的加速度越大；推重物时需要更大的力才能产生相同的加速度。"], // 学生的解答
        questionId: "q5_essay",
      }],
      answerType: 1, // 文本
      answerDuration: 245, // 用时 245 秒
      answerResult: 2, // 错误（答案不够完整）
      evaluationType: 1, // 系统判题
      answerStats: [], // 解答题没有选择分布统计
    },
  },
];

function ExercisePageContent() {
  // TODO: 从不同平台获取题目数据并转换格式
  const questionList = mockPreviewQuestionList;

  const handleBack = () => {
    console.log('返回按钮被点击');
    // TODO: 实际的返回逻辑
  };

  // const handleComplete = () => {
  //   console.log('练习完成');
  //   // TODO: 实际的完成逻辑
  // };

  return (
    <ExerciseView
      questionList={questionList}
      initialIndex={0}
      studyType={StudyType.REINFORCEMENT_EXERCISE}
      onBack={handleBack}
    />
  );
}

export default function ExercisePage() {
  return (
    <Suspense fallback={
      <div className='exercise-page-loading flex h-full w-full items-center justify-center'>
        加载中...
      </div>
    }>
      <ExercisePageContent />
    </Suspense>
  );
}
