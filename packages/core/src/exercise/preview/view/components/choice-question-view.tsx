"use client";

import { QUESTION_TYPE } from "@repo/core/enums";
import { ChoiceOptionsList } from "@repo/core/exercise/components";
import { QuestionState } from "@repo/core/exercise/model/types";
import { useMemo } from "react";
import { NextQuestionInfo } from "../../../model";
import { useQuestionPreviewContext } from "../../contexts/question-preview-context";
import { useChoiceQuestionViewModel } from "../../viewmodel/choice-question-viewmodel";

interface Props {
  questionState?: QuestionState;
  question?: NextQuestionInfo; // 可选，优先使用 Context 中的数据
}

export function ChoiceQuestionView({ questionState = "answering" }: Props) {
  // 🎯 优先从统一Context获取题目数据和正确答案，fallback到props
  const { currentQuestion, answerStats, currentStudentAnswer } =
    useQuestionPreviewContext();
  const question = currentQuestion;

  // 🔥 从 answerStats 中提取当前题目的答案统计数据
  const choiceAnswerStat = useMemo(() => {
    // 判断题目类型，如果是单选、多选题，则从 answerStats 数组中查找当前题目的统计
    if (
      (question?.questionType === QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE ||
        question?.questionType ===
          QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE) &&
      answerStats
    ) {
      const currentStat = answerStats.find(
        (stat) => stat.questionId === question.questionId
      );
      return currentStat?.answerStat || [];
    }
    return [];
  }, [answerStats, question?.questionType, question?.questionId]);

  // 使用选择题专用ViewModel管理选择逻辑
  const choiceViewModel = useChoiceQuestionViewModel(question);

  // 支持选择题和判断题类型
  if (
    question?.questionType !== QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE &&
    question?.questionType !== QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE
  ) {
    console.error(
      "ChoiceQuestionView only supports single or multiple choice questions. Received type:",
      question?.questionType
    );
    return (
      <div className="choice-question-error p-4 text-center text-red-500">
        不支持的题目类型
      </div>
    );
  }

  // 构建选项数据 - 使用 useMemo 缓存，避免无限重新渲染
  const options = useMemo(() => {
    if (!question) return [];
    return (
      question.questionContent.questionOptionList?.map((option) => ({
        id: option.optionKey || "",
        key: option.optionKey || "",
        content: option.optionVal || "",
        isCorrect: false, // 前端不需要知道正确答案
      })) || []
    );
  }, [question]);

  // 判断是否显示结果（提交后、放弃作答后、或等待自评状态）
  const showResult = useMemo(() => {
    const result =
      questionState === "submitted" || questionState === "giving_up";
    return result;
  }, [questionState]);

  // 创建稳定的空数组引用，避免每次渲染都创建新的数组
  const emptySelectedOptions = useMemo(() => [], []);

  // 计算实际传递给组件的选项状态
  // 当用户点击"不确定"或"放弃作答"时，传递空数组，这样判题时只显示正确答案的漏选状态
  const displaySelectedOptions = useMemo(() => {
    if (questionState === "uncertain" || questionState === "giving_up") {
      return emptySelectedOptions; // 不确定或放弃作答时不显示任何用户选择
    }
    return currentStudentAnswer?.content || [];
  }, [questionState, currentStudentAnswer, emptySelectedOptions]);

  // 转换题目类型为组件需要的格式 - 使用 useMemo 缓存
  const questionTypeForComponent = useMemo(():
    | "single_choice"
    | "multiple_choice"
    | "true_false" => {
    if (!question) return "single_choice";
    if (question.questionType === QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE)
      return "multiple_choice";
    return "single_choice";
  }, [question]);

  return (
    <div className="choice-question-view">
      <ChoiceOptionsList
        isPreview={true}
        questionId={question?.questionId}
        options={options}
        selectedOptions={displaySelectedOptions}
        onSelectOption={choiceViewModel.handleOptionClick}
        correctAnswer={
          question?.questionAnswer
            ? {
                answerOptionList: question.questionAnswer.answerOptionList.map(
                  (opt) => ({
                    optionKey: opt.optionKey,
                    optionVal: opt.optionVal,
                  })
                ),
              }
            : undefined
        }
        showResult={showResult}
        questionType={questionTypeForComponent}
        answerStat={choiceAnswerStat}
      />
    </div>
  );
}
