import { cn } from "@repo/ui/lib/utils";
import React, { useRef, useEffect, useState } from "react";
import { FillBlankViewModel } from "../../viewmodel/fill-blank-question-viewmodel";

export type BlankAnswer = {
  id: number;
  value: string;
};

export type InputMode = "keyboard" | "camera";

const KeyboardInput: React.FC<{
  viewModel: FillBlankViewModel;
  type: "fill-blank" | "solution" | "english-fill-blank";
}> = ({ viewModel, type }) => {
  const {
    answers,
    activeBlankIndex,
    handleTextChange,
    setActiveBlankIndex,
    isReview,
    correctAnswers,
    openInputDialog,
    closeInputDialog,
    confirmInputDialog,
    showInputDialog,
    inputDialogValue,
    setInputDialogValue,
    selfEvaluation,
  } = viewModel;
  const tabRefs = useRef<(HTMLLIElement | null)[]>([]);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const maxRows = 3;
  const lineHeight = 24;
  const maxHeight = maxRows * lineHeight;
  const [showToast, setShowToast] = useState(false);
  const lastTimesRef = useRef<number[]>([]); // 记录最近3次自评时间戳

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputDialogValue(e.target.value);
    viewModel.textareaAutoResize(textareaRef.current);
  };

  useEffect(() => {
    if (showInputDialog) {
      viewModel.textareaAutoResize(textareaRef.current);
    }
  }, [inputDialogValue, showInputDialog]);

  return (
    <>
      <div className="flex h-full flex-col">
        {/* Tab 切换 */}
        {(type === "fill-blank" || type === "english-fill-blank") &&
          answers.length > 1 && (
            <div className="pb-13 mt-5 h-8 overflow-hidden">
              <ul className="flex overflow-x-auto pb-10">
                {answers.map((answer, index) => {
                  let tabColor = "";
                  if (isReview && selfEvaluation[index]) {
                    const evalResult = selfEvaluation[index];
                    if (evalResult === "right")
                      tabColor =
                        "bg-[rgba(132,214,75,0.15)] text-[#449908] border-[#84D64B]";
                    else if (evalResult === "partial")
                      tabColor =
                        "bg-[rgba(255,212,102,0.15)] text-[#CC6204] border-[#FFD466]";
                    else if (evalResult === "wrong")
                      tabColor =
                        "bg-[rgba(255,123,89,0.15)] text-[#D1320A] border-[#FF6139]";

                    if (activeBlankIndex === index) {
                      if (evalResult === "right")
                        tabColor = "bg-[#84D64B] text-white border-[#84D64B]";
                      else if (evalResult === "partial")
                        tabColor = "bg-[#FFD466] text-white border-[#FFD466]";
                      else if (evalResult === "wrong")
                        tabColor = "bg-[#FF6139] text-white border-[#FF6139]";
                    }
                  }
                  return (
                    <li
                      key={answer.id}
                      ref={(el) => {
                        tabRefs.current[index] = el;
                      }}
                      className={cn(
                        "border-1 mr-6 flex h-8 w-8 shrink-0 cursor-pointer items-center justify-center rounded-full border-solid border-[rgba(88,196,250,0.5)] bg-white text-center text-sm text-[#0A8AC2]",
                        activeBlankIndex === index
                          ? "bg-[#58C4FA] text-white"
                          : answer.value
                            ? "bg-white text-[#0A8AC2]"
                            : "bg-[rgba(88,196,250,0.15)] text-[#0A8AC2]",
                        isReview ? tabColor : ""
                      )}
                      onClick={() => {
                        setActiveBlankIndex(index);
                        // handleOptionClick(); // 已移除，使用新的状态管理
                      }}
                    >
                      {index + 1}
                    </li>
                  );
                })}
              </ul>
            </div>
          )}
        {/* 输入区域 */}
        {/* 解析状态 */}
        {/* {!isReview && (
          <div className="mt-3 h-full flex-1">
            <textarea
              disabled
              className="box-border h-full w-full resize-none rounded-xl border border-[rgba(31,35,43,0.12)] bg-[#FFFFFF] p-6 text-base"
              placeholder={`点击输入答案`}
              value={answers[activeBlankIndex]?.value || ""}
              onChange={handleTextChange}
              onFocus={() =>
                openInputDialog(
                  answers[activeBlankIndex]?.value || "",
                  activeBlankIndex
                )
              }
            />
          </div>
        )} */}
      </div>
      {/* 底部弹窗和蒙层 */}
      {showInputDialog && (
        <>
          <div
            className="fixed inset-0 z-40 bg-black/40"
            onClick={closeInputDialog}
          ></div>
          <div className="fixed bottom-0 left-0 right-0 z-50 flex h-[13.75rem] w-full items-end">
            <div className="flex w-full items-end rounded-t-2xl bg-white p-4 shadow-lg">
              <textarea
                ref={textareaRef}
                className="flex-1 resize-none rounded-lg border-none bg-[#F7F6F5] p-2 outline-0"
                value={inputDialogValue}
                onChange={handleInputChange}
                autoFocus
                rows={1}
                style={{
                  maxHeight,
                  minHeight: `${lineHeight}px`,
                  resize: "none",
                }}
              />
              <button
                className="ml-4 h-10 w-[6.125rem] rounded bg-[#FEA100] text-white"
                onClick={confirmInputDialog}
              >
                确定
              </button>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default KeyboardInput;
