'use client';

import { useState, useCallback } from 'react';
import { StudyType } from '@repo/core/enums';

export interface ExitViewModelState {
  showExitConfirm: boolean;
  isExiting: boolean;
  exitError: Error | null;
}

export interface ExitViewModelActions {
  handleExitRequest: (studyType: StudyType, onExitCallback?: () => void) => void;
  handleConfirmExit: (studySessionId: number, onExitCallback?: () => void) => Promise<void>;
  handleCancelExit: () => void;
}

export type ExitViewModel = ExitViewModelState & ExitViewModelActions;

/**
 * 退出功能的 ViewModel
 * 
 * 职责：
 * - 管理退出确认弹窗状态
 * - 处理不同学习类型的退出逻辑
 * - 调用退出会话API保存进度
 * - 提供统一的退出接口给View层
 * 
 * 架构优势：
 * - View层无需直接调用Model层的hooks
 * - 退出逻辑集中管理，便于维护
 * - 支持不同学习类型的差异化处理
 */
export function useExitViewModel(): ExitViewModel {
  const [showExitConfirm, setShowExitConfirm] = useState(false);
  const [exitError, setExitError] = useState<Error | null>(null);

  /**
   * 处理退出请求
   * 
   * @param studyType 学习类型
   * @param onExitCallback 退出回调函数
   */
  const handleExitRequest = useCallback((studyType: StudyType, onExitCallback?: () => void) => {
    // 清除之前的错误状态
    setExitError(null);

    // 如果是AI课程，直接触发退出状态，不显示弹窗
    if (studyType === StudyType.AI_COURSE) {
      // AI课程直接返回，由外层处理退出逻辑
      return;
    }

    // 其他类型显示确认弹窗
    setShowExitConfirm(true);
  }, []);

  /**
   * 确认退出处理
   * 
   * @param studySessionId 学习会话ID
   * @param onExitCallback 退出回调函数
   */
  const handleConfirmExit = useCallback(async (studySessionId: number, onExitCallback?: () => void) => {
    try {
      setExitError(null);

      // 如果有学习会话ID，先调用退出会话接口
      if (studySessionId > 0) {
        // 调用退出会话接口
      }

      // 关闭确认弹窗
      setShowExitConfirm(false);

      // 触发退出回调
      if (onExitCallback) {
        onExitCallback();
      }
    } catch (error) {
      const exitErr = error instanceof Error ? error : new Error('退出会话失败');
      console.error('退出会话失败:', exitErr);

      // 记录错误但仍然执行退出
      setExitError(exitErr);
      setShowExitConfirm(false);

      // 即使退出会话失败，也要触发退出回调
      if (onExitCallback) {
        onExitCallback();
      }
    }
  }, []);

  /**
   * 取消退出
   */
  const handleCancelExit = useCallback(() => {
    setShowExitConfirm(false);
    setExitError(null);
  }, []);

  return {
    // 状态
    showExitConfirm,
    isExiting: false,
    exitError: exitError,

    // 操作
    handleExitRequest,
    handleConfirmExit,
    handleCancelExit,
  };
} 