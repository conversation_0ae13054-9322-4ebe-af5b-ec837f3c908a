import { QUESTION_TYPE } from '@repo/core/enums';
import { useCallback, useMemo } from 'react';
import { NextQuestionInfo } from '../../model';
import { useQuestionPreviewContext } from '../contexts/question-preview-context';

/**
 * 选择题 ViewModel
 * 
 * ✅ 已适配新的统一架构：
 * - 移除对已删除的 useExerciseSessionContext 的依赖
 * - 使用新的 useQuestionPreviewContext 获取题目数据
 * - 简化答案管理，只管理本地状态
 * 
 * 职责：
 * - 管理选择题的选项选择状态
 * - 处理单选/多选逻辑
 * - 提供选项点击交互
 */
export function useChoiceQuestionViewModel(question?: NextQuestionInfo) {
  // 🎯 从统一Context获取题目数据和答案管理方法
  const { currentQuestion, userAnswerData, updateUserAnswer } = useQuestionPreviewContext();

  // 使用传入的题目或Context中的当前题目
  const displayQuestion = question || currentQuestion;

  // 🔧 修复：使用Context中的选择状态，而不是本地状态
  const choiceAnswers = useMemo(() => {
    return userAnswerData.choiceAnswers || [];
  }, [userAnswerData.choiceAnswers]);

  // 判断是否为多选题
  const isMultipleChoice = useMemo(() => {
    return displayQuestion?.questionType === QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE;
  }, [displayQuestion?.questionType]);

  // 处理选项点击
  const handleOptionClick = useCallback((optionKey: string) => {
    if (!displayQuestion) return;

    let newSelectedOptions: string[];

    if (isMultipleChoice) {
      // 多选题逻辑
      if (choiceAnswers.includes(optionKey)) {
        newSelectedOptions = choiceAnswers.filter(key => key !== optionKey);
      } else {
        newSelectedOptions = [...choiceAnswers, optionKey];
      }
    } else {
      // 单选题逻辑
      newSelectedOptions = [optionKey];
    }

    // 🔧 修复：更新Context中的答案状态
    updateUserAnswer({ choiceAnswers: newSelectedOptions });
  }, [choiceAnswers, isMultipleChoice, displayQuestion, updateUserAnswer]);

  // 重置选择
  const resetSelection = useCallback(() => {
    updateUserAnswer({ choiceAnswers: [] });
  }, [updateUserAnswer]);

  // 检查是否有选择
  const hasSelection = useMemo(() => {
    return choiceAnswers.length > 0;
  }, [choiceAnswers]);

  return {
    choiceAnswers,
    isMultipleChoice,
    hasSelection,
    handleOptionClick,
    resetSelection,
  };
}