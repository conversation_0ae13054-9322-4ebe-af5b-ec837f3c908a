'use client';

import { useState, useCallback, useMemo, useEffect } from 'react';
import { PreviewQuestionInfo } from '../../model/types';

export interface PreviewViewModelState {
  // 题目列表数据
  questionList: PreviewQuestionInfo[];
  currentIndex: number;
  isLoading: boolean;
  error: Error | null;
  
  // 导航状态
  canGoPrevious: boolean;
  canGoNext: boolean;
  
  // 预览模式配置
  showAnswers: boolean; // 是否显示答案
  showExplanations: boolean; // 是否显示解析
}

export interface PreviewViewModelActions {
  // 导航操作
  goToPrevious: () => void;
  goToNext: () => void;
  goToQuestion: (index: number) => void;
  
  // 配置操作
  toggleShowAnswers: () => void;
  toggleShowExplanations: () => void;
  
  // 数据操作
  setQuestionList: (questions: PreviewQuestionInfo[]) => void;
  refreshCurrentQuestion: () => void;
}

export type PreviewViewModel = PreviewViewModelState & PreviewViewModelActions;

/**
 * 预览模式 ViewModel
 * 
 * 职责：
 * - 管理题目列表数据和当前题目索引
 * - 处理上一题/下一题导航逻辑
 * - 控制答案和解析的显示状态
 * - 提供题目预览的完整功能
 * 
 * 架构优势：
 * - 独立的预览逻辑，不与练习模式耦合
 * - 支持灵活的题目列表浏览
 * - 可配置的显示选项
 */
export function usePreviewViewModel(
  initialQuestions: PreviewQuestionInfo[] = [],
  initialIndex: number = 0
): PreviewViewModel {
  // 基础状态
  const [questionList, setQuestionList] = useState<PreviewQuestionInfo[]>(initialQuestions);
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  // 预览配置状态
  const [showAnswers, setShowAnswers] = useState(false);
  const [showExplanations, setShowExplanations] = useState(false);

  // 计算导航状态
  const canGoPrevious = useMemo(() => currentIndex > 0, [currentIndex]);
  const canGoNext = useMemo(() => currentIndex < questionList.length - 1, [currentIndex, questionList.length]);

  // 确保索引在有效范围内
  useEffect(() => {
    if (questionList.length > 0 && currentIndex >= questionList.length) {
      setCurrentIndex(questionList.length - 1);
    } else if (currentIndex < 0) {
      setCurrentIndex(0);
    }
  }, [questionList.length, currentIndex]);

  // 导航操作
  const goToPrevious = useCallback(() => {
    if (canGoPrevious) {
      setCurrentIndex(prev => prev - 1);
      setError(null);
    }
  }, [canGoPrevious]);

  const goToNext = useCallback(() => {
    if (canGoNext) {
      setCurrentIndex(prev => prev + 1);
      setError(null);
    }
  }, [canGoNext]);

  const goToQuestion = useCallback((index: number) => {
    if (index >= 0 && index < questionList.length) {
      setCurrentIndex(index);
      setError(null);
    } else {
      setError(new Error(`题目索引 ${index} 超出范围 [0, ${questionList.length - 1}]`));
    }
  }, [questionList.length]);

  // 配置操作
  const toggleShowAnswers = useCallback(() => {
    setShowAnswers(prev => !prev);
  }, []);

  const toggleShowExplanations = useCallback(() => {
    setShowExplanations(prev => !prev);
  }, []);

  // 数据操作
  const handleSetQuestionList = useCallback((questions: PreviewQuestionInfo[]) => {
    setQuestionList(questions);
    setCurrentIndex(0); // 重置到第一题
    setError(null);
  }, []);

  const refreshCurrentQuestion = useCallback(() => {
    // 这里可以添加刷新当前题目的逻辑
    // 比如重新获取题目数据等
    setError(null);
  }, []);

  return {
    // 状态
    questionList,
    currentIndex,
    isLoading,
    error,
    canGoPrevious,
    canGoNext,
    showAnswers,
    showExplanations,
    
    // 操作
    goToPrevious,
    goToNext,
    goToQuestion,
    toggleShowAnswers,
    toggleShowExplanations,
    setQuestionList: handleSetQuestionList,
    refreshCurrentQuestion,
  };
}

/**
 * 获取当前题目的辅助 Hook
 */
export function useCurrentQuestion(viewModel: PreviewViewModel): PreviewQuestionInfo | null {
  return useMemo(() => {
    const { questionList, currentIndex } = viewModel;
    return questionList[currentIndex] || null;
  }, [viewModel.questionList, viewModel.currentIndex]);
}

/**
 * 获取题目导航信息的辅助 Hook
 */
export function useQuestionNavigation(viewModel: PreviewViewModel) {
  return useMemo(() => {
    const { currentIndex, questionList, canGoPrevious, canGoNext } = viewModel;
    
    return {
      currentNumber: currentIndex + 1, // 1-based 显示
      totalCount: questionList.length,
      progress: questionList.length > 0 ? ((currentIndex + 1) / questionList.length) * 100 : 0,
      canGoPrevious,
      canGoNext,
    };
  }, [viewModel]);
}
