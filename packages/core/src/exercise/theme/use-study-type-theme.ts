import { useMemo } from "react";
import { StudyType } from "../../enums/question";
import {
  createCSSVariablesFromTheme,
  getStudyTypeCSSVariables,
  getStudyTypeTailwindClasses,
  getStudyTypeTheme,
} from "./study-type-theme";

/**
 * 练习类型主题色 Hook
 * @param studyType 练习类型
 * @returns 主题色相关的工具函数和配置
 */
export function useStudyTypeTheme(studyType: StudyType) {
  const theme = useMemo(() => getStudyTypeTheme(studyType), [studyType]);
  const cssVariables = useMemo(
    () => getStudyTypeCSSVariables(studyType),
    [studyType]
  );
  const tailwindClasses = useMemo(
    () => getStudyTypeTailwindClasses(studyType),
    [studyType]
  );

  /**
   * 获取主题色的CSS样式对象
   * @returns CSS样式对象
   */
  const getThemeStyles = useMemo(() => {
    return createCSSVariablesFromTheme(theme) as React.CSSProperties;
  }, [theme]);

  /**
   * 获取渐变背景样式
   * @returns 渐变背景样式对象
   */
  const getGradientStyles = useMemo(() => {
    if (!theme.gradient) {
      return { backgroundColor: theme.primary };
    }

    return {
      background: `linear-gradient(${
        theme.gradient.direction === "to-r"
          ? "90deg"
          : theme.gradient.direction === "to-l"
            ? "270deg"
            : theme.gradient.direction === "to-t"
              ? "0deg"
              : theme.gradient.direction === "to-b"
                ? "180deg"
                : theme.gradient.direction === "to-tr"
                  ? "45deg"
                  : theme.gradient.direction === "to-tl"
                    ? "315deg"
                    : theme.gradient.direction === "to-br"
                      ? "135deg"
                      : "225deg"
      }, 
                                   ${theme.gradient.from} 0%, ${theme.gradient.to} 100%)`,
    } as React.CSSProperties;
  }, [theme]);

  /**
   * 获取按钮样式
   * @param variant 按钮变体
   * @returns 按钮样式对象
   */
  const getButtonStyles = useMemo(() => {
    return {
      primary: {
        backgroundColor: theme.primary,
        color: "#FFFFFF",
        border: `1px solid ${theme.primary}`,
        boxShadow: `0 4px 12px ${theme.shadowColor}`,
      },
      secondary: {
        backgroundColor: "transparent",
        color: theme.primary,
        border: `1px solid ${theme.primary}`,
      },
      outline: {
        backgroundColor: "transparent",
        color: theme.textColor,
        border: `1px solid ${theme.borderColor}`,
      },
      ghost: {
        backgroundColor: theme.backgroundLight,
        color: theme.textColor,
        border: "none",
      },
    } as const;
  }, [theme]);

  /**
   * 获取卡片样式
   * @returns 卡片样式对象
   */
  const getCardStyles = useMemo(() => {
    return {
      backgroundColor: theme.background,
      border: `1px solid ${theme.borderColor}`,
      boxShadow: `0 2px 8px ${theme.shadowColor}`,
    } as React.CSSProperties;
  }, [theme]);

  return {
    theme,
    cssVariables,
    tailwindClasses,
    getThemeStyles,
    getGradientStyles,
    getButtonStyles,
    getCardStyles,
  };
}
