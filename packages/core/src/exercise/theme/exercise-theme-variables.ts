import { PRESS_BUTTON_THEMES } from "./study-type-theme";

/**
 * 练习组件完整CSS变量集合
 * 包含所有练习组件需要的基础变量，实现真正的样式隔离
 * 基于 packages/core/src/style/stu-globals.css 但独立维护
 */
export const COMPLETE_EXERCISE_VARIABLES = {
  ...PRESS_BUTTON_THEMES,
  // === 基础颜色变量 ===
  "--color-white-bg": "#FFFFFF",
  "--color-white-hover": "#F9FAFB",
  "--color-white-border": "rgba(51, 46, 41, 0.06)",
  "--color-gray-bg": "#F4F3F2",
  "--color-gray-text": "rgba(51, 48, 45, 0.7)",

  // === 主题色系 ===
  "--color-orange-1": "#FF902E",
  "--color-orange-2": "#FFEDDF",
  "--color-orange-text": "#CC6204",
  "--color-red-1": "#F66042",
  "--color-red-2": "#FFE5DF",
  "--color-red-text": "#D1320A",
  "--color-green-1": "#84D64B",
  "--color-green-2": "#DFF2D1",
  "--color-green-text": "#449908",

  // === 文字颜色 ===
  "--color-text-1": "rgba(51, 48, 45, 0.95)",
  "--color-text-2": "rgba(51, 48, 45, 0.85)",
  "--color-text-3": "rgba(51, 48, 45, 0.7)",
  "--color-text-4": "rgba(51, 48, 45, 0.55)",
  "--color-text-5": "rgba(51, 48, 45, 0.4)",

  // === 填充色 ===
  "--color-fill-1": "#1F1D1B",
  "--color-fill-2": "rgba(31, 29, 27, 0.6)",
  "--color-fill-3": "rgba(31, 29, 27, 0.2)",
  "--color-fill-4": "rgba(31, 29, 27, 0.05)",

  // === 背景色 ===
  "--color-fill-beige": "#FAEAD7",
  "--color-fill-gray": "#F7F6F5",
  "--color-fill-white": "#FFFFFF",
  "--color-bg-white": "#FFFFFF",
  "--color-bg-gray": "#F4F3F2",
  "--color-bg-orange": "#FFEDDF",
  "--color-bg-red": "#FFE5DF",
  "--color-bg-green": "#DFF2D1",

  // === 文稿组件颜色 ===
  "--color-stone-600": "#5C5757",
  "--color-stone-700": "#4A292A",
  "--color-stone-900": "#221407",
  "--color-amber-200": "#FFD080",
  "--color-yellow-950": "#4D2F13",
  "--color-dim-orange": "#CC6204",
  "--color-main-orange": "#FFA666",
  "--color-pink-100": "#F7EBF0",

  // === 按钮阴影 ===
  "--btn-orange-shadow": "#F27813",
  "--btn-red-shadow": "#D94123",
  "--btn-green-shadow": "rgba(115, 178, 71, 0.3)",
  "--btn-gray-shadow": "rgba(51, 46, 41, 0.2)",
  "--btn-white-shadow": "rgba(51, 46, 41, 0.2)",
  "--btn-shadow-orange-primary": "#F27813",
  "--btn-shadow-orange-secondary": "#F1CFB5",
  "--btn-shadow-red-primary": "#E36544",
  "--btn-shadow-red-secondary": "#F4D3CC",
  "--btn-shadow-green-primary": "#73B247",
  "--btn-shadow-green-secondary": "#D0E7C0",
  "--btn-shadow-white": "rgba(51, 46, 41, 0.06)",
  "--btn-shadow-gray": "#E0DEDC",

  // === 尺寸变量 ===
  "--text-xxs": "0.625rem",
  "--text-xxs--line-height": "calc(1 / 0.625)",
  "--aspect-stu": "10/6",

  // === 分割线颜色 ===
  "--color-divider-1": "rgba(51, 46, 41, 0.12)",
  "--color-divider-2": "rgba(51, 46, 41, 0.06)",
  "--Divider-Divider1": "rgba(51, 46, 41, 0.12)",
  "--Divider-Divider2": "rgba(51, 46, 41, 0.06)",

  // === shadcn-ui 基础变量 ===
  "--background": "#FFFFFF",
  "--foreground": "rgba(51, 48, 45, 0.7)",
  "--card": "#FFFFFF",
  "--card-foreground": "rgba(51, 48, 45, 0.7)",
  "--popover": "#FFFFFF",
  "--popover-foreground": "rgba(51, 48, 45, 0.7)",
  "--primary": "#FF902E",
  "--primary-foreground": "#FFFFFF",
  "--secondary": "#FFFFFF",
  "--secondary-foreground": "#FF902E",
  "--muted": "#F7F6F5",
  "--muted-foreground": "rgba(51, 48, 45, 0.55)",
  "--accent": "#F7F6F5",
  "--accent-foreground": "rgba(51, 48, 45, 0.95)",
  "--destructive": "#F66042",
  "--destructive-foreground": "#FFFFFF",
  "--border": "rgba(51, 46, 41, 0.12)",
  "--input": "rgba(51, 46, 41, 0.12)",
  "--ring": "rgba(51, 48, 45, 0.7)",
  "--radius": "0.625rem",

  // === shadcn-ui 颜色映射 ===
  "--color-background": "#FFFFFF",
  "--color-foreground": "rgba(51, 48, 45, 0.7)",
  "--color-card": "#FFFFFF",
  "--color-card-foreground": "rgba(51, 48, 45, 0.7)",
  "--color-popover": "#FFFFFF",
  "--color-popover-foreground": "rgba(51, 48, 45, 0.7)",
  "--color-primary": "#FF902E",
  "--color-primary-foreground": "#FFFFFF",
  "--color-secondary": "#FFFFFF",
  "--color-secondary-foreground": "#FF902E",
  "--color-muted": "#F7F6F5",
  "--color-muted-foreground": "rgba(51, 48, 45, 0.55)",
  "--color-accent": "#F7F6F5",
  "--color-accent-foreground": "rgba(51, 48, 45, 0.95)",
  "--color-destructive": "#F66042",
  "--color-destructive-foreground": "#FFFFFF",
  "--color-border": "rgba(51, 46, 41, 0.12)",
  "--color-input": "rgba(51, 46, 41, 0.12)",
  "--color-ring": "rgba(51, 48, 45, 0.7)",

  // === 图表颜色 ===
  "--chart-1": "#FF902E",
  "--chart-2": "#84D64B",
  "--chart-3": "#F66042",
  "--chart-4": "#FFA666",
  "--chart-5": "#CC6204",
  "--color-chart-1": "#FF902E",
  "--color-chart-2": "#84D64B",
  "--color-chart-3": "#F66042",
  "--color-chart-4": "#FFA666",
  "--color-chart-5": "#CC6204",

  // === 侧边栏变量 ===
  "--sidebar": "#FFFFFF",
  "--sidebar-foreground": "rgba(51, 48, 45, 0.95)",
  "--sidebar-primary": "#FF902E",
  "--sidebar-primary-foreground": "#FFFFFF",
  "--sidebar-accent": "#F7F6F5",
  "--sidebar-accent-foreground": "rgba(51, 48, 45, 0.95)",
  "--sidebar-border": "rgba(51, 46, 41, 0.12)",
  "--sidebar-ring": "rgba(51, 48, 45, 0.7)",
  "--color-sidebar": "#FFFFFF",
  "--color-sidebar-foreground": "rgba(51, 48, 45, 0.95)",
  "--color-sidebar-primary": "#FF902E",
  "--color-sidebar-primary-foreground": "#FFFFFF",
  "--color-sidebar-accent": "#F7F6F5",
  "--color-sidebar-accent-foreground": "rgba(51, 48, 45, 0.95)",
  "--color-sidebar-border": "rgba(51, 46, 41, 0.12)",
  "--color-sidebar-ring": "rgba(51, 48, 45, 0.7)",

  // === 字体和布局变量 ===
  "--font-resource-han-rounded": "ResourceHanRounded",
  "--width-guide": "804px",
  "--width-section-h3": "772px",
  "--text-h3": "28px",
  "--text-h4": "22px",
  "--tracking-guide": "0.08em",
  "--leading-guide": "2",
}
