# 练习类型主题色系统

这是一个根据 `studyType` 来区分不同练习主题色的完整解决方案，为不同类型的练习提供独特的视觉体验。

## 功能特性

- 🎨 **多主题支持**：支持 AI课、巩固练习、拓展练习等不同练习类型
- 🎯 **统一设计**：每种练习类型都有完整的主色调、背景色、文字色等配置
- 🔧 **易于使用**：提供 React Hook 和组件，开箱即用
- 🎪 **渐变效果**：支持渐变背景和进度条
- 📱 **响应式**：完全兼容移动端和桌面端

## 练习类型与主题色

| 练习类型 | 主色调 | 背景色 | 说明 |
|---------|--------|--------|------|
| AI课 | `#FEA026` (橙色) | `#FEF8F4` (浅橙色) | 温暖友好的学习体验 |
| 巩固练习 | `#FEA026` (橙色) | `#FEF8F4` (浅橙色) | 稳定可靠的练习环境 |
| 拓展练习 | `#84D64B` (绿色) | `#E6FAF5` (浅青色) | 充满挑战的拓展学习 |

## 快速开始

### 1. 基础使用

```tsx
import { StudyTypeThemeProvider } from '@repo/core/exercise/theme';
import { StudyType } from '@repo/core/enums';

function ExercisePage() {
  return (
    <StudyTypeThemeProvider studyType={StudyType.REINFORCEMENT_EXERCISE}>
      <div className="exercise-content">
        {/* 你的练习内容 */}
      </div>
    </StudyTypeThemeProvider>
  );
}
```

### 2. 使用主题色 Hook

```tsx
import { useStudyTypeThemeContext } from '@repo/core/exercise/theme';

function MyComponent() {
  const { theme, getButtonStyles, getCardStyles } = useStudyTypeThemeContext();
  
  return (
    <div style={{ backgroundColor: theme.background }}>
      <button style={getButtonStyles.primary}>
        主题色按钮
      </button>
      <div style={getCardStyles}>
        主题色卡片
      </div>
    </div>
  );
}
```

### 3. 使用主题色组件

```tsx
import { ThemedButton, ThemedCard, ThemedGradient } from '@repo/core/exercise/theme';

function MyComponent() {
  return (
    <div>
      <ThemedButton variant="primary" onClick={() => alert('点击')}>
        主要按钮
      </ThemedButton>
      
      <ThemedCard>
        <h2>主题色卡片</h2>
        <p>内容...</p>
      </ThemedCard>
      
      <ThemedGradient>
        <h2>渐变背景</h2>
        <p>渐变内容...</p>
      </ThemedGradient>
    </div>
  );
}
```

## API 参考

### StudyTypeThemeProvider

主题色提供者组件，为整个练习页面提供主题色支持。

```tsx
interface StudyTypeThemeProviderProps {
  studyType: StudyType;        // 练习类型
  children: ReactNode;         // 子组件
  className?: string;          // 自定义样式类名
}
```

### useStudyTypeThemeContext

在组件内部使用主题色配置的 Hook。

```tsx
const {
  theme,                    // 主题色配置对象
  getThemeStyles,          // CSS样式对象
  getGradientStyles,       // 渐变背景样式
  getButtonStyles,         // 按钮样式集合
  getCardStyles,           // 卡片样式
} = useStudyTypeThemeContext();
```

### ThemedButton

主题色按钮组件。

```tsx
interface ThemedButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  children: ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
}
```

### ThemedCard

主题色卡片组件。

```tsx
interface ThemedCardProps {
  children: ReactNode;
  className?: string;
}
```

### ThemedGradient

主题色渐变背景组件。

```tsx
interface ThemedGradientProps {
  children: ReactNode;
  className?: string;
}
```

## 主题色配置

### StudyTypeTheme 接口

```tsx
interface StudyTypeTheme {
  primary: string;           // 主色调
  primaryLight: string;      // 主色调浅色版本
  primaryDark: string;       // 主色调深色版本
  background: string;        // 背景色
  backgroundLight: string;   // 背景色浅色版本
  textColor: string;         // 文字颜色
  borderColor: string;       // 边框颜色
  shadowColor: string;       // 阴影颜色
  gradient?: {               // 渐变背景配置
    from: string;
    to: string;
    direction: 'to-r' | 'to-l' | 'to-t' | 'to-b' | 'to-tr' | 'to-tl' | 'to-br' | 'to-bl';
  };
}
```

## CSS 变量

主题色系统会自动设置以下 CSS 变量：

```css
:root {
  --study-primary: #84D64B;
  --study-primary-light: #DFF2D1;
  --study-primary-dark: #449908;
  --study-background: #F8FFF4;
  --study-background-light: #FAFFF6;
  --study-text-color: #449908;
  --study-border-color: rgba(132, 214, 75, 0.2);
  --study-shadow-color: rgba(132, 214, 75, 0.3);
  --study-gradient-from: #84D64B;
  --study-gradient-to: #A8E675;
  --study-gradient-direction: to-r;
}
```

## 工具函数

### getStudyTypeTheme

获取练习类型对应的主题色配置。

```tsx
import { getStudyTypeTheme } from '@repo/core/exercise/theme';

const theme = getStudyTypeTheme(StudyType.REINFORCEMENT_EXERCISE);
```

### getStudyTypeCSSVariables

获取练习类型对应的 CSS 变量。

```tsx
import { getStudyTypeCSSVariables } from '@repo/core/exercise/theme';

const cssVars = getStudyTypeCSSVariables(StudyType.REINFORCEMENT_EXERCISE);
```

### getStudyTypeTailwindClasses

获取练习类型对应的 Tailwind CSS 类名。

```tsx
import { getStudyTypeTailwindClasses } from '@repo/core/exercise/theme';

const classes = getStudyTypeTailwindClasses(StudyType.REINFORCEMENT_EXERCISE);
```

## 集成到现有组件

### 更新练习页面

```tsx
// 在 exercise-view.tsx 中
import { StudyTypeThemeProvider } from "@repo/core/exercise/theme";

export function ExerciseView({ studyType, ...props }) {
  return (
    <StudyTypeThemeProvider studyType={studyType}>
      <QuestionContextProvider {...props}>
        <ExerciseViewContent />
      </QuestionContextProvider>
    </StudyTypeThemeProvider>
  );
}
```

### 更新进度条

```tsx
// 在 ProgressBar.tsx 中
<div
  className="progress-bar-fill"
  style={{
    background: 'var(--study-gradient-from) && var(--study-gradient-to) ? linear-gradient(90deg, var(--study-gradient-from) 0%, var(--study-gradient-to) 100%) : linear-gradient(90deg, #FE512E 0%, #FA8329 100%)',
  }}
/>
```

## 演示页面

访问 `/exercise/theme-demo` 页面可以查看不同练习类型的主题色效果演示。

## 注意事项

1. **必须包装在 Provider 中**：使用主题色 Hook 的组件必须被 `StudyTypeThemeProvider` 包装
2. **CSS 变量优先级**：主题色 CSS 变量会覆盖默认样式
3. **渐变方向**：支持 8 个方向的渐变，默认为 `to-r`（从左到右）
4. **响应式设计**：所有主题色组件都支持响应式设计

## 扩展主题色

如需添加新的练习类型主题色，请在 `study-type-theme.ts` 中的 `STUDY_TYPE_THEMES` 对象中添加配置：

```tsx
export const STUDY_TYPE_THEMES: Record<StudyType, StudyTypeTheme> = {
  // 现有配置...
  [StudyType.NEW_TYPE]: {
    primary: '#NEW_COLOR',
    primaryLight: '#NEW_COLOR_LIGHT',
    primaryDark: '#NEW_COLOR_DARK',
    background: '#NEW_BACKGROUND',
    backgroundLight: '#NEW_BACKGROUND_LIGHT',
    textColor: '#NEW_TEXT_COLOR',
    borderColor: 'rgba(NEW_COLOR_RGB, 0.2)',
    shadowColor: 'rgba(NEW_COLOR_RGB, 0.3)',
    gradient: {
      from: '#NEW_COLOR',
      to: '#NEW_COLOR_LIGHT',
      direction: 'to-r'
    }
  }
};
``` 