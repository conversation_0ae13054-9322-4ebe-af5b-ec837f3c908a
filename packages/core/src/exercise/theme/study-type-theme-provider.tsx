import React, { createContext, ReactNode, useContext } from "react";
import { StudyType } from "../../enums/question";
import {
  getStudyTypeCSSVariables,
  type StudyTypeTheme,
} from "./study-type-theme";
import { useStudyTypeTheme } from "./use-study-type-theme";

/**
 * 主题色上下文类型
 */
interface StudyTypeThemeContextType {
  theme: StudyTypeTheme;
  getThemeStyles: React.CSSProperties;
  getGradientStyles: React.CSSProperties;
  getButtonStyles: {
    primary: React.CSSProperties;
    secondary: React.CSSProperties;
    outline: React.CSSProperties;
    ghost: React.CSSProperties;
  };
  getCardStyles: React.CSSProperties;
}

/**
 * 主题色上下文
 */
const StudyTypeThemeContext = createContext<StudyTypeThemeContextType | null>(
  null
);

/**
 * 主题色提供者组件属性
 */
interface StudyTypeThemeProviderProps {
  studyType: StudyType;
  children: ReactNode;
  className?: string;
  /**
   * 自定义CSS变量对象
   * 用于覆盖或补充默认的CSS变量
   * 适用于需要样式隔离的场景（如teacher端）
   *
   * @example
   * ```tsx
   * import { COMPLETE_EXERCISE_VARIABLES } from '@repo/core/exercise/styles/complete-variables';
   *
   * <StudyTypeThemeProvider
   *   studyType={StudyType.REINFORCEMENT_EXERCISE}
   *   customVariables={COMPLETE_EXERCISE_VARIABLES}
   * >
   *   <ExerciseContent />
   * </StudyTypeThemeProvider>
   * ```
   */
  customVariables?: Record<string, string>;
}

/**
 * 主题色提供者组件
 * 为练习页面提供主题色支持
 *
 * 样式隔离机制：
 * - 无customVariables：变量设置到body上（学生端兼容模式）
 * - 有customVariables：变量设置到局部容器上（Teacher端隔离模式）
 */
export function StudyTypeThemeProvider({
  studyType,
  children,
  className = "",
  customVariables,
}: StudyTypeThemeProviderProps) {
  const {
    theme,
    getThemeStyles,
    getGradientStyles,
    getButtonStyles,
    getCardStyles,
  } = useStudyTypeTheme(studyType);

  // 创建容器引用
  const containerRef = React.useRef<HTMLDivElement>(null);

  // 同步主题变量
  React.useEffect(() => {
    const cssVars = getStudyTypeCSSVariables(studyType);

    if (customVariables) {
      // 如果传入了customVariables，设置到局部容器上，避免样式污染
      const allVars = { ...cssVars, ...customVariables };

      if (containerRef.current) {
        Object.entries(allVars).forEach(([key, value]) => {
          containerRef.current!.style.setProperty(key, value);
        });
      }
    } else {
      // 默认情况下，设置到 body（学生端保持原有行为）
      Object.entries(cssVars).forEach(([key, value]) => {
        document.body.style.setProperty(key, value);
      });

      // 清理时移除这些变量
      return () => {
        // Object.keys(cssVars).forEach((key) => {
        //   document.body.style.removeProperty(key);
        // });
      };
    }
  }, [studyType, customVariables]);

  const contextValue: StudyTypeThemeContextType = {
    theme,
    getThemeStyles,
    getGradientStyles,
    getButtonStyles,
    getCardStyles,
  };

  return (
    <StudyTypeThemeContext.Provider value={contextValue}>
      <div ref={containerRef} className={`study-type-theme ${className}`}>
        {children}
      </div>
    </StudyTypeThemeContext.Provider>
  );
}

/**
 * 使用主题色 Hook
 * 在组件内部使用主题色配置
 */
export function useStudyTypeThemeContext() {
  const context = useContext(StudyTypeThemeContext);
  if (!context) {
    throw new Error(
      "useStudyTypeThemeContext must be used within a StudyTypeThemeProvider"
    );
  }
  return context;
}

/**
 * 主题色按钮组件
 */
interface ThemedButtonProps {
  variant?: "primary" | "secondary" | "outline" | "ghost";
  children: ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
}

export function ThemedButton({
  variant = "primary",
  children,
  onClick,
  disabled = false,
  className = "",
}: ThemedButtonProps) {
  const { getButtonStyles } = useStudyTypeThemeContext();

  return (
    <button
      className={`themed-button ${className}`}
      style={getButtonStyles[variant]}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}

/**
 * 主题色卡片组件
 */
interface ThemedCardProps {
  children: ReactNode;
  className?: string;
}

export function ThemedCard({ children, className = "" }: ThemedCardProps) {
  const { getCardStyles } = useStudyTypeThemeContext();

  return (
    <div className={`themed-card ${className}`} style={getCardStyles}>
      {children}
    </div>
  );
}

/**
 * 主题色渐变背景组件
 */
interface ThemedGradientProps {
  children: ReactNode;
  className?: string;
}

export function ThemedGradient({
  children,
  className = "",
}: ThemedGradientProps) {
  const { getGradientStyles } = useStudyTypeThemeContext();

  return (
    <div className={`themed-gradient ${className}`} style={getGradientStyles}>
      {children}
    </div>
  );
}
