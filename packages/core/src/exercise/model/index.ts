'use client';
export type { CommonQuestionAnswer, CommonQuestionAnswerOption, QuestionTag, AiExplanationRecord, AiExplanation } from '@repo/core/types';

// 统一导出 exercise 模块的所有接口


// 前端类型定义
export type {
  SubmitAnswerPayload,
  GetNextQuestionParams,
  FeedbackData,
  NextQuestionInfo,
  ProgressInfo,
  StudentAnswer,
  AnswerStat, // 🔥 答题统计数据结构
  UserAnswerData, // 🔥 用户答案数据类型
  QuestionState, // 🔥 题目状态类型
  ApiGetNextQuestionData, // 🔥 API 获取下一题数据类型
  SubmitAnswerResponse, // 🔥 提交答案响应类型
  // 兼容性类型导出
  SubmitAnswer,

} from './types';

// 🔥 所有类型定义现在都在 types.ts 中，无需 schema 校验
