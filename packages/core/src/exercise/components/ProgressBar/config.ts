

export type EffectType =
  | "endCircleBurst"
  | "lightningWrap"
  | "fullBarBlast"
  | "fullScreenConfetti"
  | "streakBlocks";

export type ProgressAnimationMode =
  | "static"
  | "default"
  | "correct"
  | "continuous_2_4"
  | "continuous_4_8"
  | "continuous_8_plus"
  | "all_correct"
  | "incorrect";

// 音频文件 CDN 路径配置
export const getBaseUrl = () => {
  const env = process.env.NODE_ENV;
  if (env === "development") {
    return "";
  } else if (env === "production") {
    // 生产环境使用 CDN 路径
    return "https://static.xiaoluxue.com/stu/_next/public";
  } else {
    // 其他所有环境使用 CDN 路径
    return "https://static.test.xiaoluxue.cn/stu/_next/public";
  }
};

// 音频文件路径配置 - 使用字符串路径而不是直接导入
export const audioFiles = {
  correct: `${getBaseUrl()}/audios/single-correct.wav`,
  incorrect: `${getBaseUrl()}/audios/incorrect.wav`,
  continuous: `${getBaseUrl()}/audios/continuous-correct.wav`,
};

export const audioCache: Map<string, HTMLAudioElement> = new Map();
export interface AnimationItem {
  id: string;
  mode: ProgressAnimationMode;
  text?: string;
  progress?: number;
  onComplete?: () => void;
}
export const ANIMATION_CONFIG: Record<
  ProgressAnimationMode,
  { effects: EffectType[]; duration: number; audio?: string }
> = {
  static: { effects: [], duration: 0, audio: undefined },
  default: { effects: [], duration: 0, audio: undefined },
  correct: {
    effects: ["endCircleBurst"],
    duration: 1200,
    audio: audioFiles.correct,
  },
  continuous_2_4: {
    effects: ["endCircleBurst"],
    duration: 1200,
    audio: audioFiles.continuous,
  },
  continuous_4_8: {
    effects: ["endCircleBurst", "lightningWrap"],
    duration: 1300,
    audio: audioFiles.continuous,
  },
  continuous_8_plus: {
    effects: ["endCircleBurst", "lightningWrap", "streakBlocks"],
    duration: 1400,
    audio: audioFiles.continuous,
  },
  all_correct: {
    effects: ["lightningWrap", "fullBarBlast", "fullScreenConfetti"],
    duration: 1400,
    audio: audioFiles.continuous,
  },
  incorrect: { effects: [], duration: 1200, audio: audioFiles.incorrect },
};
