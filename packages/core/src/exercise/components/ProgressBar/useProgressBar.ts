// packages/core/src/exercise/components/ProgressBar/useProgressBar.ts
import { useCallback, useEffect, useState } from "react";
import {
    ANIMATION_CONFIG,
    AnimationItem,
    audioCache,
    ProgressAnimationMode,
} from "./config";
// ✨ Export the audio player function so the hook can use it
export const playAudioEffect = (audio?: string) => {
    if (!audio) return;
    try {
        let audioElement = audioCache.get(audio);
        if (!audioElement) {
            audioElement = new Audio(audio);
            audioElement.volume = 0.7;
            audioCache.set(audio, audioElement);
        }
        audioElement.currentTime = 0;
        audioElement
            .play()
            .catch((error) => console.warn(`[Audio] Play failed: ${audio}`, error));
    } catch (error) {
        console.warn(`[Audio] Load failed: ${audio}`, error);
    }
};
export interface HandleProgressParams {
    type: Exclude<
        ProgressAnimationMode,
        "static" | "continuous_2_4" | "continuous_4_8" | "continuous_8_plus"
    >;
    text?: string;
    progress: number;
    correctComboCount?: number;
    isAllCorrect?: boolean;
}

export const useProgressBar = ({
    initialProgress,
}: {
    initialProgress?: number;
} = {}) => {
    const [progress, setProgress] = useState(() => initialProgress ?? 0);

    const [currentItem, setCurrentItem] = useState<AnimationItem | null>(null);
    const [queue, setQueue] = useState<AnimationItem[]>([]);
    const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
    const isAnimating = !!currentItem;

    const getActiveType = useCallback(
        (
            type: HandleProgressParams["type"],
            {
                isAllCorrect = false,
                correctComboCount = 0,
            }: { isAllCorrect?: boolean; correctComboCount?: number }
        ) => {
            if (isAllCorrect) return "all_correct";
            if (type === "correct") {
                if (correctComboCount >= 8) return "continuous_8_plus";
                if (correctComboCount >= 4) return "continuous_4_8";
                if (correctComboCount >= 2) return "continuous_2_4";
                if (correctComboCount >= 1) return "correct";
            }
            return type;
        },
        []
    );

    const processQueue = useCallback(() => {
        if (isAnimating || queue.length === 0) return;

        const [nextItem, ...remainingQueue] = queue;
        if (!nextItem) return;

        setCurrentItem(nextItem);
        setQueue(remainingQueue);

        if (typeof nextItem.progress !== "undefined") {
            setProgress(nextItem.progress);
        }

        const config = ANIMATION_CONFIG[nextItem.mode];
        playAudioEffect(config.audio);

        const id = setTimeout(() => {
            setCurrentItem(null);
            setTimeoutId(null);
        }, config.duration);
        setTimeoutId(id);
    }, [isAnimating, queue]);

    useEffect(() => {
        processQueue();
    }, [queue, currentItem, processQueue]);

    // 🔧 清理定时器，防止内存泄漏
    useEffect(() => {
        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        };
    }, [timeoutId]);

    const handleProgress = useCallback(
        ({
            type,
            text,
            progress,
            correctComboCount,
            isAllCorrect,
        }: HandleProgressParams) => {
            const _activeType = getActiveType(type, {
                isAllCorrect,
                correctComboCount,
            });

            const newAnimationItem: AnimationItem = {
                id: `anim_${Date.now()}_${Math.random()}`, // 增加随机数确保唯一性
                mode: _activeType,
                text: text ?? "",
                progress: progress,
            };

            setQueue((prevQueue) => [...prevQueue, newAnimationItem]);
        },
        [getActiveType]
    );

    const reset = useCallback(() => {
        setQueue([]);
        setCurrentItem(null);
        setProgress(0);
    }, []);

    // ✨ 核心变更：将 animationId 传递出去
    const progressBarProps = {
        progress,
        activeType: currentItem?.mode ?? "static",
        explosionText: currentItem?.text ?? "",
        animationId: currentItem?.id ?? undefined,
    };

    const controllers = {
        handleProgress,
        reset,
        isAnimating,
    };

    return { progressBarProps, ...controllers } as const;
};