// packages/core/src/exercise/components/ProgressBar/ProgressBar.tsx
"use client";

import { AnimatePresence, motion } from "framer-motion";
import Lottie, { LottieComponentProps } from "lottie-react";
import Image from "next/image";
import React, { useEffect, useMemo, useState } from "react";

import { EnhancedCanvasText } from "@repo/core/components/EnhancedCanvasText";
import allCorrectAnimation from "@repo/core/public/assets/stu-exercise/lottie/all-correct-blocks.json";
import continuousCorrectAnimation from "@repo/core/public/assets/stu-exercise/lottie/continuous-correct-blocks.json";
import fullScreenAnimation from "@repo/core/public/assets/stu-exercise/lottie/full-screen-block.json";
import singleCorrectAnimation from "@repo/core/public/assets/stu-exercise/lottie/single-correct-circle.json";
import ReactDOM from "react-dom";
import { ANIMATION_CONFIG, getBaseUrl, ProgressAnimationMode } from "./config";

export type ProgressDisplayMode = "number" | "progress";

const DelayLottie = React.memo(
  ({
    animationData,
    loop,
    className,
    rendererSettings,
  }: {
    animationData: Record<string, unknown>;
    loop: boolean;
    className: string;
    rendererSettings?: LottieComponentProps["rendererSettings"];
  }) => {
    const [play, setPlay] = useState(false);
    useEffect(() => {
      const timer = setTimeout(() => setPlay(true), 0);
      return () => clearTimeout(timer);
    }, []);

    if (!play) return null;
    return (
      <Lottie
        animationData={animationData}
        loop={loop}
        className={className}
        rendererSettings={rendererSettings}
      />
    );
  }
);
DelayLottie.displayName = "DelayLottie";

const BaseProgressBar = React.memo(
  ({
    progress,
    activeType,
    explosionText,
    animationId, // 接收 animationId
  }: {
    progress: number;
    activeType: ProgressAnimationMode;
    explosionText: string;
    animationId?: string; // 声明类型
  }) => {
    const normalizedProgress = Math.min(Math.max(0, progress), 100);

    // 🔥 优化宽度计算：确保始终有最小宽度，同时保持动画流畅性
    const { progressWidth, initialWidth } = useMemo(() => {
      const baseWidth = 280 * (normalizedProgress / 100);

      // 始终保持最小宽度 20px，确保进度条可见性
      const finalWidth = Math.max(20, baseWidth + 4);

      // 动画初始宽度逻辑：
      // - 如果是从 0 开始，初始宽度为 20px（最小宽度）
      // - 如果计算宽度已经大于最小宽度，从 0 开始动画更自然
      const initWidth = baseWidth <= 20 ? 20 : 0;

      return { progressWidth: finalWidth, initialWidth: initWidth };
    }, [normalizedProgress]);

    const effects = ANIMATION_CONFIG[activeType]?.effects || [];
    const showEndCircleBurst = effects.includes("endCircleBurst");
    const showLightningWrap = effects.includes("lightningWrap");
    const showFullBarBlast = effects.includes("fullBarBlast");
    const showFullScreenConfetti = effects.includes("fullScreenConfetti");
    const showStreakBlocks = effects.includes("streakBlocks");
    const showExplosionText = !!explosionText && activeType !== "static";

    const isAnimating = activeType !== "static";

    return (
      <div className="progress-bar-container relative h-4 w-[17.5rem] flex-shrink-0 overflow-visible rounded-[1.25rem] bg-[rgba(31,29,27,0.08)]">
        <motion.div
          className="progress-bar-fill absolute -top-0.5 left-0 z-20 h-5 translate-x-[-2px]"
          initial={{ width: initialWidth }}
          animate={{ width: progressWidth }}
          transition={{
            type: "spring",
            stiffness: 800,
            damping: 40,
            mass: 0.5,
            velocity: 0,
          }}
          style={{
            willChange: "width, transform",
            transform: "translateZ(0)",
            backfaceVisibility: "hidden",
          }}
        >
          {/* 🔥 优化：始终显示进度条内容，因为现在总是有最小宽度 */}
          <motion.div
            className="progress-bar-inner absolute inset-0 z-10 h-5 rounded-[10px] border-2 border-white/60"
            style={{ borderColor: "rgba(255, 255, 255, 0.6)" }}
            // 播放和静止状态的透明度
            // animate={{ opacity: isAnimating ? 1 : 0.4 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            initial={{ opacity: 0 }}
          >
            <div
              className="progress-bar-highlight z-1 absolute left-[6px] right-[6px] top-[3px] h-[3px] rounded-full opacity-50 mix-blend-plus-lighter"
              style={{ backgroundColor: "rgba(255, 255, 255, 0.3)" }}
            />
            <motion.div
              className="progress-bar-gradient relative mt-0 h-4 w-full rounded-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              style={{
                background: `linear-gradient(90deg, #3E98FF 0%, #3EDD72 ${100}%)`,
              }}
            />
          </motion.div>
          {/* ✨ 核心变更：为 AnimatePresence 的子元素添加唯一的 key */}
          <AnimatePresence>
            {showEndCircleBurst && (
              <DelayLottie
                key={`endCircleBurst-${animationId}`}
                animationData={singleCorrectAnimation}
                loop={false}
                className="pointer-events-none absolute right-0 top-1/2 h-20 w-20 -translate-y-1/2 translate-x-[48%]"
              />
            )}
            {showStreakBlocks && (
              <DelayLottie
                key={`streakBlocks-${animationId}`}
                animationData={continuousCorrectAnimation}
                loop={false}
                className="continuousCorrectAnimation z-15 pointer-events-none absolute left-[-25%] top-1/2 !h-[90px] w-[150%] -translate-y-1/2"
                rendererSettings={{ preserveAspectRatio: "none" }}
              />
            )}
            {showLightningWrap && (
              <motion.div
                key={`lightningWrap-${animationId}`}
                className="pointer-events-none absolute left-[-7%] top-[-60%] z-[-1] h-[220%] w-[115%]"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: [0, 1, 0], scale: 1 }}
                transition={{
                  duration: 1.0,
                  times: [0, 0.5, 1],
                  ease: "easeInOut",
                }}
              >
                <Image
                  src={`${getBaseUrl()}/images/lightning.webp`}
                  alt="Lightning Wrap"
                  width={50}
                  height={50}
                  unoptimized
                  className="h-full w-full"
                />
              </motion.div>
            )}
            {showExplosionText && (
              <motion.div
                key={`explosionText-${animationId}`}
                className="explosion-text-container absolute top-0 z-10 -translate-y-full rotate-[0deg]"
                initial={{
                  scale: 0,
                  opacity: 0,
                  left: `${progressWidth / 2}px`, // 与已走进度条中心对齐，最大不超过140px
                  transform: `translateX(-50%) translateY(0%) rotate(0deg)`,
                }}
                animate={{ scale: [0, 1.3, 1, 1, 0], opacity: [0, 1, 1, 1, 0] }}
                transition={{
                  duration: 1.0,
                  times: [0, 0.2, 0.3, 0.8, 1.0],
                  ease: "easeInOut",
                }}
              >
                {/* 如果是错误图片透明度为 0 */}
                {/* <div
                  className={cn(
                    "explosion-text-background absolute left-1/2 top-1/2 z-[-1] h-[100%] w-[110%] shrink-0 -translate-x-[49%] -translate-y-[35%] opacity-100",
                    activeType == "incorrect" ? "opacity-0" : ""
                  )}
                >
                  <Image
                    className="h-full w-full"
                    src={ProgressBarTextBackground}
                    alt="ProgressBarTextBackground"
                  />
                </div> */}
                <EnhancedCanvasText
                  className="explosion-text-canvas -skew-x-[10deg]"
                  text={explosionText}
                  fontSize={20}
                  fontWeight={900}
                  color="#0DA7D6"
                  strokeColor="rgba(255, 255, 255, 0.90)"
                  strokeWidth={2}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
        {showFullBarBlast && (
          <DelayLottie
            key={`fullBarBlast-${animationId}`}
            animationData={allCorrectAnimation}
            loop={false}
            className="z-15 pointer-events-none absolute left-[-25%] top-1/2 w-[150%] -translate-y-1/2"
          />
        )}
        {showFullScreenConfetti &&
          ReactDOM.createPortal(
            <>
              <DelayLottie
                key={`fullScreenConfetti-${animationId}`}
                animationData={fullScreenAnimation}
                loop={false}
                className="z-15 pointer-events-none absolute left-0 top-0 h-screen w-screen"
              />
            </>,
            document.body
          )}
      </div>
    );
  }
);
BaseProgressBar.displayName = "BaseProgressBar";

interface ProgressBarProps {
  progress: number;
  activeType: ProgressAnimationMode;
  explosionText?: string;
  animationId?: string; // 添加 animationId 类型

  // 预览模式
  isPreviewMode?: boolean;
  displayMode?: "number" | "progress";
  currentIndex?: number;
  totalCount?: number;
  className?: string;
}

export type { ProgressBarProps };

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  activeType,
  explosionText = "",
  animationId, // 接收 animationId
  isPreviewMode = false,
  displayMode = "number",
  currentIndex = 0,
  totalCount = 1,
  className = "",
}) => {
  const shouldShowNumber = isPreviewMode && displayMode === "number";

  if (shouldShowNumber) {
    const displayCurrent =
      typeof currentIndex === "number" ? currentIndex + 1 : 1;
    const displayTotal = totalCount || 1;

    return (
      <div
        className={`preview-progress-display flex items-center ${className}`}
      >
        <span className="text-text-4 text-sm font-medium">
          {displayCurrent} / {displayTotal}
        </span>
      </div>
    );
  }

  return (
    <BaseProgressBar
      progress={progress}
      activeType={activeType}
      explosionText={explosionText}
      animationId={animationId} // 传递 animationId
    />
  );
};
