import React from 'react';
import Image from 'next/image';
import BackIcon from "@repo/core/public/assets/stu-exercise/icons/back.svg";

interface BackButtonProps {
  onClick: () => void;
  className?: string;
}

/**
 * 练习页面返回按钮组件
 */
export const BackButton: React.FC<BackButtonProps> = ({
  onClick,
  className = '',
}) => {
  return (
    <button
      onClick={onClick}
      className={`flex justify-center items-center gap-1 flex-shrink-0 rounded-lg text-text-1 w-6 h-6 ${className}`}
      aria-label="返回"
    >
      <BackIcon ></BackIcon>
    </button>
  );
};

export default BackButton; 