"use client";

import { InteractiveExplanation } from "@repo/core/interactive-explanation/view";
import type { AiExplanation, AiExplanationRecord } from "@repo/core/types";
import { cn } from "@repo/ui/lib/utils";
import React, { memo, Suspense, useMemo } from "react";
import { FormatMath } from "../format-math";

// 导入SVG图标
import { StudyType } from "@repo/core/enums";

interface QuestionExplanationProps {
  questionId: string;
  /** 解析内容 */
  explanation: string;
  /** 额外的CSS类名 */
  className?: string;
  /** 是否显示标题 */
  showTitle?: boolean;
  /** 自定义标题文本 */
  title?: string;
  /** 🔥 互动解题数据 */
  aiExplanation?: AiExplanation | null;
  /** 🔥 互动解题记录 */
  aiExplanationRecord?: AiExplanationRecord | null;
  /** 🔥 学习会话ID，用于互动讲题进度上报 */
  studySessionId?: number;
  /** 是否是预览模式 */
  preview?: boolean;
  studyType?: StudyType;
}

/**
 * 题目解析组件
 * 用于显示题目的答案解析内容，支持数学公式渲染
 * 🔥 新增：支持互动讲题功能，当有aiExplanation数据时显示互动讲题按钮
 * 根据Figma设计稿实现：水平布局，标题在左，解析内容在右
 */
export const QuestionExplanation: React.FC<QuestionExplanationProps> = memo(
  function QuestionExplanation({
    questionId,
    explanation = "暂无解析",
    className,
    showTitle = true,
    title = "题目解析:",
    aiExplanation,
    aiExplanationRecord,
    studySessionId,
    preview = false,
    studyType,
  }: QuestionExplanationProps) {
    // 🔥 优化：使用useMemo缓存计算结果, 预览模式下也显示互动讲题按钮
    const hasInteractiveExplanation = useMemo(
      () => !!aiExplanation,
      [aiExplanation]
    );

    return (
      <div
        className={cn(
          "question-explanation flex w-full flex-col items-start gap-2",
          className
        )}
      >
        {/* 标题 */}
        {showTitle && (
          <div className="font-bold text-text-1 text-[1.0625rem] leading-[1.5em] explanation-title">
            {title}
          </div>
        )}

        {/* 传统解析内容 - 🔥 优化：使用useMemo缓存FormatMath组件 */}
        <div className="font-normal flex-1 text-text-4 text-[1.0625rem] explanation-content">
          {useMemo(
            () => (
              <FormatMath htmlContent={explanation} questionId={questionId} />
            ),
            [explanation, questionId]
          )}
        </div>

        {/* 🔥 互动讲题按钮 - 优先显示 */}
        {hasInteractiveExplanation && (
          <>
            {/* 🔥 互动讲题浮层 - 动态导入避免打包体积 */}
            <Suspense fallback={<></>}>
              <InteractiveExplanation
                preview={preview}
                questionId={questionId}
                studySessionId={studySessionId || 0}
                initialData={aiExplanation!}
                initialRecord={aiExplanationRecord || undefined}
                studyType={studyType!}
              />
            </Suspense>
          </>
        )}
      </div>
    );
  }
);
