# 题目组件库

## 简介

题目组件库是一套为学生端答题场景设计的 React 组件集合，提供了完整的题目展示、作答和反馈功能。组件库遵循模块化设计原则，支持多种题型，并具有高度的可扩展性。

## 特性

- **类型安全**：全面使用 TypeScript，提供完整类型定义
- **组件化**：将题目界面拆分为可复用的小组件
- **多题型支持**：支持选择题、填空题、判断题、简答题等多种题型
- **模块化**：组件可单独导入，实现按需加载

## 组件分类

题目组件库分为以下几个类别：

### [头部组件](/header)
提供题目页面顶部的导航和信息显示组件，包括返回按钮、进度条、计时器等。

### [操作组件](/actions)
提供与题目相关的操作按钮，如继续按钮、加入错题本按钮等。

### [内容区域组件](/content)
提供题目内容展示相关的组件，包括题干、题目说明、题型标签等。

### [答案及解析区域组件](/answer)
提供答案输入、展示和解析相关的组件，如选项列表、正确答案、题目解析等。

### [解析组件](/explanation)
提供题目解析展示相关的组件，包括解析标题和解析内容的格式化展示。

## 使用示例

```tsx
import {
  QuestionContent,
  ProgressBar,
  TimerDisplay,
  QuestionExplanation
} from "apps/stu/app/components/question";

// 计时器显示示例
<TimerDisplay timeSeconds={120} />

// 进度条示例
<ProgressBar progress={75} />

// 题目解析示例
<QuestionExplanation
  explanation="这是题目的详细解析内容..."
/>

// 自定义标题
<QuestionExplanation
  explanation="解析内容..."
  title="详细解析:"
/>

// 隐藏标题
<QuestionExplanation
  explanation="解析内容..."
  showTitle={false}
/>
```

## 设计原则

1. **一致性**：所有组件遵循统一的设计语言
2. **可组合性**：组件可以自由组合构建复杂题目界面
3. **可访问性**：支持键盘导航和屏幕阅读器
4. **性能优化**：组件设计考虑性能因素
5. **可扩展性**：支持通过 className 和自定义属性扩展 

# Question 题目组件

练习题目相关的UI组件集合。

## 组件列表

### TimerDisplay 计时器显示组件

**位置**: `TimerDisplay.tsx`

**功能**: 独立管理计时状态的显示组件

#### 核心特性
- **独立计时**: 内部使用 useState 管理显示时间，避免 ViewModel 重新渲染
- **时间同步**: 通过 onTimeUpdate 回调同步时间给 ViewModel
- **自动重置**: 检测 shouldReset 信号自动重置计时器
- **时间限制**: 达到59:59后自动停止计时，保持在最大值

#### 🔥 新功能：时间限制管理

根据产品需求，计时器达到59:59（3599秒）后会：
- ✅ 停止继续计时，显示时间固定在59:59
- ✅ 时间文字变为橙色，提示用户已达到最大显示时间
- ✅ 其他操作按钮完全不受影响，用户可以正常答题
- ✅ 重置后恢复正常计时

#### Props接口
```typescript
interface TimerDisplayProps {
  timerControl: {
    isActive: boolean | null;    // 是否激活计时
    onTimeUpdate: (time: number) => void;  // 时间更新回调
    shouldReset: string;         // 重置信号（通常是题目ID）
  };
  className?: string;            // 自定义样式类名
}
```

#### 使用示例
```tsx
<TimerDisplay
  timerControl={{
    isActive: true,
    onTimeUpdate: (time) => console.log('当前时间:', time),
    shouldReset: questionId
  }}
  className="ml-2"
/>
```

#### 视觉反馈
- **正常计时**: 默认文字颜色显示
- **达到限制**: 文字变为橙色（text-orange-500）
- **鼠标悬停**: 显示提示信息"已达到最大显示时间 59:59"

### TODO
- [ ] 考虑是否需要添加音效提示（产品确认后）
- [ ] 优化长时间答题的用户体验

## 架构说明

所有组件都遵循 MVVM 架构：
- **View**: 纯 UI 展示，接收 props 数据
- **ViewModel**: 业务逻辑处理，状态管理
- **Model**: 数据结构定义，API 调用

## 开发规范

1. 每个组件都应该有完整的 TypeScript 类型定义
2. 组件应该包含语义化的 className，便于调试
3. 复杂逻辑应该抽取到 hooks 或 ViewModel 中
4. 组件应该支持必要的可访问性属性 