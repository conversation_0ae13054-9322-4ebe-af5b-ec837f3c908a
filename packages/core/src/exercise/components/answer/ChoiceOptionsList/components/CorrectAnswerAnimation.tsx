import { cn } from "@repo/ui/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { memo } from "react";
import StarTwinkleEffects from "./StarTwinkleEffects";

// 内部组件：动效组件
const CorrectAnswerAnimation = memo(
  ({
    optionId,
    isAnimating,
    className,
  }: {
    optionId: string;
    isAnimating: boolean;
    className?: string;
  }) => {
    return (
      <AnimatePresence>
        {isAnimating && (
          <motion.div
            key={`anim-${optionId}`}
            className={cn(
              "pointer-events-none absolute inset-0 z-10 h-full w-full overflow-visible rounded-lg",
              className
            )}
            initial="hidden"
            animate="visible"
            exit="hidden"
            transition={{ staggerChildren: 0.1 }}
          >
            {/* 平行四边形滑动动效 */}
            <motion.div
              className="z-11 absolute inset-0 h-full w-full overflow-hidden"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1, transition: { duration: 0.1 } }}
              exit={{ opacity: 0, transition: { duration: 0.1, delay: 1.1 } }}
            >
              <motion.div
                className="absolute inset-y-0 -left-0 top-0 flex items-center justify-center"
                initial={{ x: "-150%" }}
                animate={{
                  x: "100vw",
                  transition: { duration: 1.2, ease: "easeOut", delay: 0 },
                }}
              >
                <div className="h-full w-[7.875rem]">
                  <svg
                    style={{ height: "100%" }}
                    viewBox="0 0 126 51"
                    fill="none"
                    preserveAspectRatio="none"
                  >
                    <path
                      d="M30 0H126L96 50.5H0L30 0Z"
                      fill="white"
                      fillOpacity="0.6"
                    />
                  </svg>
                </div>
                <motion.div
                  className="ml-2 h-full w-[3.875rem]"
                  initial={{ x: "-150%" }}
                  animate={{
                    x: "100vw",
                    transition: { duration: 1.2, ease: "easeOut", delay: 0 },
                  }}
                >
                  <svg
                    style={{ height: "100%" }}
                    viewBox="0 0 62 51"
                    fill="none"
                    preserveAspectRatio="none"
                  >
                    <path
                      d="M30 0H62L32 50.5H0L30 0Z"
                      fill="white"
                      fillOpacity="0.6"
                    />
                  </svg>
                </motion.div>
              </motion.div>
            </motion.div>

            {/* 轮廓闪烁动效 */}
            <motion.div
              className="absolute inset-0 rounded-lg"
              initial={{
                opacity: 0,
                scale: 1,
                boxShadow: "0 0 0 0px transparent",
              }}
              animate={{
                opacity: [0, 1, 0],
                scale: [1, 1.0, 1],
                boxShadow: [
                  `0 0 0 0px var(--color-green-1, #22c55e)`,
                  `0 0 0 3px var(--color-green-1, #22c55e)`,
                  `0 0 0 0px var(--color-green-1, #22c55e)`,
                ],
                transition: { duration: 0.8, ease: "easeOut", delay: 0 },
              }}
            />

            {/* 星星闪烁动效 */}
            <StarTwinkleEffects />
          </motion.div>
        )}
      </AnimatePresence>
    );
  }
);
CorrectAnswerAnimation.displayName = "CorrectAnswerAnimation";
export default CorrectAnswerAnimation;
