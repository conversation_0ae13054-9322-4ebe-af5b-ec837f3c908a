import { motion } from "framer-motion";
import { memo } from "react";

// 内部组件：星星闪烁效果
const StarTwinkleEffects = memo(() => {
  const starConfigs = [
    { className: "star-1 absolute top-0 -left-[0.7rem] rotate-40", delay: 0.0 },
    {
      className: "star-2 absolute top-2 left-[1rem] scale-[0.8] rotate-15",
      delay: 0.2,
    },
    { className: "star-3 absolute bottom-1 right-18", delay: 0.4 },
  ];

  return (
    <motion.div
      className="z-12 absolute inset-0"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1, transition: { delay: 0 } }}
      exit={{ opacity: 0, transition: { duration: 0.4, delay: 1.1 } }}
    >
      {starConfigs.map((config, index) => (
        <motion.div
          key={index}
          className={`star-twinkle-animation ${config.className}`}
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: [0, 1, 0], scale: [0, 1.2, 0] }}
          transition={{ duration: 1, ease: "easeOut", delay: config.delay }}
        >
          <svg width="19" height="18" viewBox="0 0 19 18" fill="none">
            <path
              d="M0.854696 4.91139C0.325181 4.36913 0.842406 3.47327 1.57678 3.66071L8.00967 5.30262C8.26312 5.36731 8.53198 5.29527 8.71913 5.11252L13.4692 0.47414C14.0115 -0.0553752 14.9073 0.46185 14.7199 1.19622L13.078 7.62911C13.0133 7.88257 13.0853 8.15142 13.2681 8.33857L17.9065 13.0887C18.436 13.6309 17.9188 14.5268 17.1844 14.3393L10.7515 12.6974C10.498 12.6327 10.2292 12.7048 10.042 12.8875L5.29195 17.5259C4.74968 18.0554 3.85383 17.5382 4.04127 16.8038L5.68318 10.3709C5.74787 10.1175 5.67583 9.84864 5.49308 9.66148L0.854696 4.91139Z"
              fill="#79E32B"
            />
          </svg>
        </motion.div>
      ))}
    </motion.div>
  );
});
StarTwinkleEffects.displayName = "StarTwinkleEffects";
export default StarTwinkleEffects;
