import { memo } from "react";
import { OptionState } from "..";
// 内联图标组件，避免路径导入问题
const CorrectIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    viewBox="0 0 28 28"
    fill="none"
  >
    <path
      d="M20.2549 6.81934C20.7358 6.30129 21.482 6.51176 21.7539 6.76367C22.2721 7.2445 21.8646 7.64644 21.5391 7.99609C18.6314 11.1192 15.2016 15.3781 12.8359 19.9336C12.5143 20.5528 11.6596 20.6847 11.0166 20.1699C11.0056 20.1611 10.9951 20.1516 10.9844 20.1426C10.9015 20.0857 10.8257 20.0152 10.7607 19.9316L6.21094 14.082L6.15234 13.998C5.8831 13.5687 5.97726 12.9967 6.38574 12.6787C6.79445 12.3609 7.37266 12.4098 7.72266 12.7764L7.78906 12.8535L11.161 17.1902C11.286 17.3509 11.5313 17.3433 11.6468 17.1756C13.9663 13.808 17.2242 9.69331 20.2549 6.81934Z"
      fill="#449908"
    />
  </svg>
);

const ErrorIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    viewBox="0 0 28 28"
    fill="none"
  >
    <path
      d="M8.79541 8.42688C11.3363 10.8966 16.9545 17.0657 19.2046 19.9875"
      stroke="#D64A2B"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.83677 19.9544C10.6004 15.796 15.2116 10.5729 18.4035 7.58496C18.8108 7.25214 19.4425 7.32945 19.8962 7.72964C20.5139 8.27463 20.5205 9.15522 19.9543 9.59501C15.7895 12.83 12.0923 17.0724 9.44487 20.5763C9.14847 20.9686 8.45502 21.0112 8.08994 20.8024C7.76811 20.6183 7.64662 20.2175 7.83677 19.9544Z"
      fill="#D64A2B"
    />
  </svg>
);

// 🔥 简洁化的状态指示器
const StateIndicator = memo(
  ({
    state,
    optionKey,
    hasImages,
    getOptionPercentage,
    questionType,
    hasIncorrect,
  }: {
    state: OptionState;
    optionKey: string;
    hasImages: boolean;
    getOptionPercentage: (key: string) => string;
    questionType: "single_choice" | "multiple_choice" | "true_false";
    hasIncorrect: boolean;
  }) => {
    const percentage = getOptionPercentage(optionKey);

    // 🔥 统一的样式配置
    const styles = {
      container: hasImages
        ? "absolute right-[0.8rem] bottom-[0.76rem] flex items-center flex-row-reverse"
        : "flex items-center gap-1",

      percentage: {
        className: hasImages
          ? "rounded-[1.25rem] bg-black/20 h-5 px-2 pl-5 flex items-center text-sm font-normal text-white -ml-[1.125rem]"
          : "text-text-4 text-sm font-normal",
        style: hasImages ? { backdropFilter: "blur(5px)" } : {},
      },

      icon: `flex items-center justify-center rounded-full ${
        hasImages ? "h-6.5 relative z-30 mb-[.125rem]" : "h-7 w-7"
      }`,

      missedLabel: `mr-[-0.25rem] flex w-[2rem] items-center justify-center rounded border border-orange-400/60 bg-orange-50 text-xs font-medium text-orange-700 ${
        hasImages ? "h-6.5 relative z-30 mb-[.125rem]" : ""
      }`,
    };

    // 🔥 状态内容配置
    const stateContent = {
      [OptionState.CORRECT]:
        questionType === "single_choice" && hasIncorrect ? null : (
          <CorrectIcon className={hasImages ? "h-full w-full" : ""} />
        ),
      [OptionState.INCORRECT]: (
        <ErrorIcon className={hasImages ? "h-full w-full" : ""} />
      ),
      [OptionState.MISSED]: <span className={styles.missedLabel}>漏选</span>,
      [OptionState.NORMAL]: null,
      [OptionState.SELECTED]: null,
    };

    const content = stateContent[state];
    const showIcon =
      state === OptionState.CORRECT || state === OptionState.INCORRECT;

    return (
      <div className={styles.container}>
        {percentage && (
          <span
            className={styles.percentage.className}
            style={styles.percentage.style}
          >
            {percentage}
          </span>
        )}
        {showIcon ? (
          <div className={styles.icon}>{content}</div>
        ) : state === OptionState.MISSED ? (
          content
        ) : (
          <div className={styles.icon}>{/* 保留空间 */}</div>
        )}
      </div>
    );
  }
);
StateIndicator.displayName = "StateIndicator";
export default StateIndicator;
