"use client";

import { useSignal, useSignalEffect } from "@preact-signals/safe-react";
import React, { useEffect, useRef } from "react";

// Props 接口和 formatTime, MAX_DISPLAY_TIME_MS 常量部分保持不变...
interface TimerDisplayProps {
  timerControl: {
    isActive: boolean | null;
    onTimeUpdate: (timeMs: number) => void;
    shouldReset: string;
    initialTime?: number;
  };
  className?: string;
  previewMode?: boolean;
  previewDurationMs?: number;
}
const formatTime = (totalMs: number): string => {
  const totalSeconds = Math.floor(totalMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  const formattedHours =
    hours > 0 ? `${hours.toString().padStart(2, "0")}:` : "";
  const formattedMinutes = `${minutes.toString().padStart(2, "0")}:`;
  const formattedSeconds = seconds.toString().padStart(2, "0");
  return `${formattedHours}${formattedMinutes}${formattedSeconds}`;
};
const MAX_DISPLAY_TIME_MS = 3599000;

/**
 * 计时器组件 (V5 - 终极简化版)
 *
 * 核心设计:
 * 1. `startTimeRef` 和 `lastResetId` 因其明确且必要的作用被保留。
 * 2. 将所有“停止”逻辑统一到 `useSignalEffect` 的清理函数中，代码更整洁。
 * 3. Effect 主体现在只负责“启动”和“状态准备”，逻辑更单一。
 */
export const TimerDisplay: React.FC<TimerDisplayProps> = React.memo(
  ({
    timerControl,
    className = "",
    previewMode = false,
    previewDurationMs,
  }) => {
    const displayMs = useSignal(timerControl.initialTime || 0);
    const controlSignal = useSignal(timerControl);

    const animationFrameId = useRef<number | null>(null);
    const startTimeRef = useRef<number | null>(null);
    const lastResetId = useRef(timerControl.shouldReset);

    // 桥梁 Effect: 保持不变，用于同步 props 到 signal
    useEffect(() => {
      controlSignal.value = timerControl;
    }, [timerControl, controlSignal]);

    // 核心逻辑 Effect: 经过简化，职责更清晰
    useSignalEffect(() => {
      const { isActive, onTimeUpdate, shouldReset, initialTime } =
        controlSignal.value;

      const tick = () => {
        if (startTimeRef.current === null) return;
        const elapsedMs = Date.now() - startTimeRef.current;

        if (elapsedMs >= MAX_DISPLAY_TIME_MS) {
          displayMs.value = MAX_DISPLAY_TIME_MS;
          onTimeUpdate(MAX_DISPLAY_TIME_MS);
        } else {
          displayMs.value = elapsedMs;
          onTimeUpdate(elapsedMs);
          animationFrameId.current = requestAnimationFrame(tick);
        }
      };

      // --- 重置逻辑 ---
      if (lastResetId.current !== shouldReset) {
        lastResetId.current = shouldReset;
        // 重置时，只需要更新状态。正在运行的计时器会被下面的清理函数自动停止。
        startTimeRef.current = null;
        displayMs.value = 0;
        onTimeUpdate(0);
      }

      // --- 启动/准备逻辑 ---
      if (isActive) {
        // 准备启动：如果计时器未启动，则设置好起跑线
        if (startTimeRef.current === null) {
          // 如果有 initialTime，则在启动时应用它
          if (initialTime) {
            displayMs.value = initialTime;
          }
          startTimeRef.current = Date.now() - displayMs.peek();
        }
        // 确保循环运行
        if (animationFrameId.current === null) {
          tick();
        }
      } else {
        // 准备暂停：重置 startTimeRef，为下一次恢复做准备。
        startTimeRef.current = null;
      }

      /**
       * 【关键简化】统一的清理函数
       * 这是停止计时的唯一地方。它会在 effect 重新运行前或组件卸载时执行。
       * 无论是 isActive 变为 false，还是组件被卸载，这个函数都会确保计时器被正确停止。
       */
      return () => {
        if (animationFrameId.current) {
          cancelAnimationFrame(animationFrameId.current);
          animationFrameId.current = null;
        }
      };
    });

    // --- 渲染逻辑 (保持不变) ---
    if (previewMode) {
      const displayTime = previewDurationMs || 0;
      return (
        <div
          className={`timer-display text-text-4 flex select-none items-center whitespace-nowrap text-[1.0625rem] leading-[125%] ${className}`}
        >
          <span className="mr-[0.125rem]">用时</span>
          <span>{formatTime(displayTime)}</span>
        </div>
      );
    }
    return (
      <div
        className={`timer-display text-text-4 flex select-none items-center whitespace-nowrap text-[1.0625rem] leading-[125%] ${className}`}
      >
        <span className="mr-[0.125rem]">用时</span>
        <span>{formatTime(displayMs.value)}</span>
      </div>
    );
  }
);

TimerDisplay.displayName = "TimerDisplay";
export default TimerDisplay;
