"use client";

import { MathJax } from "better-react-mathjax";
import { useEffect, useMemo, useState } from "react";

// 解决MathJax N次渲染导致内存溢出的问题
export const MathContent = ({ children }: { children: React.ReactNode }) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // const timer = setTimeout(() => {
    setIsClient(true);
    // }, 100);
    // return () => clearTimeout(timer);
  }, []);
  //TODO: 是否可以使用server worker来解决这个问题
  const content = useMemo(() => {
    return <MathJax inline={true}>{children}</MathJax>;
  }, [children]);

  return <>{isClient && content}</>;
};
