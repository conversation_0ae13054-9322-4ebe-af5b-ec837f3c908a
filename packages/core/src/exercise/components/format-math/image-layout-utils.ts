/**
 * 图片排版工具函数 - 简化版
 * 处理连续图片的智能排版，使用 Grid 布局
 */

// 图片基准尺寸常量
const BASE_IMAGE_DIMENSIONS = {
  width: 25.125, // rem
  height: 16.75, // rem
} as const;

/**
 * 检查图片是否已经被包装过
 */
function isImageAlreadyWrapped(imgTag: string): boolean {
  return imgTag.includes('image-with-preview') || imgTag.includes('image-preview-btn');
}

/**
 * 提取图片src地址
 */
function extractImageSrc(imgTag: string): string {
  const srcMatch = imgTag.match(/src="([^"]*)"/);
  return srcMatch ? srcMatch[1] || '' : '';
}

/**
 * 为图片添加预览按钮包装
 */
function wrapImageWithPreviewButton(imgTag: string, imageSrc: string): string {
  if (isImageAlreadyWrapped(imgTag)) {
    return imgTag;
  }

  return `
    <div class="image-with-preview image-preview-wrapper" style="
      position: relative; 
      display: block;
      width: 100%;
      height: 100%;

    ">
      <img src="${imageSrc}" style="
        width: 90%;
        object-fit: contain;
        display: block;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        max-width: 100%;
        max-height: 100%;
      " />
      <button
        data-image-src="${imageSrc}"
        class="image-preview-btn image-preview-trigger"
        style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.65); border: none; border-radius: 50%; width: 28px; height: 28px; cursor: pointer; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 8px rgba(0,0,0,0.15); transition: all 0.2s ease; z-index: 20;"
        title="放大查看图片"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none" >
          <g clip-path="url(#clip0_3281_17171)">
            <path d="M7.35132 0.853271C10.8949 0.853448 13.7672 3.7267 13.7673 7.27026C13.7673 8.77188 13.2496 10.1518 12.3855 11.2449L14.783 13.6423L14.8347 13.7C15.0747 13.9945 15.0574 14.4284 14.783 14.7029C14.5085 14.9772 14.0745 14.9946 13.78 14.7546L13.7224 14.7029L11.325 12.3054C10.232 13.1692 8.8526 13.6862 7.35132 13.6863C3.80761 13.6863 0.934502 10.8139 0.934326 7.27026C0.934491 3.72659 3.80761 0.853271 7.35132 0.853271ZM7.35132 2.35327C4.63603 2.35327 2.43449 4.55502 2.43433 7.27026C2.4345 9.9855 4.63604 12.1863 7.35132 12.1863C10.0664 12.1861 12.2672 9.98539 12.2673 7.27026C12.2672 4.55513 10.0665 2.35345 7.35132 2.35327ZM7.35132 4.52026C7.76553 4.52026 8.10132 4.85605 8.10132 5.27026V6.52124L9.35034 6.52026C9.76441 6.5199 10.1008 6.85518 10.1013 7.26929C10.1019 7.68339 9.76636 8.01955 9.35229 8.02026L8.10132 8.02124V9.27026C8.10132 9.68448 7.76553 10.0203 7.35132 10.0203C6.9371 10.0203 6.60132 9.68448 6.60132 9.27026V8.02319L5.35718 8.02515C4.94296 8.02569 4.60674 7.69034 4.6062 7.27612C4.60583 6.86206 4.94112 6.52569 5.35522 6.52515L6.60132 6.52319V5.27026C6.60132 4.85605 6.9371 4.52026 7.35132 4.52026Z" fill="#33302D" fill-opacity="0.4"/>
          </g>
          <defs>
            <clipPath id="clip0_3281_17171">
              <rect width="16" height="16" fill="white" transform="translate(0.351318 0.270264)"/>
            </clipPath>
          </defs>
        </svg>
      </button>
    </div>
  `;
}

/**
 * 处理连续图片组，使用 Grid 布局
 */
function formatImageGroup(images: string[], imagesPerRow: number = 2): string {
  if (images.length === 0) return '';

  // 计算容器和单个图片的尺寸
  let containerWidth: string;
  let itemHeight: string;

  if (images.length === 1) {
    // 单张图片使用基准尺寸
    containerWidth = `${BASE_IMAGE_DIMENSIONS.width}rem`;
    itemHeight = `${BASE_IMAGE_DIMENSIONS.height}rem`;
    imagesPerRow = 1;
  } else {
    // 多张图片时
    containerWidth = `${BASE_IMAGE_DIMENSIONS.width}rem`;
    itemHeight = `${BASE_IMAGE_DIMENSIONS.height / 2}rem`; // 高度减半
  }

  // 处理图片，添加预览功能
  const processedImages = images.map(imgTag => {
    const imageSrc = extractImageSrc(imgTag);
    return wrapImageWithPreviewButton(imgTag, imageSrc);
  });

  // 使用 Grid 布局
  return `
    <div class="formatted-image-group image-layout-container" style="
      width: ${containerWidth};
      margin: 0 auto 12px;
      display: grid;
      grid-template-columns: repeat(${imagesPerRow}, 1fr);
      gap: 12px;
      padding: 0.01px;
    ">
      ${processedImages.map(img => `
        <div
         class="image-layout-item"
         style="
          width: 100%;
          height: ${itemHeight};
          position: relative;
          background: rgba(0, 0, 0, 0.02);
          border-radius: 0.5rem;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          ${img}
        </div>
      `).join('')}
    </div>
  `;
}

/**
 * 主要处理函数：匹配连续图片并应用布局
 */
export function processConsecutiveImages(htmlContent: string, imagesPerRow: number = 2, enablePreview: boolean = true): string {
  // 匹配连续图片（包括单张）
  return htmlContent.replace(/(<img[^>]*>(\s*<img[^>]*>)*)/g, (match) => {
    const imgTags = match.match(/<img[^>]*>/g) || [];
    if (imgTags.length > 0) {
      return formatImageGroup(imgTags, imagesPerRow);
    }
    return match;
  });
}

/**
 * 主要导出函数
 */
export function applyImageLayout(htmlContent: string, imagesPerRow: number = 2, enablePreview: boolean = true): string {
  return processConsecutiveImages(htmlContent, imagesPerRow, enablePreview);
}

/**
 * 根据配置应用图片处理功能
 */
export function applyImageProcessing(
  htmlContent: string,
  enableLayout: boolean = true,
  enablePreview: boolean = true,
  imagesPerRow: number = 2
): string {
  if (!enableLayout && !enablePreview) {
    return htmlContent;
  }

  if (enableLayout) {
    return processConsecutiveImages(htmlContent, imagesPerRow, enablePreview);
  }

  return htmlContent;
}