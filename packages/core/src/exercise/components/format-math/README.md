# FormatMath 组件

数学公式和图片排版组件，支持智能图片布局和专业图片预览功能。

## 组件概览

FormatMath 组件负责处理包含数学公式和图片的 HTML 内容，提供：
- 数学公式渲染
- 智能图片排版（Grid 布局）
- 专业图片预览功能（基于 react-photo-view）
- 图片预览按钮自动添加

## 特性

- ✅ 数学公式渲染支持
- ✅ 智能图片排版（连续图片自动组织为网格布局）
- ✅ 专业图片预览体验（缩放、旋转、拖拽）
- ✅ 自动添加图片预览按钮
- ✅ 可配置的图片处理选项
- ✅ 响应式设计
- ✅ 完整的 TypeScript 类型支持

## 基础用法

```tsx
import { FormatMath } from "@repo/core/exercise/components/format-math";

function ExerciseContent() {
  const htmlContent = `
    <p>这是一个数学题：</p>
    <p>$$x^2 + y^2 = z^2$$</p>
    <img src="image1.jpg" alt="图片1" />
    <img src="image2.jpg" alt="图片2" />
  `;

  return (
    <FormatMath
      htmlContent={htmlContent}
      questionId="q123"
      className="exercise-content"
    />
  );
}
```

## 高级配置

```tsx
import { FormatMath } from "@repo/core/exercise/components/format-math";

function AdvancedExample() {
  const imageProcessConfig = {
    enableLayout: true,    // 启用图片排版
    enablePreview: true,   // 启用图片预览
    imagesPerRow: 3,      // 每行显示3张图片
  };

  return (
    <FormatMath
      htmlContent={htmlContent}
      questionId="q123"
      imageProcess={imageProcessConfig}
      className="custom-math-content"
    />
  );
}
```

## API 参考

### FormatMath Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| htmlContent | `string` | - | 要渲染的 HTML 内容 |
| questionId | `string` | - | 题目 ID（可选） |
| className | `string` | - | 自定义样式类名 |
| imageProcess | `ImageProcessConfig` | 见下方 | 图片处理配置 |

### ImageProcessConfig

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| enableLayout | `boolean` | `true` | 是否启用图片排版 |
| enablePreview | `boolean` | `true` | 是否启用图片预览 |
| imagesPerRow | `number` | `2` | 每行显示的图片数量 |

## 图片处理功能

### 智能排版
- 自动检测连续的图片标签
- 使用 CSS Grid 布局组织图片
- 单张图片使用完整尺寸
- 多张图片自动调整为网格布局
- 响应式设计，适配不同屏幕尺寸

### 图片预览
- 基于 `react-photo-view` 的专业预览体验
- 支持缩放、旋转、拖拽操作
- 键盘快捷键支持（ESC 关闭）
- 自动添加预览按钮到图片右下角
- 流畅的动画效果

### 预览按钮
- 自动为图片添加预览按钮
- 按钮位置：图片右下角
- 半透明背景，悬停效果
- 防止重复添加（智能检测）

## 全局预览函数

组件会在 window 对象上注册 `previewImage` 函数：

```javascript
// 在任何地方调用图片预览
window.previewImage('image-url.jpg');
```

这个函数被 `wrapImageWithPreviewButton` 使用，确保预览按钮点击时能正确打开预览。

## 样式定制

组件使用语义化的 CSS 类名，方便自定义样式：

```css
/* 主容器 */
.format-math-container { }

/* 图片布局容器 */
.formatted-image-group { }
.image-layout-container { }
.image-layout-item { }

/* 图片预览相关 */
.image-with-preview { }
.image-preview-wrapper { }
.image-preview-btn { }
.image-preview-trigger { }

/* 预览组件样式 */
.format-math-image-preview { }
```

## 与其他组件的关系

### 依赖组件
- `RichTextView`: 负责基础的 HTML 内容渲染
- `ImagePreviewModal`: 提供专业的图片预览功能

### 工具函数
- `image-layout-utils.ts`: 图片排版和预览按钮处理
- `wrapImageWithPreviewButton`: 为图片添加预览按钮（保持不变）

## 注意事项

1. **预览按钮**: `wrapImageWithPreviewButton` 函数保持不变，按照 UI 设计要求
2. **全局函数**: `window.previewImage` 函数会被多个组件实例共享
3. **性能优化**: 使用 useMemo 缓存处理后的 HTML 内容
4. **响应式**: 图片布局会根据容器宽度自动调整
5. **兼容性**: 保持与现有代码的向后兼容性

## 升级说明

### v2.0 更新内容
- ✅ 使用 `react-photo-view` 替代简陋的预览实现
- ✅ 新增专业的图片预览体验
- ✅ 保持 `wrapImageWithPreviewButton` 函数不变
- ✅ 优化预览组件的状态管理
- ✅ 改进键盘导航和用户体验

### 迁移指南
现有代码无需修改，组件 API 保持完全兼容。新的预览功能会自动生效。

## 示例场景

### 数学题目
```tsx
<FormatMath
  htmlContent="解方程：$$x^2 + 2x + 1 = 0$$"
  questionId="math-001"
/>
```

### 图文混合内容
```tsx
<FormatMath
  htmlContent={`
    <p>观察下面的图形：</p>
    <img src="triangle.jpg" alt="三角形" />
    <img src="square.jpg" alt="正方形" />
    <p>计算面积：$$S = \\frac{1}{2}bh$$</p>
  `}
  imageProcess={{ imagesPerRow: 2 }}
/>
```

### 禁用图片功能
```tsx
<FormatMath
  htmlContent={htmlContent}
  imageProcess={{
    enableLayout: false,
    enablePreview: false,
  }}
/>
```
