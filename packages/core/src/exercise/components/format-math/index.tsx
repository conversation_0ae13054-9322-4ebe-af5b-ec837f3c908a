import { signal } from "@preact-signals/safe-react";
import { useEffect } from "@preact-signals/safe-react/react";
import RichTextView from "@repo/core/components/rich-text-view";
import { cn } from "@repo/ui/lib/utils";
import { useMemo } from "react";
import { ImagePreviewModal } from "../../../components/image-preview";
import { applyImageProcessing } from "./image-layout-utils";
export interface ImageProcessConfig {
  enableLayout?: boolean; // 是否启用图片排版
  enablePreview?: boolean; // 是否启用图片预览
  imagesPerRow?: number; // 每行显示的图片数量，默认为2
}

export interface FormatMathProps {
  htmlContent: string;
  questionId?: string;
  ossHost?: string;
  resourceApiHost?: string;
  className?: string;
  imageProcess?: ImageProcessConfig; // 图片处理配置
}

export interface ImageGroup {
  images: string[];
  beforeText: string;
  afterText: string;
}

// 扩展 Window 接口
declare global {
  interface Window {
    previewImage?: (imageSrc: string) => void;
    currentPreviewImage?: string;
  }
}

const previewImageSrc = signal<string | null>(null);

export const FormatMath = ({
  htmlContent,
  questionId,
  className,
  imageProcess = {
    enableLayout: true,
    enablePreview: true,
    imagesPerRow: 2,
  },
}: FormatMathProps) => {
  const ossHost = process.env.NEXT_PUBLIC_OSS_HOST;
  const resourceApiHost = process.env.NEXT_PUBLIC_RESOURCE_API_HOST;

  // 使用 signal 管理图片预览状态
  const closePreview = () => {
    previewImageSrc.value = null;
  };

  // 处理图片排版和预览
  const processedHtmlContent = useMemo(() => {
    try {
      // 使用新的统一处理函数，支持独立控制布局和预览功能
      return applyImageProcessing(
        htmlContent,
        imageProcess.enableLayout ?? false,
        imageProcess.enablePreview ?? false,
        imageProcess.imagesPerRow ?? 2
      );
    } catch (error) {
      console.warn("图片处理失败，使用原始内容:", error);
      return htmlContent;
    }
  }, [
    htmlContent,
    imageProcess.enableLayout,
    imageProcess.enablePreview,
    imageProcess.imagesPerRow,
  ]);

  useEffect(() => {
    const handleClick = (e: Event) => {
      const btn = (e.target as HTMLElement).closest(
        ".image-preview-btn"
      ) as HTMLElement;

      const src = btn?.dataset.imageSrc;
      if (src) {
        previewImageSrc.value = src;
        console.log("previewImageSrc", previewImageSrc.value);
      }
    };

    document.addEventListener("click", handleClick, true);
    return () => document.removeEventListener("click", handleClick, true);
  }, []);

  return (
    <>
      <div
        style={{ display: "inline-block" }}
        className={cn("format-math-container leading-[190%]", className)}
      >
        <RichTextView
          htmlContent={processedHtmlContent}
          questionId={questionId}
          ossHost={ossHost}
          resourceApiHost={resourceApiHost}
          className={cn(
            "w-full !leading-[190%] [&_.katex]:px-1 [&_.katex]:!text-[1em] [&_.katex_.base]:my-1 [&_p]:mb-2 [&_u]:!whitespace-pre-wrap"
          )}
        />
      </div>

      {/* 使用简化的图片预览组件 */}
      {previewImageSrc.value && (
        <ImagePreviewModal
          currentImage={previewImageSrc.value}
          onClose={closePreview}
          className="format-math-image-preview"
        />
      )}
    </>
  );
};

// 保持向后兼容性的默认导出
export default FormatMath;
