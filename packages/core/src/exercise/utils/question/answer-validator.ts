import { ImageFile } from "@repo/core/exercise/type";
import { QUESTION_TYPE } from "@repo/core/enums";

// 用户答案数据类型（从 question-context 中提取）
interface UserAnswerData {
  // 选择题、判断题
  choiceAnswers?: string[];
  // 填空题、问答题
  subjectiveAnswer?: string[];
  // 英语填空题
  englishFillBlankAnswers?: string[];
  // 解答方式
  inputMode?: "keyboard" | "camera";
  // 图片
  imgFiles?: ImageFile[];
}

/**
 * 答案完成状态验证器配置
 * 新增题型只需要在这里添加对应的验证逻辑
 */
const answerValidators = {
  [QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE]: (data: UserAnswerData) =>
    data.choiceAnswers?.length === 1,

  [QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE]: (/* data: UserAnswerData */) =>
    // 多选题允许选择任意数量的选项（包括0个、1个或多个）
    true,

  [QUESTION_TYPE.QUESTION_TYPE_JUDGMENT]: (data: UserAnswerData) =>
    data.choiceAnswers?.length === 1,

  [QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK]: (data: UserAnswerData) => {
    
    if (data.inputMode === "keyboard") {
      const isComplete = data.subjectiveAnswer?.every((answer) => answer.trim() !== "") || false;
      console.log('[FILL_BLANK Validator] 键盘输入模式，答案完整性:', isComplete);
      return isComplete;
    } else {
      const isComplete = (data.imgFiles && data.imgFiles.length > 0) || false;
      console.log('[FILL_BLANK Validator] 图片输入模式，答案完整性:', isComplete);
      return isComplete;
    }
  },

  [QUESTION_TYPE.QUESTION_TYPE_QA]: (data: UserAnswerData) => {
    if (data.inputMode === "keyboard") {
      return (data.subjectiveAnswer?.length || 0) > 0 || false;
    } else {
      return (data.imgFiles && data.imgFiles.length > 0) || false;
    }
  },

  [QUESTION_TYPE.QUESTION_TYPE_CLOZE]: (data: UserAnswerData) =>
    data.englishFillBlankAnswers?.every((answer) => answer.trim() !== "") ||
    false,

  [QUESTION_TYPE.QUESTION_TYPE_PARENT_CHILD]: (/* data: UserAnswerData */) =>
    false,
};

/**
 * 检查答案是否完成
 * @param data 用户答案数据
 * @param type 题目类型
 * @param isGivingUp 是否放弃作答
 * @returns 是否完成
 */
export const checkAnswerComplete = (
  data: UserAnswerData,
  type: QUESTION_TYPE,
  isGivingUp: boolean
): boolean => {
  // 放弃作答也算完成
  if (isGivingUp) {
    return true;
  }

  const validator = answerValidators[type];
  return validator ? validator(data) : false;
};

// 导出类型供其他文件使用
export type { UserAnswerData };
