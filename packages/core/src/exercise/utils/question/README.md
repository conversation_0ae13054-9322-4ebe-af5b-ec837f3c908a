# Question 工具函数库

题目相关的工具函数和业务逻辑处理。

## 文件结构

```
question/
├── answer-builder.ts          # 答案构建器
├── answer-validator.ts        # 答案验证器  
├── question-viewmodel-utils.ts # QuestionViewModel 相关工具函数
├── index.ts                   # 统一导出
└── README.md                  # 说明文档
```

## 工具函数

### answer-builder.ts
- `buildQuestionAnswer()` - 构建题目答案数据
- `createEmptyAnswer()` - 创建空答案

### answer-validator.ts  
- `checkAnswerComplete()` - 检查答案是否完整
- `UserAnswerData` - 用户答案数据类型

### question-viewmodel-utils.ts
- `getStudyTypeFromUrl()` - 从URL获取学习类型
- `buildQuestionAnswerFromUserData()` - 从用户数据构建答案
- `isChoiceQuestionType()` - 判断是否为选择题类型
- `isValidQuestionData()` - 验证题目数据有效性

## 使用示例

### 基础工具函数
```typescript
import { 
  checkAnswerComplete, 
  buildQuestionAnswer,
  getStudyTypeFromUrl,
  isChoiceQuestionType 
} from '../../utils/question';

// 检查答案完整性
const isComplete = checkAnswerComplete(userAnswer, questionType);

// 构建答案数据
const answer = buildQuestionAnswer(userAnswer, questionType);

// 获取学习类型
const studyType = getStudyTypeFromUrl();

// 判断题目类型
const isChoice = isChoiceQuestionType(QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE);
```

### ViewModel 工具函数
```typescript
import { 
  buildQuestionAnswerFromUserData,
  isValidQuestionData 
} from '../../utils/question';

// 转换题目数据
const question = nextQuestionInfo;

// 构建用户答案
const answer = buildQuestionAnswerFromUserData(userInput, questionType);

// 验证数据有效性
const isValid = isValidQuestionData(questionData);
```

## 设计原则

1. **单一职责** - 每个函数只负责一个特定功能
2. **纯函数** - 避免副作用，便于测试和复用
3. **类型安全** - 严格的 TypeScript 类型定义
4. **统一导出** - 通过 index.ts 统一管理导出

## 维护规范

- 新增工具函数时需要更新 index.ts 的导出
- 添加完整的 TypeScript 类型定义
- 提供清晰的 JSDoc 注释
- 遵循项目的命名规范