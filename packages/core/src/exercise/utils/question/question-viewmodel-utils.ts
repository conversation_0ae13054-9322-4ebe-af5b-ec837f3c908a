import { QUESTION_TYPE, StudyType } from '@repo/core/enums';
import { SUBJECT, subjectEnumManager } from '@repo/core/enums/lesson';
import { UserAnswerData } from '../../model';
import { PreviewQuestionInfo, StudentAnswerItem } from '../../model/types';

// getSubjectIdFromUrl
export function getSubjectFromUrl(): { isEnglish: boolean, subjectId: number | null, subjectName: string | undefined } {
  if (typeof window === 'undefined') return {
    isEnglish: false,
    subjectId: null,
    subjectName: undefined,
  };
  const urlParams = new URLSearchParams(window.location.search);

  const subjectIdParam = urlParams.get("subjectId");

  // 正确处理类型转换：字符串转数字，并验证是否为有效的学科ID
  const subjectId = subjectIdParam ? parseInt(subjectIdParam, 10) : null;

  // 验证 subjectId 是否为有效的 SUBJECT 枚举值
  const isValidSubjectId =
    subjectId && Object.values(SUBJECT).includes(subjectId);

  const isEnglish = isValidSubjectId
    ? subjectEnumManager.getLabelByValue(subjectId as SUBJECT) === "英语"
    : false;

  return { isEnglish, subjectId, subjectName: subjectEnumManager.getLabelByValue(subjectId as SUBJECT) };
}
/**
 * 工具函数：从URL参数中提取studyType
 */
export function getStudyTypeFromUrl(): StudyType | undefined {
  if (typeof window === 'undefined') return undefined;

  const urlParams = new URLSearchParams(window.location.search);
  const studyTypeParam = urlParams.get('studyType');

  if (studyTypeParam) {
    const studyTypeNum = Number(studyTypeParam);
    if (Object.values(StudyType).includes(studyTypeNum as StudyType)) {
      return studyTypeNum as StudyType;
    }
  }

  // 提供默认值：巩固练习
  return StudyType.REINFORCEMENT_EXERCISE;
}

/**
 * 根据用户答案数据构建新的 studentAnswers 格式
 */
export function buildQuestionAnswerFromUserData(
  userAnswerData: UserAnswerData | undefined,
  questionType: QUESTION_TYPE,
  questionId: string
): StudentAnswerItem[] {
  if (!userAnswerData) {
    return [];
  }

  switch (questionType) {
    case QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE:
      // 单选题：只有一个选项，index 为 0
      const singleOption = userAnswerData.choiceAnswers?.[0];
      if (!singleOption) return [];

      return [{
        questionId: questionId,
        content: [singleOption],
        index: 0,
        type: 1, // 文本类型
      }];

    case QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE:
      // 多选题：多个选项，index 为 0（因为只有一个"空"）
      const multipleOptions = userAnswerData.choiceAnswers || [];
      if (multipleOptions.length === 0) return [];

      return [{
        questionId: questionId,
        content: multipleOptions,
        index: 0,
        type: 1, // 文本类型
      }];

    case QUESTION_TYPE.QUESTION_TYPE_JUDGMENT:
      // 判断题：true/false，index 为 0
      if (userAnswerData.trueFalseAnswer === undefined) return [];

      const judgmentAnswer = userAnswerData.trueFalseAnswer ? 'true' : 'false';
      return [{
        questionId: questionId,
        content: [judgmentAnswer],
        index: 0,
        type: 1, // 文本类型
      }];

    case QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK:
      // 填空题：每个空对应一个 StudentAnswerItem，index 从 0 开始
      const subjectiveAnswer = userAnswerData.subjectiveAnswer || [];

      const imageAnswerList = userAnswerData.imgFiles?.length ? userAnswerData.imgFiles : [];
      // 处理填空题（统一处理单空和多空情况）
      return subjectiveAnswer.map((answer: string, index: number) => ({
        questionId: questionId,
        content: (userAnswerData.imgFiles?.length ?? 0) > 0 
        ? imageAnswerList.map(img => img) 
        : [answer],
        index: index,
        type: userAnswerData.inputMode === 'camera' ? 2 : 1, // 文本类型，注意：如果是图片填空需要根据实际情况调整 type
      }));

    case QUESTION_TYPE.QUESTION_TYPE_QA:
      // 问答题：只有一个答案，index 为 0
      const subjectiveAnswerData = userAnswerData.imgFiles?.length ? userAnswerData.imgFiles : userAnswerData.subjectiveAnswer || [];
      if (!subjectiveAnswerData.length) return [];

      return [{
        questionId: questionId,
        content: subjectiveAnswerData,
        index: 0,
        type: userAnswerData.inputMode === 'camera' ? 2 : 1, // 文本类型，注意：如果是图片填空需要根据实际情况调整 type
      }];

    default:
      console.warn(`[buildQuestionAnswerFromUserData] Unknown question type: ${questionType}`);
      return [{
        questionId: questionId,
        content: [''],
        index: 0,
        type: 1, // 文本类型
      }];
  }
}

/**
 * 检查是否为选择题类型
 */
export function isChoiceQuestionType(questionType: QUESTION_TYPE): boolean {
  return questionType === QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE ||
    questionType === QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE;
}

/**
 * 检查题目数据的有效性
 */
export function isValidQuestionData(questionData: PreviewQuestionInfo | null | undefined): boolean {
  const isValid = !!questionData?.questionId;

  if (!isValid) {
    console.warn('[QuestionUtils] 数据验证失败:', {
      questionData,
      hasQuestionId: !!questionData?.questionId,
      questionIdValue: questionData?.questionId
    });
  }

  return isValid;
}

/**
 * 检查是否为主观题类型
 * 主观题：需要文字/图片输入，包括填空题、完形填空题、问答题
 */
export function isSubjectiveQuestionType(questionType: QUESTION_TYPE): boolean {
  return questionType === QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK ||
    questionType === QUESTION_TYPE.QUESTION_TYPE_QA;
}

/**
 * 检查是否为客观题类型  
 * 客观题：选择答案，系统自动判题，包括单选题、多选题、判断题
 */
export function isObjectiveQuestionType(questionType?: QUESTION_TYPE): boolean {
  return questionType === QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE ||
    questionType === QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE ||
    questionType === QUESTION_TYPE.QUESTION_TYPE_JUDGMENT;
}

/**
 * 检查是否为系统判题类型
 * 系统判题：客观题 + 完形填空题
 */
export function isSystemGradedQuestionType(questionType: QUESTION_TYPE): boolean {
  return isObjectiveQuestionType(questionType) ||
    questionType === QUESTION_TYPE.QUESTION_TYPE_CLOZE;
}

/**
 * 检查是否为自评类型
 * 自评类型：填空题 + 问答题，需要学生提交自评结果
 */
export function isSelfEvaluationQuestionType(questionType: QUESTION_TYPE): boolean {
  return questionType === QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK ||
    questionType === QUESTION_TYPE.QUESTION_TYPE_QA;
}

/**
 * 检查是否需要提交确认弹窗
 * 主观题通常需要确认弹窗，防止误操作
 * 完形填空题和主观题类型需要确认弹窗，防止误操作
 */
export function needsSubmitConfirmation(questionType: QUESTION_TYPE): boolean {
  return isSubjectiveQuestionType(questionType) || questionType === QUESTION_TYPE.QUESTION_TYPE_CLOZE;
}

/**
 * 检查是否为多选题且需要确认弹窗
 * 多选题只选一个选项时需要确认
 */
export function isMultipleChoiceNeedingConfirmation(
  questionType: QUESTION_TYPE,
  selectedOptionsCount: number
): boolean {
  return questionType === QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE &&
    selectedOptionsCount === 1;
}

// 当前支持的题型
export const isSupportQuestionType = (questionType: QUESTION_TYPE): boolean => {
  return [
    QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE,
    QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE,
    QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK,
    QUESTION_TYPE.QUESTION_TYPE_QA,
  ].includes(questionType);
};