/**
 * 解析填空题内容，返回二维数组
 * @param content 填空题内容字符串
 * @returns 二维数组，第一层区分段落，第二层包含文本和空白标记
 */
export function parseContent(content: string): (string | null)[][] {
  // 创建临时 DOM 元素来解析 HTML
  const div =
    typeof document !== "undefined" ? document.createElement("div") : null;
  if (!div) return [];
  div.innerHTML = content;

  const result: (string | null)[][] = [];

  // 递归处理节点，返回 (string | null)[]
  function processNode(node: Node): (string | null)[] {
    if (node.nodeType === Node.TEXT_NODE) {
      return node.textContent ? [node.textContent] : [];
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element;

      // 检查是否是普通的填空标记
      if (
        element.tagName.toLowerCase() === "span" &&
        element.getAttribute("data-tiptype") === "question-blank_filling"
      ) {
        // 普通的填空标记，返回null
        return [null];
      }

      // 递归处理子节点，拼接为 innerHTML
      let inner = "";
      for (const child of Array.from(element.childNodes)) {
        const childParts = processNode(child);
        childParts.forEach((part) => {
          if (part === null) {
            inner += "[[[BLANK]]]"; // 普通填空标记的占位符
          } else {
            inner += part;
          }
        });
      }

      // 保留标签和属性
      let html = element.outerHTML;

      // 用 inner 替换 element.innerHTML
      html = html.replace(element.innerHTML, inner);

      // 拆分 [[[BLANK]]] 为 null，其余为 html 字符串
      const parts: (string | null)[] = [];
      const splitArr = html.split("[[[BLANK]]]");

      for (let i = 0; i < splitArr.length; i++) {
        if (typeof splitArr[i] === "string" && splitArr[i]) {
          parts.push(splitArr[i] as string);
        }

        // 添加普通填空标记
        if (i < splitArr.length - 1) {
          parts.push(null);
        }
      }

      return parts;
    }
    return [];
  }

  // 处理所有顶级元素
  function processTopLevelElements() {
    // 确保div不为null
    if (!div) return;

    // 获取所有顶级元素
    const topElements = Array.from(div.childNodes);

    // 当前段落的内容
    let currentParagraph: (string | null)[] = [];

    for (const element of topElements) {
      if (element.nodeType === Node.ELEMENT_NODE) {
        const el = element as Element;
        const tagName = el.tagName.toLowerCase();

        if (tagName === "p") {
          // 遇到 p 标签，先保存当前段落（如果有内容）
          if (currentParagraph.length > 0) {
            result.push(currentParagraph);
            currentParagraph = [];
          }

          // 处理 p 标签内的内容作为新段落
          const parts: (string | null)[] = [];
          for (const node of Array.from(el.childNodes)) {
            if (node) {
              parts.push(...processNode(node));
            }
          }
          if (parts.length > 0) {
            result.push(parts);
          }
        } else if (tagName === "br") {
          // 遇到 br 标签，结束当前段落并开始新段落
          if (currentParagraph.length > 0) {
            result.push(currentParagraph);
            currentParagraph = [];
          }

          
        } else {
          // 其他所有元素（sub、img、span、div、table等）都添加到当前段落中，不换行
          // 即使它们被当作顶级元素处理，也要保持在当前段落中
          const parts = processNode(el);
          currentParagraph.push(...parts);
        }
      } else if (
        element.nodeType === Node.TEXT_NODE &&
        element.textContent?.trim()
      ) {
        // 处理顶级文本节点，添加到当前段落
        currentParagraph.push(element.textContent);
      }
    }

    // 处理最后一个段落
    if (currentParagraph.length > 0) {
      result.push(currentParagraph);
    }
  }

  // 开始处理
  processTopLevelElements();

  // 如果没有找到任何内容，将整个内容作为一个段落
  if (result.length === 0) {
    result.push([content]);
  }

  return result;
}

/**
 * 测试函数：验证解析行为
 */
export function testParseContent() {
  const testContent = `CO<sub>2</sub>的捕获和转化可减少CO<sub>2</sub>排放并实现资源的利用。在催化剂作用下，消耗CH<sub>4</sub>和CO<sub>2</sub>，生成合成气(H<sub>2</sub>、CO)，主要发生反应i，可能发生副反应ii、iii：<br>i．CH<sub>4</sub>(g)＋CO<sub>2</sub>(g)<img src="https://static.test.xiaoluxue.cn/question/paper-parsing/4c243903-74c6-4cd3-8e96-33c7fc8274c7/wmf_0877944f7cd5bb8a4b5088954d418c36.webp" width="" height=""/>2CO(g)＋2H<sub>2</sub>(g)  ΔH<sub>1</sub><br>ii．CH<sub>4</sub>(g)=C(s)＋2H<sub>2</sub>(g)  ΔH<sub>2</sub>=＋75.0 kJ·mol<sup>-1</sup><br>iii．2CO(g)=CO<sub>2</sub>(g)＋C(s)  ΔH<sub>3</sub>=－172.0 kJ·mol<sup>-1</sup><br>ΔH<sub>1</sub>=<span data-tiptype="question-blank_filling"></span>。`;

  const result = parseContent(testContent);
  console.log("解析结果:", result);
  console.log("段落数量:", result.length);

  return result;
}

/**
 * 调试函数：查看DOM结构
 */
export function debugDOMStructure(content: string) {
  const div =
    typeof document !== "undefined" ? document.createElement("div") : null;
  if (!div) return;

  div.innerHTML = content;

  console.log("=== DOM结构分析 ===");
  console.log("原始内容:", content);
  console.log("顶级节点数量:", div.childNodes.length);

  Array.from(div.childNodes).forEach((node, index) => {
    console.log(`节点 ${index}:`, {
      nodeType: node.nodeType,
      nodeName: node.nodeName,
      textContent: node.textContent?.substring(0, 50) + "...",
      tagName:
        node.nodeType === Node.ELEMENT_NODE ? (node as Element).tagName : null,
      outerHTML:
        node.nodeType === Node.ELEMENT_NODE
          ? (node as Element).outerHTML.substring(0, 100) + "..."
          : null,
    });
  });

  return div;
}

/**
 * 详细调试解析过程
 */
export function debugParseProcess(content: string) {
  const div =
    typeof document !== "undefined" ? document.createElement("div") : null;
  if (!div) return;

  div.innerHTML = content;

  console.log("=== 详细解析过程 ===");
  console.log("原始内容:", content);

  const topElements = Array.from(div.childNodes);
  console.log("顶级元素数量:", topElements.length);

  topElements.forEach((element, index) => {
    console.log(`\n处理顶级元素 ${index}:`);
    console.log("节点类型:", element.nodeType);
    console.log("节点名称:", element.nodeName);

    if (element.nodeType === Node.ELEMENT_NODE) {
      const el = element as Element;
      const tagName = el.tagName.toLowerCase();
      console.log("标签名:", tagName);
      console.log("完整HTML:", el.outerHTML);

      if (tagName === "p") {
        console.log("→ 这是p标签，会创建新段落");
      } else if (tagName === "br") {
        console.log("→ 这是br标签，会创建新段落");
      } else {
        console.log("→ 这是其他标签，会添加到当前段落");
      }
    } else if (element.nodeType === Node.TEXT_NODE) {
      console.log("文本内容:", element.textContent);
      console.log("→ 这是文本节点，会添加到当前段落");
    }
  });

  return div;
}

/**
 * 简单测试用例
 */
export function simpleTest() {
  const simpleContent = `这是第一段<sub>2</sub>内容。<br>这是第二段<img src="test.jpg"/>内容。<span data-tiptype="question-blank_filling"></span>`;

  // 先调试DOM结构
  debugDOMStructure(simpleContent);

  const result = parseContent(simpleContent);
  console.log("简单测试结果:", result);
  console.log("段落数量:", result.length);

  return result;
}

/**
 * 测试sub标签处理
 */
export function testSubTag() {
  const testContent = `CO<sub>2</sub>的捕获和转化可减少CO<sub>2</sub>排放。`;

  console.log("=== 测试sub标签 ===");
  console.log("原始内容:", testContent);

  // 调试DOM结构
  debugDOMStructure(testContent);

  const result = parseContent(testContent);
  console.log("解析结果:", result);
  console.log("段落数量:", result.length);

  return result;
}
