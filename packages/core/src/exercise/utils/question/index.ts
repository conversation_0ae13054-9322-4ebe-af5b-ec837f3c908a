/**
 * 题目相关工具函数统一导出
 * 
 * 使用示例：
 * import { checkAnswerComplete, UserAnswerData } from '../../utils/question';
 * import { buildQuestionAnswer, createEmptyAnswer } from '../../utils/question/answer-builder';
 * import { getStudyTypeFromUrl } from '../../utils/question';
 */

export { checkAnswerComplete, type UserAnswerData } from './answer-validator';
export { createEmptyAnswer } from './answer-builder';
export {
  getStudyTypeFromUrl,
  buildQuestionAnswerFromUserData,
  isChoiceQuestionType,
  isValidQuestionData,
  isSelfEvaluationQuestionType
} from './question-viewmodel-utils';