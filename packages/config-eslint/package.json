{"name": "@repo/config-eslint", "version": "0.0.0", "private": true, "exports": {"./base": "./base.mjs", "./next-js": "./next-js.mjs", "./react-internal": "./react-internal.mjs"}, "devDependencies": {"@eslint/js": "^9.22.0", "@next/eslint-plugin-next": "^15.2.1", "@types/eslint": "^9.6.1", "eslint-config-prettier": "^10.1.1", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^6.0.0-rc.1", "eslint-plugin-turbo": "^2.4.4", "globals": "^16.0.0", "typescript": "^5.8.3", "typescript-eslint": "^8.26.0"}}