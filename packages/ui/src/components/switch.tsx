"use client";

import * as SwitchPrimitive from "@radix-ui/react-switch";
import * as React from "react";

import { cn } from "@repo/ui/lib/utils";

function Switch({
  className,
  ...props
}: React.ComponentProps<typeof SwitchPrimitive.Root>) {
  return (
    <SwitchPrimitive.Root
      data-slot="switch"
      className={cn(
        "bg bg-blackA6 relative h-[24px] w-[42px] cursor-default rounded-full outline-none data-[state=checked]:bg-black",
        className
      )}
      {...props}
    >
      <SwitchPrimitive.Thumb
        data-slot="switch-thumb"
        className={cn(
          "block size-[18px] translate-x-1 rounded-full bg-white transition-transform duration-200 will-change-transform data-[state=checked]:translate-x-[20px]"
        )}
      />
    </SwitchPrimitive.Root>
  );
}

export { Switch };
